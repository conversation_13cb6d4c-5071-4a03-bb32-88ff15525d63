{"示例代码": {"反调试检测示例": "int main() {\n    // 初始化反调试组件\n    AntiDebugger debugDetector;\n    \n    // 注册多种反调试技术\n    debugDetector.registerDetectionMethod(AntiDebugger::PTRACE_CHECK);\n    debugDetector.registerDetectionMethod(AntiDebugger::PARENT_PROCESS_CHECK);\n    debugDetector.registerDetectionMethod(AntiDebugger::DEBUG_REGISTERS_CHECK);\n    \n    // 执行反调试检测\n    bool isBeingDebugged = debugDetector.checkDebuggerPresence();\n    \n    if (isBeingDebugged) {\n        // 检测到调试器，执行自毁操作\n        debugDetector.selfDestruct(DESTROY_LEVEL_COMPLETE);\n        return -1;\n    }\n    \n    // 正常程序逻辑\n    performMainOperation();\n    \n    return 0;\n}"}, "API接口": {"registerDetectionMethod": "功能: 注册一种反调试检测方法\n      参数:\n        - 名称: methodType\n          类型: DetectionMethodType\n          描述: 要注册的检测方法类型\n      返回值:\n        类型: bool\n        描述: 是否成功注册检测方法\n      示例: debugDetector.registerDetectionMethod(AntiDebugger::PTRACE_CHECK);", "checkDebuggerPresence": "功能: 检测是否存在调试器\n      参数:\n      返回值:\n        类型: bool\n        描述: 如果检测到调试器存在则返回true，否则返回false\n      示例: bool isBeingDebugged = debugDetector.checkDebuggerPresence();", "selfDestruct": "功能: 执行自毁操作，清除程序及相关组件\n      参数:\n        - 名称: level\n          类型: DestructionLevel\n          描述: 自毁操作的级别，如仅删除自身、删除所有组件等\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: debugDetector.selfDestruct(DESTROY_LEVEL_COMPLETE);", "addComponentForRemoval": "功能: 添加需要在自毁时一并删除的组件路径\n      参数:\n        - 名称: componentPath\n          类型: const char*\n          描述: 组件文件的完整路径\n      返回值:\n        类型: bool\n        描述: 是否成功添加组件\n      示例: debugDetector.addComponentForRemoval(\"/tmp/component.so\");"}, "数据结构": {"DetectionMethodType": "描述: 反调试检测方法类型枚举\n      字段:\n        - 名称: PTRACE_CHECK\n          类型: enum\n          描述: 使用ptrace系统调用检测调试器\n        - 名称: PARENT_PROCESS_CHECK\n          类型: enum\n          描述: 检查父进程是否为调试器\n        - 名称: DEBUG_REGISTERS_CHECK\n          类型: enum\n          描述: 检查调试寄存器状态\n        - 名称: TIMING_CHECK\n          类型: enum\n          描述: 通过时间差异检测调试器\n        - 名称: SIGNAL_HANDLER_CHECK\n          类型: enum\n          描述: 通过信号处理检测调试器", "DestructionLevel": "描述: 自毁操作的级别枚举\n      字段:\n        - 名称: DESTROY_LEVEL_SELF\n          类型: enum\n          描述: 仅删除程序自身\n        - 名称: DESTROY_LEVEL_COMPONENTS\n          类型: enum\n          描述: 删除自身和已注册的组件\n        - 名称: DESTROY_LEVEL_COMPLETE\n          类型: enum\n          描述: 完全清除所有痕迹，包括日志等\n        - 名称: DESTROY_LEVEL_GRACEFUL\n          类型: enum\n          描述: 在正常退出前执行清理"}}