{"示例代码": {"受控端替换示例": "int main() {\n    // 初始化更新替换管理器\n    UpdateReplacer replacer;\n    \n    // 准备进行热更新替换\n    const char* updateFilePath = \"/opt/client/updates/update_1624512345.bin\";\n    const char* currentExecutablePath = \"/opt/client/program/client\";\n    \n    // 验证更新文件\n    if (!replacer.ValidateUpdateFile(updateFilePath)) {\n        printf(\"更新文件验证失败\\n\");\n        return 1;\n    }\n    \n    // 备份当前可执行文件\n    char backupPath[256];\n    snprintf(backupPath, sizeof(backupPath), \"%s.backup.%lu\", currentExecutablePath, time(NULL));\n    \n    if (!replacer.BackupCurrentExecutable(currentExecutablePath, backupPath)) {\n        printf(\"备份当前可执行文件失败\\n\");\n        return 1;\n    }\n    \n    printf(\"正在替换旧版本程序...\\n\");\n    \n    // 执行热更新替换\n    REPLACEMENT_RESULT result;\n    if (replacer.HotReplaceExecutable(updateFilePath, currentExecutablePath, &result)) {\n        printf(\"热更新替换成功\\n\");\n        printf(\"旧版本: %s\\n\", result.oldVersion);\n        printf(\"新版本: %s\\n\", result.newVersion);\n        printf(\"备份路径: %s\\n\", result.backupPath);\n        \n        // 更新版本记录\n        replacer.UpdateVersionRecord(result.newVersion);\n    } else {\n        printf(\"热更新替换失败: %s\\n\", result.errorMessage);\n        // 恢复备份\n        if (replacer.RestoreFromBackup(backupPath, currentExecutablePath)) {\n            printf(\"已从备份恢复\\n\");\n        }\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"ValidateUpdateFile": "功能: 验证更新文件的完整性和兼容性\n      参数:\n        - 名称: updateFilePath\n          类型: const char*\n          描述: 更新文件路径\n      返回值:\n        类型: bool\n        描述: 更新文件是否验证通过\n      示例: replacer.ValidateUpdateFile(updateFilePath)", "BackupCurrentExecutable": "功能: 备份当前可执行文件\n      参数:\n        - 名称: sourcePath\n          类型: const char*\n          描述: 当前可执行文件路径\n        - 名称: backupPath\n          类型: const char*\n          描述: 备份文件保存路径\n      返回值:\n        类型: bool\n        描述: 备份是否成功\n      示例: replacer.BackupCurrentExecutable(currentExecutablePath, backupPath)", "HotReplaceExecutable": "功能: 热替换可执行文件\n      参数:\n        - 名称: updateFilePath\n          类型: const char*\n          描述: 更新文件路径\n        - 名称: targetPath\n          类型: const char*\n          描述: 目标可执行文件路径\n        - 名称: result\n          类型: REPLACEMENT_RESULT*\n          描述: 替换结果结构体指针\n      返回值:\n        类型: bool\n        描述: 替换是否成功\n      示例: replacer.HotReplaceExecutable(updateFilePath, currentExecutablePath, &result)", "RestoreFromBackup": "功能: 从备份恢复可执行文件\n      参数:\n        - 名称: backupPath\n          类型: const char*\n          描述: 备份文件路径\n        - 名称: targetPath\n          类型: const char*\n          描述: 目标可执行文件路径\n      返回值:\n        类型: bool\n        描述: 恢复是否成功\n      示例: replacer.RestoreFromBackup(backupPath, currentExecutablePath)", "UpdateVersionRecord": "功能: 更新版本记录\n      参数:\n        - 名称: version\n          类型: const char*\n          描述: 新版本号\n      返回值:\n        类型: bool\n        描述: 版本记录是否更新成功\n      示例: replacer.UpdateVersionRecord(result.newVersion)"}, "数据结构": {"REPLACEMENT_RESULT": "描述: 替换结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 替换是否成功\n        - 名称: oldVersion\n          类型: char[32]\n          描述: 旧版本号\n        - 名称: newVersion\n          类型: char[32]\n          描述: 新版本号\n        - 名称: backupPath\n          类型: char[256]\n          描述: 备份文件路径\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 替换时间戳\n        - 名称: errorMessage\n          类型: char[256]\n          描述: 错误信息(如果替换失败)", "VERSION_INFO": "描述: 版本信息结构体\n      字段:\n        - 名称: versionString\n          类型: char[32]\n          描述: 版本号字符串\n        - 名称: versionMajor\n          类型: uint16_t\n          描述: 主版本号\n        - 名称: versionMinor\n          类型: uint16_t\n          描述: 次版本号\n        - 名称: versionPatch\n          类型: uint16_t\n          描述: 补丁版本号\n        - 名称: buildTimeStamp\n          类型: uint64_t\n          描述: 构建时间戳"}}