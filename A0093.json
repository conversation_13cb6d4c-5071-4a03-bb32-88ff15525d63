{"示例代码": {"说明": "暂无"}, "API接口": {"ClientInitiateConnection": "功能: 受控端向控制端发起连接请求并发送设备信息。\n      参数:\n        - 名称: server_address\n          类型: const char*\n          描述: 控制端服务器地址。\n        - 名称: server_port\n          类型: unsigned short\n          描述: 控制端服务器端口。\n        - 名称: device_info\n          类型: const DeviceFingerprint*\n          描述: 包含设备指纹信息的结构体指针。\n        - 名称: session_key_out\n          类型: unsigned char**\n          描述: 用于接收建立的加密会话密钥的输出参数。\n      返回值:\n        类型: int\n        描述: 连接状态，0表示成功建立会话，非0表示失败。\n      示例: DeviceFingerprint deviceInfo = {...}; unsigned char* sessionKey = NULL; if (ClientInitiateConnection(\"c2.example.com\", 443, &deviceInfo, &sessionKey) == 0) { /* 会话建立成功 */ }"}, "数据结构": {"DeviceFingerprint": "描述: 存储设备唯一标识和特征信息，用于控制端验证。\n      字段:\n        - 名称: device_id\n          类型: char[128]\n          描述: 设备的唯一ID (例如MAC地址、硬件序列号组合)。\n        - 名称: os_version\n          类型: char[64]\n          描述: 操作系统版本。\n        - 名称: hostname\n          类型: char[64]\n          描述: 设备主机名。\n        - 名称: public_key_modulus\n          类型: unsigned char[256]\n          描述: 设备用于加密通信的公钥模数 (示例)。\n        - 名称: public_key_exponent\n          类型: unsigned char[4]\n          描述: 设备用于加密通信的公钥指数 (示例)。"}}