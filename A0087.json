{"示例代码": {"代码自篡改示例": "int main() {\n    // 初始化代码自篡改模块\n    CodeSelfModifier modifier;\n    \n    // 配置自篡改参数\n    SelfModificationConfig config;\n    config.enableCodeMutation = true;\n    config.enableInstructionSubstitution = true;\n    config.enableJumpObfuscation = true;\n    config.mutationFrequency = 5; // 每执行5次关键函数变换一次代码\n    config.preserveFunctionality = true;\n    \n    // 初始化修改器\n    if (!modifier.Initialize(config)) {\n        fprintf(stderr, \"无法初始化代码自篡改模块\\n\");\n        return 1;\n    }\n    \n    printf(\"代码自篡改模块已启动\\n\");\n    \n    // 注册需要保护的关键函数\n    int (*criticalFunction)(int, int) = [](int a, int b) -> int {\n        return a * b + a - b; // 示例计算\n    };\n    \n    // 注册函数获取唯一ID\n    uint32_t functionId = modifier.RegisterFunction((void*)criticalFunction, 120); // 120字节大小\n    if (functionId == 0) {\n        fprintf(stderr, \"函数注册失败\\n\");\n        return 1;\n    }\n    \n    printf(\"关键函数已注册，ID: %u\\n\", functionId);\n    \n    // 创建初始代码变体\n    if (!modifier.CreateInitialVariant(functionId)) {\n        fprintf(stderr, \"创建初始代码变体失败\\n\");\n        return 1;\n    }\n    \n    printf(\"已创建初始代码变体\\n\");\n    \n    // 设置代码检测回调\n    modifier.SetCodeIntegrityCallback([](CodeIntegrityInfo* info) {\n        if (!info->isIntact) {\n            printf(\"检测到代码完整性问题!\\n\");\n            printf(\"  - 函数ID: %u\\n\", info->functionId);\n            printf(\"  - 检测时间: %llu ms\\n\", info->detectionTime);\n            printf(\"  - 状态: %s\\n\", info->statusMessage);\n        }\n    });\n    \n    // 启动代码完整性监控\n    modifier.StartCodeMonitoring();\n    \n    // 演示调用受保护的函数多次，观察自动变异\n    printf(\"\\n开始测试自篡改保护...\\n\");\n    \n    for (int i = 0; i < 20; i++) {\n        // 获取当前函数变体\n        void* currentVariant = modifier.GetCurrentFunctionVariant(functionId);\n        if (!currentVariant) {\n            fprintf(stderr, \"获取当前函数变体失败\\n\");\n            break;\n        }\n        \n        // 转换为函数指针并调用\n        int (*funcPtr)(int, int) = (int (*)(int, int))currentVariant;\n        int result = funcPtr(10, 5);\n        \n        printf(\"调用 #%d - 计算结果: %d\\n\", i+1, result);\n        \n        // 检查函数是否已变异\n        VariantInfo variantInfo = modifier.GetVariantInfo(functionId);\n        printf(\"  当前变体ID: %u, 已执行变异次数: %u\\n\", \n               variantInfo.currentVariantId, \n               variantInfo.totalMutations);\n        \n        // 强制执行一次变异（通常由系统自动触发）\n        if (i % 5 == 4) {\n            printf(\"  触发代码变异...\\n\");\n            modifier.ForceMutation(functionId);\n        }\n    }\n    \n    // 停止代码监控\n    modifier.StopCodeMonitoring();\n    \n    // 恢复原始代码（可选）\n    printf(\"\\n恢复原始代码...\\n\");\n    if (modifier.RestoreOriginalCode(functionId)) {\n        printf(\"原始代码已恢复\\n\");\n    }\n    \n    // 清理资源\n    modifier.Cleanup();\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化代码自篡改模块\n      参数:\n        - 名称: config\n          类型: SelfModificationConfig\n          描述: 自篡改配置参数\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: modifier.Initialize(config)", "RegisterFunction": "功能: 注册需要保护的函数\n      参数:\n        - 名称: functionPtr\n          类型: void*\n          描述: 函数指针\n        - 名称: size\n          类型: size_t\n          描述: 函数代码大小(字节)\n      返回值:\n        类型: uint32_t\n        描述: 函数注册ID，0表示失败\n      示例: functionId = modifier.RegisterFunction((void*)function, size)", "CreateInitialVariant": "功能: 创建函数的初始变体\n      参数:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n      返回值:\n        类型: bool\n        描述: 创建是否成功\n      示例: modifier.CreateInitialVariant(functionId)", "GetCurrentFunctionVariant": "功能: 获取当前函数变体\n      参数:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n      返回值:\n        类型: void*\n        描述: 当前函数变体指针，NULL表示失败\n      示例: currentVariant = modifier.GetCurrentFunctionVariant(functionId)", "SetCodeIntegrityCallback": "功能: 设置代码完整性检测回调\n      参数:\n        - 名称: callback\n          类型: std::function<void(CodeIntegrityInfo*)>\n          描述: 检测到代码完整性问题时调用的回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: modifier.SetCodeIntegrityCallback(callbackFunction)", "StartCodeMonitoring": "功能: 启动代码完整性监控\n      参数:\n      返回值:\n        类型: bool\n        描述: 启动是否成功\n      示例: modifier.StartCodeMonitoring()", "ForceMutation": "功能: 强制执行代码变异\n      参数:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n      返回值:\n        类型: bool\n        描述: 变异是否成功\n      示例: modifier.ForceMutation(functionId)", "GetVariantInfo": "功能: 获取变体信息\n      参数:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n      返回值:\n        类型: VariantInfo\n        描述: 变体信息\n      示例: variantInfo = modifier.GetVariantInfo(functionId)", "StopCodeMonitoring": "功能: 停止代码完整性监控\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: modifier.StopCodeMonitoring()", "RestoreOriginalCode": "功能: 恢复原始代码\n      参数:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n      返回值:\n        类型: bool\n        描述: 恢复是否成功\n      示例: modifier.RestoreOriginalCode(functionId)"}, "数据结构": {"SelfModificationConfig": "描述: 代码自篡改配置\n      字段:\n        - 名称: enableCodeMutation\n          类型: bool\n          描述: 是否启用代码变异\n        - 名称: enableInstructionSubstitution\n          类型: bool\n          描述: 是否启用指令替换\n        - 名称: enableJumpObfuscation\n          类型: bool\n          描述: 是否启用跳转混淆\n        - 名称: mutationFrequency\n          类型: uint32_t\n          描述: 变异频率(调用次数)\n        - 名称: preserveFunctionality\n          类型: bool\n          描述: 是否保持功能一致性", "CodeIntegrityInfo": "描述: 代码完整性信息\n      字段:\n        - 名称: isIntact\n          类型: bool\n          描述: 代码是否完整\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n        - 名称: detectionTime\n          类型: uint64_t\n          描述: 检测时间戳(毫秒)\n        - 名称: statusMessage\n          类型: char[128]\n          描述: 状态消息", "VariantInfo": "描述: 变体信息\n      字段:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n        - 名称: currentVariantId\n          类型: uint32_t\n          描述: 当前变体ID\n        - 名称: totalMutations\n          类型: uint32_t\n          描述: 已执行的变异总次数\n        - 名称: lastMutationTime\n          类型: uint64_t\n          描述: 上次变异时间戳(毫秒)"}}