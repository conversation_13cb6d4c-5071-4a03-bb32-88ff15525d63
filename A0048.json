{"示例代码": {"受控端数据保存示例": "int main() {\n    // 初始化更新数据管理器\n    UpdateDataManager manager;\n    \n    // 接收到的更新文件路径\n    const char* tempUpdatePath = \"/tmp/client_update.bin\";\n    \n    // 验证更新包完整性\n    if (!manager.VerifyUpdatePackage(tempUpdatePath)) {\n        printf(\"更新包验证失败，可能已损坏\\n\");\n        return 1;\n    }\n    \n    // 检查存储空间是否足够\n    uint64_t requiredSpace = manager.GetRequiredSpace(tempUpdatePath);\n    if (!manager.CheckDiskSpace(requiredSpace)) {\n        printf(\"存储空间不足，需要 %lu 字节\\n\", requiredSpace);\n        return 1;\n    }\n    \n    // 创建安全存储目录\n    const char* updateDir = \"/opt/client/updates\";\n    if (!manager.EnsureDirectoryExists(updateDir)) {\n        printf(\"创建更新存储目录失败\\n\");\n        return 1;\n    }\n    \n    // 保存更新文件到安全位置\n    char targetPath[256];\n    snprintf(targetPath, sizeof(targetPath), \"%s/update_%lu.bin\", updateDir, time(NULL));\n    \n    if (manager.SaveUpdateFile(tempUpdatePath, targetPath)) {\n        printf(\"更新文件已成功保存到: %s\\n\", targetPath);\n        \n        // 更新配置文件，记录新版本信息\n        if (manager.UpdateVersionInfo(targetPath)) {\n            printf(\"版本信息更新成功\\n\");\n        }\n        \n        // 删除临时文件\n        unlink(tempUpdatePath);\n        return 0;\n    } else {\n        printf(\"更新文件保存失败\\n\");\n        return 1;\n    }\n}"}, "API接口": {"VerifyUpdatePackage": "功能: 验证更新包的完整性和签名\n      参数:\n        - 名称: updateFilePath\n          类型: const char*\n          描述: 更新文件路径\n      返回值:\n        类型: bool\n        描述: 更新包是否验证通过\n      示例: manager.VerifyUpdatePackage(tempUpdatePath)", "GetRequiredSpace": "功能: 计算安装更新所需的磁盘空间\n      参数:\n        - 名称: updateFilePath\n          类型: const char*\n          描述: 更新文件路径\n      返回值:\n        类型: uint64_t\n        描述: 所需磁盘空间(字节)\n      示例: uint64_t requiredSpace = manager.GetRequiredSpace(tempUpdatePath)", "CheckDiskSpace": "功能: 检查是否有足够的磁盘空间\n      参数:\n        - 名称: requiredBytes\n          类型: uint64_t\n          描述: 所需空间大小(字节)\n      返回值:\n        类型: bool\n        描述: 是否有足够空间\n", "EnsureDirectoryExists": "功能: 确保目录存在，不存在则创建\n      参数:\n        - 名称: directoryPath\n          类型: const char*\n          描述: 目录路径\n      返回值:\n        类型: bool\n        描述: 目录是否存在或创建成功\n      示例: manager.EnsureDirectoryExists(updateDir)", "SaveUpdateFile": "功能: 将更新文件保存到指定位置\n      参数:\n        - 名称: sourcePath\n          类型: const char*\n          描述: 源文件路径\n        - 名称: targetPath\n          类型: const char*\n          描述: 目标文件路径\n      返回值:\n        类型: bool\n        描述: 文件是否成功保存\n      示例: manager.SaveUpdateFile(tempUpdatePath, targetPath)", "UpdateVersionInfo": "功能: 更新配置文件中的版本信息\n      参数:\n        - 名称: updateFilePath\n          类型: const char*\n          描述: 更新文件路径\n      返回值:\n        类型: bool\n        描述: 版本信息是否更新成功\n      示例: manager.UpdateVersionInfo(targetPath)"}, "数据结构": {"UPDATE_FILE_INFO": "描述: 更新文件信息结构体\n      字段:\n        - 名称: filePath\n          类型: char[256]\n          描述: 更新文件路径\n        - 名称: fileSize\n          类型: uint64_t\n          描述: 文件大小(字节)\n        - 名称: versionMajor\n          类型: uint16_t\n          描述: 主版本号\n        - 名称: versionMinor\n          类型: uint16_t\n          描述: 次版本号\n        - 名称: versionHash\n          类型: char[32]\n          描述: 版本哈希值\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 文件保存时间戳\n        - 名称: signature\n          类型: char[64]\n          描述: 文件签名", "DISK_SPACE_INFO": "描述: 磁盘空间信息结构体\n      字段:\n        - 名称: totalSpace\n          类型: uint64_t\n          描述: 总磁盘空间(字节)\n        - 名称: availableSpace\n          类型: uint64_t\n          描述: 可用磁盘空间(字节)\n        - 名称: requiredSpace\n          类型: uint64_t\n          描述: 所需磁盘空间(字节)"}}