{"示例代码": {"系统API反调试示例": "int main() {\n    // 初始化系统API反调试模块\n    ApiBasedAntiDebug antiDebug;\n    \n    // 配置API检测选项\n    ApiDetectionConfig config;\n    config.enablePtraceCheck = true;\n    config.enableProcStatusCheck = true;\n    config.enableParentProcessCheck = true;\n    config.enableFileDescriptorCheck = true;\n    config.detectionFrequencyMs = 500;\n    \n    // 初始化反调试机制\n    if (!antiDebug.Initialize(config)) {\n        fprintf(stderr, \"无法初始化系统API反调试机制\\n\");\n        return 1;\n    }\n    \n    // 注册调试检测回调\n    antiDebug.RegisterDetectionCallback([](ApiDetectionResult* result) {\n        printf(\"检测到调试器！检测方法: %s\\n\", result->detectionMethod);\n        printf(\"  检测细节: %s\\n\", result->detectionDetails);\n        \n        // 实现自定义反应，例如混淆代码或自我终止\n        switch (result->detectionMethod[0]) {\n            case 'P': // Ptrace检测\n                printf(\"通过Ptrace API检测到调试器\\n\");\n                break;\n                \n            case 'S': // 状态文件检测\n                printf(\"通过/proc状态文件检测到调试器\\n\");\n                break;\n                \n            case 'F': // 文件描述符检测\n                printf(\"通过文件描述符检测到调试器\\n\");\n                break;\n        }\n    });\n    \n    // 执行单次综合检测\n    printf(\"执行单次调试器检测...\\n\");\n    if (antiDebug.PerformFullDetection()) {\n        printf(\"检测到调试器！执行防护措施...\\n\");\n        antiDebug.ExecuteProtectiveMeasures(PROTECTION_LEVEL_HIGH);\n        return 1;\n    }\n    \n    // 启动后台监控线程\n    antiDebug.StartMonitoring();\n    \n    printf(\"程序正在运行，API反调试监控已启动...\\n\");\n    \n    // 模拟程序主循环\n    int counter = 0;\n    while (counter < 10) {\n        // 在关键点进行调试检测\n        if (counter == 5) {\n            // 使用Ptrace验证是否被调试\n            if (antiDebug.CheckWithPtrace()) {\n                printf(\"通过Ptrace检测到调试器！\\n\");\n                break;\n            }\n            \n            // 通过/proc/self/status检测\n            if (antiDebug.CheckProcStatus()) {\n                printf(\"通过/proc/self/status检测到调试器！\\n\");\n                break;\n            }\n            \n            // 父进程检测\n            if (antiDebug.CheckParentProcess()) {\n                printf(\"通过父进程检测到可能的调试器！\\n\");\n                break;\n            }\n        }\n        \n        printf(\"执行程序逻辑循环 %d...\\n\", counter);\n        sleep(1);\n        counter++;\n    }\n    \n    // 停止监控并清理资源\n    antiDebug.StopMonitoring();\n    antiDebug.Cleanup();\n    \n    printf(\"程序正常完成\\n\");\n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化系统API反调试模块\n      参数:\n        - 名称: config\n          类型: ApiDetectionConfig\n          描述: API检测配置参数\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: antiDebug.Initialize(config)", "RegisterDetectionCallback": "功能: 注册调试检测回调函数\n      参数:\n        - 名称: callback\n          类型: std::function<void(ApiDetectionResult*)>\n          描述: 检测到调试器时调用的回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: antiDebug.RegisterDetectionCallback(callbackFunction)", "PerformFullDetection": "功能: 执行一次完整的调试器检测\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否检测到调试器（true表示检测到）\n      示例: antiDebug.PerformFullDetection()", "StartMonitoring": "功能: 启动持续调试监控线程\n      参数:\n      返回值:\n        类型: bool\n        描述: 监控是否成功启动\n      示例: antiDebug.StartMonitoring()", "CheckWithPtrace": "功能: 使用ptrace系统调用检测调试器\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否检测到调试器（true表示检测到）\n      示例: antiDebug.CheckWithPtrace()", "CheckProcStatus": "功能: 检查/proc/self/status文件中的TracerPid字段\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否检测到调试器（true表示检测到）\n      示例: antiDebug.CheckProcStatus()", "CheckParentProcess": "功能: 检查父进程是否为调试器\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否检测到调试器（true表示检测到）\n      示例: antiDebug.CheckParentProcess()", "ExecuteProtectiveMeasures": "功能: 执行防护措施应对检测到的调试器\n      参数:\n        - 名称: level\n          类型: ProtectionLevel\n          描述: 防护级别\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: antiDebug.ExecuteProtectiveMeasures(PROTECTION_LEVEL_HIGH)", "StopMonitoring": "功能: 停止调试监控\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: antiDebug.StopMonitoring()"}, "数据结构": {"ApiDetectionConfig": "描述: API检测配置\n      字段:\n        - 名称: enablePtraceCheck\n          类型: bool\n          描述: 是否启用ptrace检测\n        - 名称: enableProcStatusCheck\n          类型: bool\n          描述: 是否启用/proc状态文件检测\n        - 名称: enableParentProcessCheck\n          类型: bool\n          描述: 是否启用父进程检测\n        - 名称: enableFileDescriptorCheck\n          类型: bool\n          描述: 是否启用文件描述符检测\n        - 名称: detectionFrequencyMs\n          类型: uint32_t\n          描述: 检测频率（毫秒）", "ApiDetectionResult": "描述: API检测结果\n      字段:\n        - 名称: detected\n          类型: bool\n          描述: 是否检测到调试器\n        - 名称: detectionMethod\n          类型: char[64]\n          描述: 检测方法名称\n        - 名称: detectionDetails\n          类型: char[256]\n          描述: 检测详细信息\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 检测时间戳\n        - 名称: additionalInfo\n          类型: void*\n          描述: 额外信息指针", "ProtectionLevel": "描述: 防护措施级别枚举\n      字段:\n        - 名称: PROTECTION_LEVEL_LOW\n          类型: enum\n          描述: 低级防护（仅记录检测结果）\n        - 名称: PROTECTION_LEVEL_MEDIUM\n          类型: enum\n          描述: 中级防护（混淆执行流程）\n        - 名称: PROTECTION_LEVEL_HIGH\n          类型: enum\n          描述: 高级防护（终止进程并清理痕迹）"}}