{"示例代码": {"说明": "暂无"}, "API接口": {"GetSystemOpenServices": "功能: 获取并扫描目标设备启用的网络服务及监听端口。\n      参数:\n        - 名称: target_host\n          类型: const char*\n          描述: 目标主机的IP地址或域名。\n        - 名称: services_list_out\n          类型: OpenServiceInfo**\n          描述: 用于接收开放服务列表的输出参数，需调用方释放内存。\n        - 名称: count_out\n          类型: int*\n          描述: 用于接收开放服务数量的输出参数。\n      返回值:\n        类型: int\n        描述: 操作状态，0表示成功，非0表示失败或错误码。\n      示例: OpenServiceInfo* services = NULL; int service_count = 0; if (GetSystemOpenServices(\"127.0.0.1\", &services, &service_count) == 0) { /* 处理服务列表 */ }"}, "数据结构": {"OpenServiceInfo": "描述: 存储单个已发现的网络服务的信息。\n      字段:\n        - 名称: port\n          类型: unsigned short\n          描述: 服务监听的端口号。\n        - 名称: protocol\n          类型: char[16]\n          描述: 服务使用的协议 (例如 \"TCP\", \"UDP\")。\n        - 名称: service_name\n          类型: char[128]\n          描述: 服务的名称或简要描述 (例如 \"HTTP\", \"FTP\")，可能为空。"}}