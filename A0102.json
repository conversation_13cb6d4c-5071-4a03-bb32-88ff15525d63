{"示例代码": {"载荷配置示例": "// 创建载荷配置对象\nPayloadConfig config;\n\n// 设置受控端类型\nconfig.SetPayloadType(PAYLOAD_TYPE_REVERSE_SHELL);\n\n// 设置通信参数\nconfig.SetTargetIP(\"*************\");\nconfig.SetPort(4444);\n\n// 配置加密选项\nconfig.SetEncryption(true);\nconfig.SetEncryptionKey(\"秘密密钥\");\n\n// 应用配置\nif (config.Validate()) {\n    printf(\"载荷配置有效，可以用于生成\\n\");\n} else {\n    printf(\"载荷配置无效，请检查参数\\n\");\n}"}, "API接口": {"SetPayloadType": "功能: 设置生成的受控端木马类型\n      参数:\n        - 名称: type\n          类型: int\n          描述: 受控端类型，可选值为预定义常量\n      返回值:\n        类型: void\n        描述: 无\n      示例: config.SetPayloadType(PAYLOAD_TYPE_REVERSE_SHELL)", "SetTargetIP": "功能: 设置目标IP地址\n      参数:\n        - 名称: ip\n          类型: const char*\n          描述: 目标IP地址字符串\n      返回值:\n        类型: bool\n        描述: IP地址格式是否有效\n      示例: config.SetTargetIP(\"*************\")", "SetPort": "功能: 设置通信端口\n      参数:\n        - 名称: port\n          类型: unsigned short\n          描述: 通信端口号\n      返回值:\n        类型: bool\n        描述: 端口设置是否有效\n      示例: config.SetPort(4444)", "SetEncryption": "功能: 配置是否启用加密\n      参数:\n        - 名称: enable\n          类型: bool\n          描述: 是否启用加密\n      返回值:\n        类型: void\n        描述: 无\n      示例: config.SetEncryption(true)", "SetEncryptionKey": "功能: 设置加密密钥\n      参数:\n        - 名称: key\n          类型: const char*\n          描述: 加密密钥\n      返回值:\n        类型: bool\n        描述: 密钥是否有效\n      示例: config.SetEncryptionKey(\"秘密密钥\")", "Validate": "功能: 验证配置参数是否有效\n      参数:\n      返回值:\n        类型: bool\n        描述: 配置是否有效\n      示例: if (config.Validate()) { ... }"}, "数据结构": {"PayloadConfig": "描述: 载荷配置结构体\n      字段:\n        - 名称: payloadType\n          类型: int\n          描述: 受控端木马类型\n        - 名称: targetIP\n          类型: char[16]\n          描述: 目标IP地址\n        - 名称: port\n          类型: unsigned short\n          描述: 通信端口\n        - 名称: encryptionEnabled\n          类型: bool\n          描述: 是否启用加密\n        - 名称: encryptionKey\n          类型: char[64]\n          描述: 加密密钥\n        - 名称: encryptionAlgorithm\n          类型: int\n          描述: 加密算法类型", "PAYLOAD_TYPES": "描述: 受控端木马类型常量\n      字段:\n        - 名称: PAYLOAD_TYPE_REVERSE_SHELL\n          类型: const int\n          描述: 反向Shell\n        - 名称: PAYLOAD_TYPE_BIND_SHELL\n          类型: const int\n          描述: 绑定Shell\n        - 名称: PAYLOAD_TYPE_METERPRETER\n          类型: const int\n          描述: Meterpreter载荷\n        - 名称: PAYLOAD_TYPE_CUSTOM\n          类型: const int\n          描述: 自定义载荷类型"}}