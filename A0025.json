{"示例代码": {"文件变形示例": "int main() {\n    // 创建文件变形管理器\n    FileMorpher morpher;\n    \n    // 配置变形选项\n    MorphOptions options;\n    options.inputFilePath = \"/opt/payload/original.exe\";\n    options.outputFilePath = \"/opt/payload/morphed.exe\";\n    options.morphMethod = MORPH_METHOD_POLYMORPHIC;\n    options.preserveFileSize = false;\n    options.preserveFunctionality = true;\n    \n    // 设置多态变形参数\n    PolymorphicOptions polyOptions;\n    polyOptions.instructionSubstitution = true;\n    polyOptions.registerReassignment = true;\n    polyOptions.deadCodeInsertion = true;\n    polyOptions.codeTransposition = true;\n    polyOptions.junkInsertion = true;\n    \n    options.polymorphicOptions = polyOptions;\n    \n    // 设置混淆参数\n    ObfuscationOptions obfuscate;\n    obfuscate.obfuscateStrings = true;\n    obfuscate.obfuscateControlFlow = true;\n    obfuscate.encryptCode = true;\n    obfuscate.encryptionKey = \"random\";  // 使用随机密钥\n    \n    options.obfuscationOptions = obfuscate;\n    \n    // 执行文件变形\n    MorphResult result = morpher.morphFile(options);\n    \n    if (result.success) {\n        printf(\"File morphing successful\\n\");\n        printf(\"Output file: %s\\n\", options.outputFilePath.c_str());\n        printf(\"Original size: %zu bytes\\n\", result.originalSize);\n        printf(\"Morphed size: %zu bytes\\n\", result.morphedSize);\n        printf(\"Morphing method: %s\\n\", result.methodUsed.c_str());\n    } else {\n        printf(\"Morphing failed: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 验证变形后的文件\n    VerificationResult verifyResult = morpher.verifyMorphedFile(\n        options.inputFilePath, options.outputFilePath);\n    \n    if (verifyResult.functionallyEquivalent) {\n        printf(\"Verification passed: Files are functionally equivalent\\n\");\n    } else {\n        printf(\"Verification failed: %s\\n\", verifyResult.reason.c_str());\n    }\n    \n    return 0;\n}"}, "API接口": {"morphFile": "功能: 执行文件变形操作\n      参数:\n        - 名称: options\n          类型: MorphOptions\n          描述: 变形选项\n      返回值:\n        类型: MorphResult\n        描述: 变形结果\n      示例: MorphResult result = morpher.morphFile(options);", "verifyMorphedFile": "功能: 验证变形后的文件是否与原始文件功能相同\n      参数:\n        - 名称: originalFilePath\n          类型: const std::string&\n          描述: 原始文件路径\n        - 名称: morphedFilePath\n          类型: const std::string&\n          描述: 变形后文件路径\n      返回值:\n        类型: VerificationResult\n        描述: 验证结果\n      示例: VerificationResult result = morpher.verifyMorphedFile(originalPath, morphedPath);", "getAvailableMorphMethods": "功能: 获取可用的变形方法\n      参数:\n      返回值:\n        类型: std::vector<MorphMethod>\n        描述: 可用的变形方法列表\n      示例: auto methods = morpher.getAvailableMorphMethods();", "createMorphingProfile": "功能: 创建变形配置文件\n      参数:\n        - 名称: profileName\n          类型: const std::string&\n          描述: 配置文件名称\n        - 名称: options\n          类型: MorphOptions\n          描述: 变形选项\n      返回值:\n        类型: bool\n        描述: 创建是否成功\n      示例: bool created = morpher.createMorphingProfile(\"evasion_profile\", options);", "loadMorphingProfile": "功能: 加载变形配置文件\n      参数:\n        - 名称: profileName\n          类型: const std::string&\n          描述: 配置文件名称\n      返回值:\n        类型: MorphOptions\n        描述: 加载的变形选项\n      示例: MorphOptions options = morpher.loadMorphingProfile(\"evasion_profile\");"}, "数据结构": {"MorphOptions": "描述: 文件变形选项结构体\n      字段:\n        - 名称: inputFilePath\n          类型: std::string\n          描述: 输入文件路径\n        - 名称: outputFilePath\n          类型: std::string\n          描述: 输出文件路径\n        - 名称: morphMethod\n          类型: MorphMethod\n          描述: 变形方法\n        - 名称: preserveFileSize\n          类型: bool\n          描述: 是否保持文件大小不变\n        - 名称: preserveFunctionality\n          类型: bool\n          描述: 是否保持功能不变\n        - 名称: polymorphicOptions\n          类型: PolymorphicOptions\n          描述: 多态变形选项\n        - 名称: obfuscationOptions\n          类型: ObfuscationOptions\n          描述: 混淆选项", "MorphMethod": "描述: 变形方法枚举\n      字段:\n        - 名称: MORPH_METHOD_POLYMORPHIC\n          类型: enum\n          描述: 多态变形\n        - 名称: MORPH_METHOD_ENCRYPTION\n          类型: enum\n          描述: 加密变形\n        - 名称: MORPH_METHOD_PACKING\n          类型: enum\n          描述: 打包变形\n        - 名称: MORPH_METHOD_VIRTUALIZATION\n          类型: enum\n          描述: 虚拟化变形\n        - 名称: MORPH_METHOD_CUSTOM\n          类型: enum\n          描述: 自定义变形", "PolymorphicOptions": "描述: 多态变形选项结构体\n      字段:\n        - 名称: instructionSubstitution\n          类型: bool\n          描述: 是否进行指令替换\n        - 名称: registerReassignment\n          类型: bool\n          描述: 是否重新分配寄存器\n        - 名称: deadCodeInsertion\n          类型: bool\n          描述: 是否插入死代码\n        - 名称: codeTransposition\n          类型: bool\n          描述: 是否进行代码转置\n        - 名称: junkInsertion\n          类型: bool\n          描述: 是否插入垃圾代码\n        - 名称: iterations\n          类型: uint32_t\n          描述: 变形迭代次数", "ObfuscationOptions": "描述: 混淆选项结构体\n      字段:\n        - 名称: obfuscateStrings\n          类型: bool\n          描述: 是否混淆字符串\n        - 名称: obfuscateControlFlow\n          类型: bool\n          描述: 是否混淆控制流\n        - 名称: encryptCode\n          类型: bool\n          描述: 是否加密代码\n        - 名称: encryptionKey\n          类型: std::string\n          描述: 加密密钥或'random'表示随机生成\n        - 名称: obfuscationLevel\n          类型: uint32_t\n          描述: 混淆级别（1-10）", "MorphResult": "描述: 变形结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 变形是否成功\n        - 名称: originalSize\n          类型: size_t\n          描述: 原始文件大小\n        - 名称: morphedSize\n          类型: size_t\n          描述: 变形后文件大小\n        - 名称: methodUsed\n          类型: std::string\n          描述: 使用的变形方法\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: morphSignature\n          类型: std::string\n          描述: 变形签名，可用于标识变形实例", "VerificationResult": "描述: 验证结果结构体\n      字段:\n        - 名称: functionallyEquivalent\n          类型: bool\n          描述: 文件是否功能等价\n        - 名称: reason\n          类型: std::string\n          描述: 验证结果原因\n        - 名称: similarityScore\n          类型: float\n          描述: 相似度评分（0.0-1.0）\n        - 名称: verificationMethod\n          类型: std::string\n          描述: 使用的验证方法"}}