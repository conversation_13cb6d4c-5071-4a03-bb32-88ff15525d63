{"示例代码": {"混杂模式嗅探示例": "// Linux系统中使用C实现混杂模式网络嗅探\n#include <stdio.h>\n#include <stdlib.h>\n#include <string.h>\n#include <unistd.h>\n#include <net/if.h>\n#include <netinet/ip.h>\n#include <netinet/tcp.h>\n#include <netinet/udp.h>\n#include <netinet/if_ether.h>\n#include <sys/socket.h>\n#include <sys/ioctl.h>\n#include <arpa/inet.h>\n#include <linux/if_packet.h>\n\n#define BUFFER_SIZE 65536\n\n// 协议类型字符串\nchar *get_protocol_name(int protocol) {\n    switch(protocol) {\n        case IPPROTO_TCP:\n            return \"TCP\";\n        case IPPROTO_UDP:\n            return \"UDP\";\n        case IPPROTO_ICMP:\n            return \"ICMP\";\n        default:\n            return \"其他\";\n    }\n}\n\n// 打印数据包内容\nvoid print_packet_data(unsigned char *data, int size) {\n    for(int i = 0; i < size; i++) {\n        if(i != 0 && i % 16 == 0) {\n            printf(\"\\n\");\n        }\n        printf(\"%02X \", data[i]);\n    }\n    printf(\"\\n\");\n}\n\nint main(int argc, char *argv[]) {\n    char *interface = \"eth0\"; // 默认接口\n    if (argc > 1) {\n        interface = argv[1];\n    }\n    \n    // 创建原始套接字\n    int sock = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ALL));\n    if (sock < 0) {\n        perror(\"创建套接字失败\");\n        return 1;\n    }\n    \n    // 设置接口为混杂模式\n    struct ifreq ifr;\n    memset(&ifr, 0, sizeof(ifr));\n    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);\n    \n    // 获取当前标志\n    if (ioctl(sock, SIOCGIFFLAGS, &ifr) < 0) {\n        perror(\"获取接口标志失败\");\n        close(sock);\n        return 1;\n    }\n    \n    // 设置混杂模式标志\n    ifr.ifr_flags |= IFF_PROMISC;\n    \n    // 更新标志\n    if (ioctl(sock, SIOCSIFFLAGS, &ifr) < 0) {\n        perror(\"设置混杂模式失败\");\n        close(sock);\n        return 1;\n    }\n    \n    printf(\"接口 %s 已设置为混杂模式, 开始监听网络数据包...\\n\\n\", interface);\n    \n    unsigned char buffer[BUFFER_SIZE];\n    struct ethhdr *eth_header;\n    struct iphdr *ip_header;\n    struct tcphdr *tcp_header;\n    struct udphdr *udp_header;\n    \n    while (1) {\n        // 接收所有类型的数据包\n        int data_size = recvfrom(sock, buffer, BUFFER_SIZE, 0, NULL, NULL);\n        if (data_size < 0) {\n            perror(\"接收数据包失败\");\n            break;\n        }\n        \n        // 处理以太网帧\n        eth_header = (struct ethhdr *)buffer;\n        \n        // 仅处理IP数据包\n        if (ntohs(eth_header->h_proto) == ETH_P_IP) {\n            // 解析IP头部\n            ip_header = (struct iphdr*)(buffer + sizeof(struct ethhdr));\n            \n            // 源IP和目标IP\n            struct in_addr src_addr, dst_addr;\n            src_addr.s_addr = ip_header->saddr;\n            dst_addr.s_addr = ip_header->daddr;\n            \n            printf(\"\\n=== 捕获到数据包 ===\\n\");\n            printf(\"协议: %s\\n\", get_protocol_name(ip_header->protocol));\n            printf(\"源IP: %s\\n\", inet_ntoa(src_addr));\n            printf(\"目标IP: %s\\n\", inet_ntoa(dst_addr));\n            \n            // 根据协议类型解析传输层头部\n            if (ip_header->protocol == IPPROTO_TCP) {\n                tcp_header = (struct tcphdr*)(buffer + sizeof(struct ethhdr) + ip_header->ihl * 4);\n                printf(\"源端口: %u\\n\", ntohs(tcp_header->source));\n                printf(\"目标端口: %u\\n\", ntohs(tcp_header->dest));\n                \n                // 检测是否为HTTP请求或响应\n                unsigned char *payload = buffer + sizeof(struct ethhdr) + ip_header->ihl * 4 + tcp_header->doff * 4;\n                int payload_size = data_size - (sizeof(struct ethhdr) + ip_header->ihl * 4 + tcp_header->doff * 4);\n                \n                if (payload_size > 0) {\n                    // HTTP请求检测\n                    if ((ntohs(tcp_header->dest) == 80 || ntohs(tcp_header->source) == 80) &&\n                        (payload_size >= 4 && (strncmp((char*)payload, \"GET \", 4) == 0 ||\n                                           strncmp((char*)payload, \"POST\", 4) == 0 ||\n                                           strncmp((char*)payload, \"HTTP\", 4) == 0))) {\n                        printf(\"[HTTP流量]\\n\");\n                        \n                        // 打印HTTP头部(仅前100个字节或实际大小)\n                        int header_size = payload_size > 100 ? 100 : payload_size;\n                        printf(\"HTTP头部预览:\\n\");\n                        for(int i = 0; i < header_size; i++) {\n                            printf(\"%c\", payload[i] >= 32 && payload[i] <= 126 ? payload[i] : '.');\n                        }\n                        printf(\"\\n\");\n                    } else {\n                        printf(\"数据长度: %d字节\\n\", payload_size);\n                    }\n                }\n            }\n            else if (ip_header->protocol == IPPROTO_UDP) {\n                udp_header = (struct udphdr*)(buffer + sizeof(struct ethhdr) + ip_header->ihl * 4);\n                printf(\"源端口: %u\\n\", ntohs(udp_header->source));\n                printf(\"目标端口: %u\\n\", ntohs(udp_header->dest));\n                printf(\"UDP数据长度: %d字节\\n\", ntohs(udp_header->len) - sizeof(struct udphdr));\n            }\n            \n            // 对较小的数据包打印十六进制内容\n            if (data_size < 100) {\n                printf(\"数据包内容(十六进制):\\n\");\n                print_packet_data(buffer, data_size);\n            }\n            \n            printf(\"===================\\n\");\n        }\n    }\n    \n    // 关闭套接字前关闭混杂模式\n    ifr.ifr_flags &= ~IFF_PROMISC;\n    ioctl(sock, SIOCSIFFLAGS, &ifr);\n    \n    close(sock);\n    return 0;\n}"}, "API接口": {"InitializePacketSniffer": "功能: 初始化混杂模式数据包嗅探器\n      参数:\n        - 名称: interface\n          类型: const char*\n          描述: 网络接口名称\n      返回值:\n        类型: PacketSniffer*\n        描述: 数据包嗅探器句柄，失败返回NULL\n      示例: PacketSniffer* sniffer = InitializePacketSniffer(\"eth0\")", "EnablePromiscuousMode": "功能: 开启网络接口的混杂模式\n      参数:\n        - 名称: interface\n          类型: const char*\n          描述: 网络接口名称\n        - 名称: enable\n          类型: bool\n          描述: 是否启用混杂模式\n      返回值:\n        类型: bool\n        描述: 是否成功设置混杂模式\n      示例: EnablePromiscuousMode(\"eth0\", true)", "SetPacketFilter": "功能: 设置数据包过滤规则\n      参数:\n        - 名称: sniffer\n          类型: PacketSniffer*\n          描述: 数据包嗅探器句柄\n        - 名称: filter_expression\n          类型: const char*\n          描述: BPF过滤表达式\n      返回值:\n        类型: bool\n        描述: 是否成功设置过滤器\n      示例: SetPacketFilter(sniffer, \"tcp port 80 or udp port 53\")", "StartPacketCapture": "功能: 开始捕获数据包\n      参数:\n        - 名称: sniffer\n          类型: PacketSniffer*\n          描述: 数据包嗅探器句柄\n        - 名称: callback\n          类型: PacketCallback\n          描述: 数据包回调函数\n        - 名称: packet_count\n          类型: int\n          描述: 要捕获的数据包数量，0表示无限制\n      返回值:\n        类型: int\n        描述: 捕获的数据包数量，失败返回负值\n      示例: StartPacketCapture(sniffer, OnPacketReceived, 0)"}, "数据结构": {"EthernetHeader": "描述: 以太网帧头部结构体\n      字段:\n        - 名称: destination_mac\n          类型: unsigned char[6]\n          描述: 目标MAC地址\n        - 名称: source_mac\n          类型: unsigned char[6]\n          描述: 源MAC地址\n        - 名称: ether_type\n          类型: uint16_t\n          描述: 协议类型", "IPHeader": "描述: IP头部结构体\n      字段:\n        - 名称: version\n          类型: uint8_t\n          描述: IP版本\n        - 名称: header_length\n          类型: uint8_t\n          描述: 头部长度(4字节为单位)\n        - 名称: total_length\n          类型: uint16_t\n          描述: 总长度\n        - 名称: protocol\n          类型: uint8_t\n          描述: 协议(TCP=6, UDP=17, ICMP=1)\n        - 名称: source_ip\n          类型: uint32_t\n          描述: 源IP地址\n        - 名称: destination_ip\n          类型: uint32_t\n          描述: 目标IP地址", "CapturedPacket": "描述: 捕获的数据包结构体\n      字段:\n        - 名称: timestamp\n          类型: struct timeval\n          描述: 捕获时间戳\n        - 名称: packet_length\n          类型: uint32_t\n          描述: 数据包长度\n        - 名称: protocol\n          类型: int\n          描述: 协议类型\n        - 名称: source_ip\n          类型: char[16]\n          描述: 源IP地址字符串\n        - 名称: destination_ip\n          类型: char[16]\n          描述: 目标IP地址字符串\n        - 名称: source_port\n          类型: uint16_t\n          描述: 源端口(TCP/UDP)\n        - 名称: destination_port\n          类型: uint16_t\n          描述: 目标端口(TCP/UDP)\n        - 名称: payload\n          类型: unsigned char*\n          描述: 数据包负载\n        - 名称: payload_length\n          类型: uint32_t\n          描述: 负载长度"}}