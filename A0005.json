{"示例代码": {"反杀软技术示例": "int main() {\n    // 初始化反杀软组件\n    AVEvasion evasion;\n    \n    // 检测当前系统上运行的安全软件\n    AVDetectionResult detectionResult = evasion.detectSecuritySoftware();\n    \n    // 根据检测结果应用相应规避技术\n    for (const auto& secProduct : detectionResult.detectedProducts) {\n        EvasionStrategy strategy = evasion.getRecommendedStrategy(secProduct.type);\n        evasion.applyEvasionStrategy(strategy);\n        \n        // 验证规避是否成功\n        if (!evasion.verifyEvasion(secProduct.type)) {\n            // 规避失败，尝试备用方法或终止操作\n            if (secProduct.threatLevel >= THREAT_LEVEL_HIGH) {\n                return -1;  // 高威胁级别，终止执行\n            }\n        }\n    }\n    \n    // 成功规避杀软后执行主要逻辑\n    performMainOperation();\n    \n    // 结束后清理痕迹\n    evasion.cleanupTraces();\n    \n    return 0;\n}"}, "API接口": {"detectSecuritySoftware": "功能: 检测当前系统中运行的安全软件\n      参数:\n      返回值:\n        类型: AVDetectionResult\n        描述: 安全软件检测结果\n      示例: AVDetectionResult result = evasion.detectSecuritySoftware();", "getRecommendedStrategy": "功能: 获取针对特定安全软件类型的推荐规避策略\n      参数:\n        - 名称: securityType\n          类型: SecurityProductType\n          描述: 安全软件类型\n      返回值:\n        类型: EvasionStrategy\n        描述: 推荐的规避策略\n      示例: EvasionStrategy strategy = evasion.getRecommendedStrategy(secProduct.type);", "applyEvasionStrategy": "功能: 应用指定的规避策略\n      参数:\n        - 名称: strategy\n          类型: EvasionStrategy\n          描述: 要应用的规避策略\n      返回值:\n        类型: bool\n        描述: 策略应用是否成功\n      示例: bool success = evasion.applyEvasionStrategy(strategy);", "verifyEvasion": "功能: 验证针对特定安全软件的规避是否成功\n      参数:\n        - 名称: securityType\n          类型: SecurityProductType\n          描述: 安全软件类型\n      返回值:\n        类型: bool\n        描述: 规避是否成功\n      示例: bool evaded = evasion.verifyEvasion(secProduct.type);", "cleanupTraces": "功能: 清理操作痕迹\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: evasion.cleanupTraces();"}, "数据结构": {"AVDetectionResult": "描述: 安全软件检测结果结构体\n      字段:\n        - 名称: detectedProducts\n          类型: std::vector<SecurityProduct>\n          描述: 检测到的安全软件列表\n        - 名称: systemProtectionLevel\n          类型: ProtectionLevel\n          描述: 系统整体保护级别\n        - 名称: timestamp\n          类型: time_t\n          描述: 检测时间戳", "SecurityProduct": "描述: 安全软件详情结构体\n      字段:\n        - 名称: name\n          类型: std::string\n          描述: 安全软件名称\n        - 名称: type\n          类型: SecurityProductType\n          描述: 安全软件类型\n        - 名称: version\n          类型: std::string\n          描述: 安全软件版本\n        - 名称: threatLevel\n          类型: ThreatLevel\n          描述: 对操作的威胁级别\n        - 名称: processIds\n          类型: std::vector<pid_t>\n          描述: 相关进程ID列表", "EvasionStrategy": "描述: 规避策略结构体\n      字段:\n        - 名称: techniqueId\n          类型: uint32_t\n          描述: 规避技术ID\n        - 名称: parameters\n          类型: std::map<std::string, std::string>\n          描述: 规避技术参数\n        - 名称: priority\n          类型: uint8_t\n          描述: 规避优先级\n        - 名称: fallbackStrategyId\n          类型: uint32_t\n          描述: 备用规避策略ID"}}