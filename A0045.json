{"示例代码": {"下发受控端更新指令示例": "int main() {\n    // 初始化客户端更新控制器\n    ClientUpdateController controller;\n    \n    // 创建更新指令数据\n    UPDATE_INSTRUCTION updateCmd;\n    memset(&updateCmd, 0, sizeof(UPDATE_INSTRUCTION));\n    \n    // 填充更新指令数据\n    updateCmd.cmdType = CMD_UPDATE;\n    updateCmd.updateType = UPDATE_TYPE_FULL;\n    updateCmd.newVersionMajor = 2;\n    updateCmd.newVersionMinor = 1;\n    strcpy(updateCmd.newVersionHash, \"8a7b6c5d4e3f2a1b\");\n    updateCmd.updateSize = 1457664; // 更新包大小，单位为字节\n    \n    // 发送更新指令到指定客户端\n    CLIENT_HANDLE targetClient;\n    // 假设targetClient已经初始化并连接\n    \n    if (controller.SendUpdateCommand(targetClient, &updateCmd)) {\n        printf(\"更新指令发送成功，客户端将准备接收更新数据\\n\");\n    } else {\n        printf(\"更新指令发送失败\\n\");\n        return 1;\n    }\n    \n    // 等待客户端准备就绪的响应\n    if (controller.WaitForClientReady(targetClient, 30000)) { // 等待30秒\n        printf(\"客户端已准备好接收更新数据\\n\");\n    } else {\n        printf(\"客户端未能准备好接收更新，操作超时\\n\");\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"SendUpdateCommand": "功能: 发送受控端更新指令\n      参数:\n        - 名称: clientHandle\n          类型: CLIENT_HANDLE\n          描述: 受控端句柄\n        - 名称: updateCmd\n          类型: UPDATE_INSTRUCTION*\n          描述: 更新指令数据结构\n      返回值:\n        类型: bool\n        描述: 指令是否成功发送\n      示例: controller.SendUpdateCommand(targetClient, &updateCmd)", "WaitForClientReady": "功能: 等待客户端准备就绪\n      参数:\n        - 名称: clientHandle\n          类型: CLIENT_HANDLE\n          描述: 受控端句柄\n        - 名称: timeout\n          类型: uint32_t\n          描述: 超时时间(毫秒)\n      返回值:\n        类型: bool\n        描述: 客户端是否已就绪\n      示例: controller.WaitForClientReady(targetClient, 30000)", "CancelUpdateCommand": "功能: 取消已发送的更新指令\n      参数:\n        - 名称: clientHandle\n          类型: CLIENT_HANDLE\n          描述: 受控端句柄\n      返回值:\n        类型: bool\n        描述: 是否成功取消更新\n      示例: controller.CancelUpdateCommand(targetClient)"}, "数据结构": {"UPDATE_INSTRUCTION": "描述: 受控端更新指令结构体\n      字段:\n        - 名称: cmdType\n          类型: BYTE\n          描述: 命令类型，固定为CMD_UPDATE\n        - 名称: updateType\n          类型: BYTE\n          描述: 更新类型，如全量更新、增量更新等\n        - 名称: newVersionMajor\n          类型: uint16_t\n          描述: 新版本主版本号\n        - 名称: newVersionMinor\n          类型: uint16_t\n          描述: 新版本次版本号\n        - 名称: newVersionHash\n          类型: char[32]\n          描述: 新版本哈希值\n        - 名称: updateSize\n          类型: uint32_t\n          描述: 更新包大小(字节)\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 指令发送时间戳\n        - 名称: flags\n          类型: uint32_t\n          描述: 更新标志位", "UPDATE_TYPE": "描述: 更新类型枚举\n      字段:\n        - 名称: UPDATE_TYPE_FULL\n          类型: BYTE\n          描述: 全量更新，值为0x01\n        - 名称: UPDATE_TYPE_INCREMENTAL\n          类型: BYTE\n          描述: 增量更新，值为0x02\n        - 名称: UPDATE_TYPE_CRITICAL\n          类型: BYTE\n          描述: 关键安全更新，值为0x03"}}