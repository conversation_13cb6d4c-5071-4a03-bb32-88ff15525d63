{"示例代码": {"静默运行示例": "int main(int argc, char *argv[]) {\n    // 初始化静默模式组件\n    SilentMode silencer;\n    \n    // 配置静默运行选项\n    SilentConfig config;\n    config.hideConsoleWindow = true;\n    config.suppressErrorDialogs = true;\n    config.disableLogging = true;\n    config.redirectStdOutput = true;\n    config.outputRedirectionPath = \"/dev/null\";\n    \n    // 应用静默模式配置\n    silencer.applyConfig(config);\n    \n    // 隐藏进程\n    silencer.hideProcessFromTasklist();\n    \n    // 设置低进程优先级，减少资源占用特征\n    silencer.setProcessPriority(PRIORITY_BELOW_NORMAL);\n    \n    // 禁用系统通知\n    silencer.disableSystemNotifications();\n    \n    // 执行实际任务，不产生任何可见输出\n    performSilentOperation();\n    \n    // 清理和退出\n    silencer.cleanup();\n    \n    return 0;\n}"}, "API接口": {"applyConfig": "功能: 应用静默模式配置\n      参数:\n        - 名称: config\n          类型: SilentConfig\n          描述: 静默模式配置结构体\n      返回值:\n        类型: bool\n        描述: 配置应用是否成功\n      示例: silencer.applyConfig(config);", "hideProcessFromTasklist": "功能: 使进程在任务列表中隐藏\n      参数:\n      返回值:\n        类型: bool\n        描述: 操作是否成功\n      示例: silencer.hideProcessFromTasklist();", "setProcessPriority": "功能: 设置进程优先级\n      参数:\n        - 名称: priority\n          类型: ProcessPriority\n          描述: 进程优先级枚举值\n      返回值:\n        类型: bool\n        描述: 设置是否成功\n      示例: silencer.setProcessPriority(PRIORITY_BELOW_NORMAL);", "disableSystemNotifications": "功能: 禁用系统通知\n      参数:\n      返回值:\n        类型: bool\n        描述: 禁用操作是否成功\n      示例: silencer.disableSystemNotifications();", "redirectOutputToNull": "功能: 将所有标准输出重定向到空设备\n      参数:\n      返回值:\n        类型: bool\n        描述: 重定向是否成功\n      示例: silencer.redirectOutputToNull();", "cleanup": "功能: 清理静默模式设置\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: silencer.cleanup();"}, "数据结构": {"SilentConfig": "描述: 静默模式配置结构体\n      字段:\n        - 名称: hideConsoleWindow\n          类型: bool\n          描述: 是否隐藏控制台窗口\n        - 名称: suppressErrorDialogs\n          类型: bool\n          描述: 是否抑制错误对话框\n        - 名称: disableLogging\n          类型: bool\n          描述: 是否禁用日志记录\n        - 名称: redirectStdOutput\n          类型: bool\n          描述: 是否重定向标准输出\n        - 名称: outputRedirectionPath\n          类型: std::string\n          描述: 输出重定向路径", "ProcessPriority": "描述: 进程优先级枚举\n      字段:\n        - 名称: PRIORITY_IDLE\n          类型: enum\n          描述: 空闲优先级，仅在系统空闲时运行\n        - 名称: PRIORITY_BELOW_NORMAL\n          类型: enum\n          描述: 低于普通优先级\n        - 名称: PRIORITY_NORMAL\n          类型: enum\n          描述: 普通优先级\n        - 名称: PRIORITY_ABOVE_NORMAL\n          类型: enum\n          描述: 高于普通优先级\n        - 名称: PRIORITY_HIGH\n          类型: enum\n          描述: 高优先级\n        - 名称: PRIORITY_REALTIME\n          类型: enum\n          描述: 实时优先级，可能会干扰系统操作"}}