{"示例代码": {"受控端重新启动示例": "int main() {\n    // 初始化重启管理器\n    SilentRestartManager restarter;\n    \n    // 设置重启参数\n    RESTART_OPTIONS options;\n    memset(&options, 0, sizeof(RESTART_OPTIONS));\n    \n    // 配置重启选项\n    options.preserveEnvironment = true;  // 保留当前环境变量\n    options.delaySeconds = 3;           // 延迟3秒后重启\n    options.reconnectAfterRestart = true; // 重启后自动重连\n    options.maxRetryCount = 5;          // 最多重试5次\n    \n    // 获取当前程序路径\n    char execPath[256];\n    if (!restarter.GetCurrentExecutablePath(execPath, sizeof(execPath))) {\n        printf(\"获取当前程序路径失败\\n\");\n        return 1;\n    }\n    \n    printf(\"准备静默重启程序: %s\\n\", execPath);\n    \n    // 保存当前状态到临时文件\n    restarter.SaveSessionState(\"/tmp/client_session.state\");\n    \n    // 执行静默重启\n    if (restarter.SilentRestart(execPath, &options)) {\n        // 注意：这里的代码通常不会执行，因为程序已经被重启了\n        printf(\"重启失败，程序继续执行\\n\");\n    } else {\n        printf(\"重启初始化失败\\n\");\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"GetCurrentExecutablePath": "功能: 获取当前程序的可执行文件路径\n      参数:\n        - 名称: buffer\n          类型: char*\n          描述: 路径存储缓冲区\n        - 名称: bufferSize\n          类型: size_t\n          描述: 缓冲区大小\n      返回值:\n        类型: bool\n        描述: 是否成功获取路径\n      示例: restarter.GetCurrentExecutablePath(execPath, sizeof(execPath))", "SaveSessionState": "功能: 保存当前会话状态到临时文件\n      参数:\n        - 名称: statePath\n          类型: const char*\n          描述: 状态文件保存路径\n      返回值:\n        类型: bool\n        描述: 是否成功保存状态\n      示例: restarter.SaveSessionState(\"/tmp/client_session.state\")", "SilentRestart": "功能: 静默重启程序\n      参数:\n        - 名称: executablePath\n          类型: const char*\n          描述: 可执行文件路径\n        - 名称: options\n          类型: RESTART_OPTIONS*\n          描述: 重启选项结构体指针\n      返回值:\n        类型: bool\n        描述: 重启请求是否成功发起\n      示例: restarter.SilentRestart(execPath, &options)", "LoadSessionState": "功能: 从临时文件加载会话状态\n      参数:\n        - 名称: statePath\n          类型: const char*\n          描述: 状态文件路径\n      返回值:\n        类型: bool\n        描述: 是否成功加载状态\n      示例: restarter.LoadSessionState(\"/tmp/client_session.state\")", "IsRestartedInstance": "功能: 检查当前实例是否为重启后的实例\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否为重启后的实例\n      示例: if (restarter.IsRestartedInstance()) { /* 恢复之前的会话状态 */ }"}, "数据结构": {"RESTART_OPTIONS": "描述: 程序重启选项结构体\n      字段:\n        - 名称: preserveEnvironment\n          类型: bool\n          描述: 是否保留当前环境变量\n        - 名称: delaySeconds\n          类型: uint32_t\n          描述: 重启延迟时间(秒)\n        - 名称: reconnectAfterRestart\n          类型: bool\n          描述: 重启后是否自动重连\n        - 名称: maxRetryCount\n          类型: uint8_t\n          描述: 最大重试次数\n        - 名称: customArgs\n          类型: char[256]\n          描述: 传递给重启实例的自定义参数\n        - 名称: flags\n          类型: uint32_t\n          描述: 重启标志位", "SESSION_STATE": "描述: 会话状态结构体\n      字段:\n        - 名称: connectionState\n          类型: uint8_t\n          描述: 连接状态\n        - 名称: serverAddress\n          类型: char[128]\n          描述: 服务器地址\n        - 名称: serverPort\n          类型: uint16_t\n          描述: 服务器端口\n        - 名称: sessionToken\n          类型: char[64]\n          描述: 会话令牌\n        - 名称: lastActivity\n          类型: uint64_t\n          描述: 最后活动时间戳\n        - 名称: activeOperations\n          类型: uint16_t\n          描述: 活动操作计数"}}