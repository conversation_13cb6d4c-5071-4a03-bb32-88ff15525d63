{"示例代码": {"指令中继示例": "int main() {\n    // 初始化指令中继模块\n    CommandRelay relay;\n    \n    // 连接到网络链条\n    ChainID chainId = 12345678;\n    if (!relay.ConnectToChain(chainId)) {\n        printf(\"连接到网络链条失败\\n\");\n        return 1;\n    }\n    \n    // 配置中继参数\n    RelayConfig config;\n    config.bufferSize = 4096;\n    config.encryptionEnabled = true;\n    config.compressionLevel = 6;\n    config.timeoutSeconds = 30;\n    \n    // 启动指令中继服务\n    if (!relay.StartRelayService(config)) {\n        printf(\"启动中继服务失败\\n\");\n        relay.DisconnectFromChain();\n        return 1;\n    }\n    \n    printf(\"指令中继服务已启动，等待C2指令...\\n\");\n    \n    // 设置指令处理回调函数\n    relay.SetCommandHandler([](const CommandPacket* packet) {\n        printf(\"收到C2指令，ID: %u, 类型: %d\\n\", packet->id, packet->type);\n        \n        // 处理命令\n        CommandResult result;\n        result.id = packet->id;\n        result.status = CMD_RESULT_SUCCESS;\n        strcpy(result.output, \"命令已成功执行\");\n        \n        return result;\n    });\n    \n    // 主循环：维持中继服务运行\n    while (relay.IsServiceRunning()) {\n        // 获取中继状态\n        RelayStatus status = relay.GetStatus();\n        printf(\"活跃连接: %d, 已处理命令: %d, 带宽使用: %d KB/s\\n\",\n               status.activeConnections,\n               status.processedCommands,\n               status.bandwidthUsage);\n        \n        sleep(5);\n    }\n    \n    // 停止服务并断开连接\n    relay.StopRelayService();\n    relay.DisconnectFromChain();\n    \n    return 0;\n}"}, "API接口": {"ConnectToChain": "功能: 连接到已建立的网络链条\n      参数:\n        - 名称: chainId\n          类型: ChainID\n          描述: 目标网络链条ID\n      返回值:\n        类型: bool\n        描述: 连接是否成功\n      示例: relay.ConnectToChain(chainId)", "StartRelayService": "功能: 启动指令中继服务\n      参数:\n        - 名称: config\n          类型: RelayConfig\n          描述: 中继服务配置\n      返回值:\n        类型: bool\n        描述: 服务是否成功启动\n      示例: relay.StartRelayService(config)", "SetCommandHandler": "功能: 设置指令处理回调函数\n      参数:\n        - 名称: handler\n          类型: std::function<CommandResult(const CommandPacket*)>\n          描述: 处理C2指令的回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: relay.SetCommandHandler(callback)", "IsServiceRunning": "功能: 检查中继服务是否正在运行\n      参数:\n      返回值:\n        类型: bool\n        描述: 服务是否运行中\n      示例: relay.IsServiceRunning()", "GetStatus": "功能: 获取中继服务当前状态\n      参数:\n      返回值:\n        类型: RelayStatus\n        描述: 中继服务状态信息\n      示例: status = relay.GetStatus()", "StopRelayService": "功能: 停止指令中继服务\n      参数:\n      返回值:\n        类型: bool\n        描述: 服务是否成功停止\n      示例: relay.StopRelayService()", "DisconnectFromChain": "功能: 断开与网络链条的连接\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: relay.DisconnectFromChain()"}, "数据结构": {"RelayConfig": "描述: 指令中继服务配置\n      字段:\n        - 名称: bufferSize\n          类型: uint32_t\n          描述: 中继缓冲区大小（字节）\n        - 名称: encryptionEnabled\n          类型: bool\n          描述: 是否启用加密\n        - 名称: compressionLevel\n          类型: uint8_t\n          描述: 压缩级别（0-9）\n        - 名称: timeoutSeconds\n          类型: uint32_t\n          描述: 操作超时时间（秒）", "CommandPacket": "描述: C2指令数据包\n      字段:\n        - 名称: id\n          类型: uint32_t\n          描述: 指令ID\n        - 名称: type\n          类型: uint8_t\n          描述: 指令类型\n        - 名称: length\n          类型: uint32_t\n          描述: 数据长度\n        - 名称: data\n          类型: uint8_t[]\n          描述: 指令数据\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 时间戳", "CommandResult": "描述: 指令执行结果\n      字段:\n        - 名称: id\n          类型: uint32_t\n          描述: 对应指令ID\n        - 名称: status\n          类型: uint8_t\n          描述: 执行状态码\n        - 名称: output\n          类型: char[1024]\n          描述: 执行结果输出\n        - 名称: errorCode\n          类型: int32_t\n          描述: 错误代码（如有）", "RelayStatus": "描述: 中继服务状态信息\n      字段:\n        - 名称: activeConnections\n          类型: uint32_t\n          描述: 活跃连接数\n        - 名称: processedCommands\n          类型: uint64_t\n          描述: 已处理指令数量\n        - 名称: bandwidthUsage\n          类型: uint32_t\n          描述: 当前带宽使用（KB/s）\n        - 名称: uptimeSeconds\n          类型: uint64_t\n          描述: 服务运行时间（秒）"}}