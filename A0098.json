{"示例代码": {"说明": "暂无"}, "API接口": {"BypassESETDetection": "功能: 应用一系列策略和技术，尝试在ESET环境中运行时避免被检测。\n      参数:\n        - 名称: config\n          类型: const EvasionConfig*\n          描述: 指向包含特定绕过策略配置的结构体。\n      返回值:\n        类型: bool\n        描述: true表示绕过策略已成功应用，false表示应用失败或部分失败。\n      示例: EvasionConfig es_config = {...}; if (BypassESETDetection(&es_config)) { /* 尝试执行核心逻辑 */ }"}, "数据结构": {"EvasionConfig": "描述: \n      字段:\n        - 名称: stealth_level\n          类型: int\n          描述: 隐蔽等级 (例如，0-低, 1-中, 2-高)，影响所用技术的激进程度。\n        - 名称: target_eset_modules\n          类型: char[256]\n          描述: 尝试针对性绕过的ESET模块列表 (逗号分隔，可选)。\n        - 名称: use_polymorphism\n          类型: bool\n          描述: 是否启用多态代码生成以规避签名检测。"}}