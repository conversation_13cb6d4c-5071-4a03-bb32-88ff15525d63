{"示例代码": {"创建端口转发示例": "int main() {\n    // 初始化端口转发管理器\n    PortForwardManager forwardManager;\n    \n    // 创建端口转发配置\n    PORT_FORWARD_CONFIG config;\n    memset(&config, 0, sizeof(PORT_FORWARD_CONFIG));\n    \n    // 配置本地监听地址和端口\n    strcpy(config.localAddress, \"127.0.0.1\");\n    config.localPort = 8080;\n    \n    // 配置远程目标地址和端口\n    strcpy(config.remoteAddress, \"*************\");\n    config.remotePort = 80;\n    \n    // 设置转发参数\n    config.forwardType = FORWARD_TYPE_TCP;\n    config.timeout = 30;  // 连接超时设置为30秒\n    config.maxConnections = 20;  // 最大并发连接数\n    strcpy(config.description, \"Web服务转发\");\n    \n    // 创建端口转发规则\n    FORWARD_RULE_ID ruleId;\n    if (forwardManager.CreateForwardRule(&config, &ruleId)) {\n        printf(\"端口转发规则创建成功，规则ID: %u\\n\", ruleId);\n        \n        // 保存转发规则配置\n        forwardManager.SaveForwardRules(\"/etc/portforward/rules.conf\");\n    } else {\n        printf(\"端口转发规则创建失败: %s\\n\", forwardManager.GetLastError());\n        return 1;\n    }\n    \n    // 显示创建的转发规则信息\n    PORT_FORWARD_INFO info;\n    if (forwardManager.GetForwardRuleInfo(ruleId, &info)) {\n        printf(\"转发规则详情:\\n\");\n        printf(\"本地监听: %s:%d\\n\", info.localAddress, info.localPort);\n        printf(\"远程目标: %s:%d\\n\", info.remoteAddress, info.remotePort);\n        printf(\"转发类型: %s\\n\", info.forwardType == FORWARD_TYPE_TCP ? \"TCP\" : \"UDP\");\n        printf(\"状态: %s\\n\", info.active ? \"活跃\" : \"未激活\");\n    }\n    \n    return 0;\n}"}, "API接口": {"CreateForwardRule": "功能: 创建端口转发规则\n      参数:\n        - 名称: config\n          类型: PORT_FORWARD_CONFIG*\n          描述: 端口转发配置结构体指针\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID*\n          描述: 用于存储创建的规则ID\n      返回值:\n        类型: bool\n        描述: 规则是否成功创建\n      示例: forwardManager.CreateForwardRule(&config, &ruleId)", "GetForwardRuleInfo": "功能: 获取转发规则信息\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 规则ID\n        - 名称: info\n          类型: PORT_FORWARD_INFO*\n          描述: 转发规则信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取规则信息\n      示例: forwardManager.GetForwardRuleInfo(ruleId, &info)", "SaveForwardRules": "功能: 保存转发规则配置\n      参数:\n        - 名称: configPath\n          类型: const char*\n          描述: 配置文件保存路径\n      返回值:\n        类型: bool\n        描述: 是否成功保存配置\n      示例: forwardManager.SaveForwardRules(\"/etc/portforward/rules.conf\")", "GetLastError": "功能: 获取上一操作的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: forwardManager.GetLastError()"}, "数据结构": {"PORT_FORWARD_CONFIG": "描述: 端口转发配置结构体\n      字段:\n        - 名称: localAddress\n          类型: char[64]\n          描述: 本地监听地址\n        - 名称: localPort\n          类型: uint16_t\n          描述: 本地监听端口\n        - 名称: remoteAddress\n          类型: char[64]\n          描述: 远程目标地址\n        - 名称: remotePort\n          类型: uint16_t\n          描述: 远程目标端口\n        - 名称: forwardType\n          类型: uint8_t\n          描述: 转发类型(TCP/UDP)\n        - 名称: timeout\n          类型: uint32_t\n          描述: 连接超时时间(秒)\n        - 名称: maxConnections\n          类型: uint16_t\n          描述: 最大并发连接数\n        - 名称: description\n          类型: char[128]\n          描述: 规则描述信息", "PORT_FORWARD_INFO": "描述: 端口转发规则信息结构体\n      字段:\n        - 名称: ruleId\n          类型: uint32_t\n          描述: 规则ID\n        - 名称: localAddress\n          类型: char[64]\n          描述: 本地监听地址\n        - 名称: localPort\n          类型: uint16_t\n          描述: 本地监听端口\n        - 名称: remoteAddress\n          类型: char[64]\n          描述: 远程目标地址\n        - 名称: remotePort\n          类型: uint16_t\n          描述: 远程目标端口\n        - 名称: forwardType\n          类型: uint8_t\n          描述: 转发类型\n        - 名称: active\n          类型: bool\n          描述: 规则是否激活\n        - 名称: currentConnections\n          类型: uint16_t\n          描述: 当前活跃连接数\n        - 名称: bytesForwarded\n          类型: uint64_t\n          描述: 已转发字节数", "FORWARD_TYPES": "描述: 转发类型枚举\n      字段:\n        - 名称: FORWARD_TYPE_TCP\n          类型: uint8_t\n          描述: TCP转发，值为0x01\n        - 名称: FORWARD_TYPE_UDP\n          类型: uint8_t\n          描述: UDP转发，值为0x02"}}