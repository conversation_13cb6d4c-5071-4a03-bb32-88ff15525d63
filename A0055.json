{"示例代码": {"暂停端口转发示例": "int main() {\n    // 初始化端口转发连接管理器\n    PortForwardConnector connector;\n    \n    // 获取当前活动的端口转发连接列表\n    FORWARD_CONNECTION_ID connections[10];\n    uint8_t connectionCount = 0;\n    \n    if (connector.ListActiveConnections(connections, 10, &connectionCount)) {\n        printf(\"当前活动的端口转发连接数量: %d\\n\", connectionCount);\n        \n        // 遍历显示所有活动连接\n        for (int i = 0; i < connectionCount; i++) {\n            FORWARD_CONNECTION_INFO info;\n            if (connector.GetConnectionInfo(connections[i], &info)) {\n                printf(\"%d. 连接ID %u: %s:%d -> %s:%d (状态: %s)\\n\", i + 1,\n                       info.connectionId, info.localHost, info.localPort, \n                       info.remoteHost, info.remotePort,\n                       info.status == CONNECTION_STATUS_ACTIVE ? \"活动\" : \n                       info.status == CONNECTION_STATUS_PAUSED ? \"暂停\" : \"其他\");\n            }\n        }\n        \n        // 选择要暂停的连接ID\n        if (connectionCount > 0) {\n            // 以第一个连接为例\n            FORWARD_CONNECTION_ID connectionToPause = connections[0];\n            \n            // 检查连接状态，确保连接处于活动状态\n            FORWARD_CONNECTION_INFO info;\n            if (connector.GetConnectionInfo(connectionToPause, &info) && \n                info.status == CONNECTION_STATUS_ACTIVE) {\n                \n                printf(\"\\n准备暂停连接ID: %u\\n\", connectionToPause);\n                \n                // 配置暂停选项\n                PAUSE_OPTIONS options;\n                memset(&options, 0, sizeof(PAUSE_OPTIONS));\n                options.keepAlive = true;        // 保持连接活跃，但暂停数据传输\n                options.notifyPeers = true;      // 通知对端连接将暂停\n                options.maxPauseDuration = 3600; // 最大暂停时长(秒)，0表示无限制\n                \n                // 执行暂停操作\n                if (connector.PauseForward(connectionToPause, &options)) {\n                    printf(\"端口转发连接已成功暂停\\n\");\n                    printf(\"连接将保持活跃状态，但暂停数据传输\\n\");\n                    \n                    // 等待一段时间后恢复连接\n                    printf(\"将在10秒后尝试恢复连接...\\n\");\n                    sleep(10);\n                    \n                    // 恢复连接\n                    if (connector.ResumeForward(connectionToPause)) {\n                        printf(\"端口转发连接已恢复\\n\");\n                    } else {\n                        printf(\"恢复端口转发连接失败: %s\\n\", connector.GetLastError());\n                    }\n                } else {\n                    printf(\"暂停端口转发连接失败: %s\\n\", connector.GetLastError());\n                    return 1;\n                }\n            } else {\n                printf(\"连接不处于活动状态，无法暂停\\n\");\n            }\n        } else {\n            printf(\"没有活动的端口转发连接\\n\");\n        }\n    } else {\n        printf(\"获取活动连接列表失败: %s\\n\", connector.GetLastError());\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"ListActiveConnections": "功能: 获取当前活动的端口转发连接列表\n      参数:\n        - 名称: connections\n          类型: FORWARD_CONNECTION_ID*\n          描述: 连接ID数组\n        - 名称: maxCount\n          类型: uint8_t\n          描述: 数组最大容量\n        - 名称: count\n          类型: uint8_t*\n          描述: 返回实际连接数量\n      返回值:\n        类型: bool\n        描述: 是否成功获取连接列表\n      示例: connector.ListActiveConnections(connections, 10, &connectionCount)", "GetConnectionInfo": "功能: 获取连接详细信息\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: info\n          类型: FORWARD_CONNECTION_INFO*\n          描述: 连接信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取连接信息\n      示例: connector.GetConnectionInfo(connectionToPause, &info)", "PauseForward": "功能: 暂停端口转发连接\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: options\n          类型: PAUSE_OPTIONS*\n          描述: 暂停选项结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功暂停连接\n      示例: connector.PauseForward(connectionToPause, &options)", "ResumeForward": "功能: 恢复暂停的端口转发连接\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n      返回值:\n        类型: bool\n        描述: 是否成功恢复连接\n      示例: connector.ResumeForward(connectionToPause)", "GetLastError": "功能: 获取上一操作的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: connector.GetLastError()"}, "数据结构": {"PAUSE_OPTIONS": "描述: 暂停连接选项结构体\n      字段:\n        - 名称: keepAlive\n          类型: bool\n          描述: 是否保持连接活跃\n        - 名称: notifyPeers\n          类型: bool\n          描述: 是否通知对端连接将暂停\n        - 名称: maxPauseDuration\n          类型: uint32_t\n          描述: 最大暂停时长(秒)，0表示无限制\n        - 名称: pauseReason\n          类型: uint8_t\n          描述: 暂停原因代码", "CONNECTION_STATUS": "描述: 连接状态枚举\n      字段:\n        - 名称: CONNECTION_STATUS_INACTIVE\n          类型: uint8_t\n          描述: 连接未激活，值为0x00\n        - 名称: CONNECTION_STATUS_ACTIVE\n          类型: uint8_t\n          描述: 连接活动中，值为0x01\n        - 名称: CONNECTION_STATUS_PAUSED\n          类型: uint8_t\n          描述: 连接已暂停，值为0x02\n        - 名称: CONNECTION_STATUS_ERROR\n          类型: uint8_t\n          描述: 连接出错，值为0x03\n        - 名称: CONNECTION_STATUS_CLOSING\n          类型: uint8_t\n          描述: 连接正在关闭，值为0x04", "PAUSE_RESULT": "描述: 暂停操作结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 暂停操作是否成功\n        - 名称: pauseTime\n          类型: uint64_t\n          描述: 暂停时间戳\n        - 名称: notificationSent\n          类型: bool\n          描述: 是否成功发送通知\n        - 名称: errorCode\n          类型: uint16_t\n          描述: 错误代码(如果失败)"}}