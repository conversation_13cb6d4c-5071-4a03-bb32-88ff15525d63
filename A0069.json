{"示例代码": {"使用TCP扫描示例": "// Linux系统中使用C语言实现TCP端口扫描\n#include <stdio.h>\n#include <stdlib.h>\n#include <string.h>\n#include <unistd.h>\n#include <errno.h>\n#include <sys/socket.h>\n#include <netinet/in.h>\n#include <arpa/inet.h>\n#include <fcntl.h>\n#include <netdb.h>\n#include <sys/time.h>\n\n#define MAX_PORT 65535\n#define MIN_PORT 1\n#define DEFAULT_TIMEOUT_SEC 2\n#define DEFAULT_TIMEOUT_USEC 0\n\n// 扫描单个TCP端口\nint scan_tcp_port(const char *target_ip, int port, int timeout_sec) {\n    int sock;\n    struct sockaddr_in addr;\n    fd_set fdset;\n    struct timeval tv;\n    int res;\n    \n    // 创建套接字\n    sock = socket(AF_INET, SOCK_STREAM, 0);\n    if (sock < 0) {\n        perror(\"socket创建失败\");\n        return -1;\n    }\n    \n    // 设置非阻塞模式\n    fcntl(sock, F_SETFL, O_NONBLOCK);\n    \n    // 准备连接信息\n    memset(&addr, 0, sizeof(addr));\n    addr.sin_family = AF_INET;\n    addr.sin_port = htons(port);\n    addr.sin_addr.s_addr = inet_addr(target_ip);\n    \n    // 尝试连接\n    connect(sock, (struct sockaddr*)&addr, sizeof(addr));\n    \n    // 配置select超时\n    FD_ZERO(&fdset);\n    FD_SET(sock, &fdset);\n    tv.tv_sec = timeout_sec;\n    tv.tv_usec = 0;\n    \n    // 使用select等待连接结果\n    res = select(sock + 1, NULL, &fdset, NULL, &tv);\n    \n    // 关闭套接字\n    close(sock);\n    \n    // 解析结果\n    if (res == 1) {\n        // 如果select返回1，还需检查是否真正连接成功\n        int so_error;\n        socklen_t len = sizeof(so_error);\n        getsockopt(sock, SOL_SOCKET, SO_ERROR, &so_error, &len);\n        if (so_error == 0) {\n            // 端口开放\n            return 0;\n        }\n    }\n    \n    // 端口关闭或无法连接\n    return -1;\n}\n\n// 扫描目标IP的端口范围\nvoid scan_port_range(const char *target_ip, int start_port, int end_port) {\n    printf(\"开始扫描目标 %s 的端口范围 %d-%d...\\n\", target_ip, start_port, end_port);\n    \n    for (int port = start_port; port <= end_port; port++) {\n        if (scan_tcp_port(target_ip, port, DEFAULT_TIMEOUT_SEC) == 0) {\n            printf(\"[+] 端口 %d 开放(TCP)\\n\", port);\n            \n            // 尝试识别常见服务\n            struct servent *service = getservbyport(htons(port), \"tcp\");\n            if (service) {\n                printf(\"    服务: %s\\n\", service->s_name);\n            }\n        }\n    }\n    \n    printf(\"扫描完成\\n\");\n}\n\nint main(int argc, char *argv[]) {\n    if (argc < 2) {\n        printf(\"用法: %s <目标IP> [起始端口] [结束端口]\\n\", argv[0]);\n        return 1;\n    }\n    \n    const char *target_ip = argv[1];\n    int start_port = (argc > 2) ? atoi(argv[2]) : 1;\n    int end_port = (argc > 3) ? atoi(argv[3]) : 1024;\n    \n    // 校验端口范围\n    if (start_port < MIN_PORT || start_port > MAX_PORT ||\n        end_port < MIN_PORT || end_port > MAX_PORT ||\n        start_port > end_port) {\n        printf(\"无效的端口范围. 使用范围: %d-%d\\n\", MIN_PORT, MAX_PORT);\n        return 1;\n    }\n    \n    scan_port_range(target_ip, start_port, end_port);\n    return 0;\n}"}, "API接口": {"ScanTCPPort": "功能: 扫描单个TCP端口状态\n      参数:\n        - 名称: target_ip\n          类型: const char*\n          描述: 目标IP地址\n        - 名称: port\n          类型: int\n          描述: 要扫描的端口号\n        - 名称: timeout_ms\n          类型: int\n          描述: 连接超时时间(毫秒)\n      返回值:\n        类型: int\n        描述: 0=端口开放, -1=端口关闭, -2=扫描出错\n      示例: int result = ScanTCPPort(\"***********\", 80, 1000)", "ScanTCPPortRange": "功能: 扫描连续的TCP端口范围\n      参数:\n        - 名称: target_ip\n          类型: const char*\n          描述: 目标IP地址\n        - 名称: start_port\n          类型: int\n          描述: 起始端口\n        - 名称: end_port\n          类型: int\n          描述: 结束端口\n        - 名称: timeout_ms\n          类型: int\n          描述: 每个端口连接超时(毫秒)\n      返回值:\n        类型: PortScanResult*\n        描述: 扫描结果结构体\n      示例: PortScanResult* results = ScanTCPPortRange(\"***********\", 1, 1024, 500)", "ScanTCPPortList": "功能: 扫描离散的TCP端口列表\n      参数:\n        - 名称: target_ip\n          类型: const char*\n          描述: 目标IP地址\n        - 名称: ports\n          类型: const int*\n          描述: 端口数组\n        - 名称: port_count\n          类型: int\n          描述: 端口数量\n        - 名称: timeout_ms\n          类型: int\n          描述: 每个端口连接超时(毫秒)\n      返回值:\n        类型: PortScanResult*\n        描述: 扫描结果结构体\n      示例: int common_ports[] = {21, 22, 23, 25, 80, 443, 3306, 8080};\nPortScanResult* results = ScanTCPPortList(\"***********\", common_ports, 8, 500)"}, "数据结构": {"PortStatus": "描述: 端口状态结构体\n      字段:\n        - 名称: port\n          类型: int\n          描述: 端口号\n        - 名称: is_open\n          类型: bool\n          描述: 端口是否开放\n        - 名称: service_name\n          类型: char[32]\n          描述: 识别到的服务名称，如未识别则为空\n        - 名称: response_time_ms\n          类型: int\n          描述: 连接响应时间(毫秒)\n        - 名称: banner\n          类型: char[256]\n          描述: 服务banner信息(如果有)", "PortScanResult": "描述: TCP端口扫描结果结构体\n      字段:\n        - 名称: target_ip\n          类型: char[64]\n          描述: 目标IP地址\n        - 名称: scan_time\n          类型: time_t\n          描述: 扫描时间\n        - 名称: total_ports\n          类型: int\n          描述: 扫描的总端口数\n        - 名称: open_ports\n          类型: int\n          描述: 开放的端口数量\n        - 名称: port_status\n          类型: PortStatus[]\n          描述: 端口状态数组", "ScanOptions": "描述: 扫描选项结构体\n      字段:\n        - 名称: timeout_ms\n          类型: int\n          描述: 连接超时(毫秒)\n        - 名称: retry_count\n          类型: int\n          描述: 重试次数\n        - 名称: parallel_scans\n          类型: int\n          描述: 并行扫描数量\n        - 名称: get_banners\n          类型: bool\n          描述: 是否获取服务banner信息\n        - 名称: stealth_scan\n          类型: bool\n          描述: 是否使用SYN扫描(半开扫描)"}}