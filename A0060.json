{"示例代码": {"模拟用户行为示例": "int main() {\n    // 初始化用户行为模拟器\n    UserBehaviorSimulator simulator;\n    \n    // 创建模拟配置\n    BEHAVIOR_SIMULATION_CONFIG config;\n    memset(&config, 0, sizeof(BEHAVIOR_SIMULATION_CONFIG));\n    \n    // 设置基本配置\n    config.humanlikeDelay = true;       // 使用类人延迟\n    config.randomizeBehavior = true;     // 行为随机化\n    config.logSimulation = true;         // 记录模拟活动\n    strcpy(config.logPath, \"/var/log/simulation.log\");\n    \n    // 初始化模拟器\n    if (!simulator.Initialize(&config)) {\n        printf(\"初始化用户行为模拟器失败: %s\\n\", simulator.GetLastError());\n        return 1;\n    }\n    \n    printf(\"用户行为模拟器已初始化\\n\");\n    \n    // 创建模拟会话\n    SIMULATION_SESSION_ID sessionId;\n    if (!simulator.CreateSession(&sessionId)) {\n        printf(\"创建模拟会话失败\\n\");\n        return 1;\n    }\n    \n    printf(\"已创建模拟会话，ID: %u\\n\", sessionId);\n    \n    // 添加模拟行为 - 鼠标移动\n    MOUSE_MOVEMENT_BEHAVIOR mouseBehavior;\n    memset(&mouseBehavior, 0, sizeof(MOUSE_MOVEMENT_BEHAVIOR));\n    \n    // 配置鼠标行为\n    mouseBehavior.behaviorType = BEHAVIOR_MOUSE_MOVE;\n    mouseBehavior.startX = 100;\n    mouseBehavior.startY = 100;\n    mouseBehavior.endX = 500;\n    mouseBehavior.endY = 300;\n    mouseBehavior.duration = 1500;       // 1.5秒内完成移动\n    mouseBehavior.smoothingFactor = 0.8; // 平滑因子\n    \n    BEHAVIOR_ID mouseBehaviorId;\n    if (simulator.AddBehavior(sessionId, &mouseBehavior, sizeof(mouseBehavior), &mouseBehaviorId)) {\n        printf(\"已添加鼠标移动行为，ID: %u\\n\", mouseBehaviorId);\n    }\n    \n    // 添加模拟行为 - 鼠标点击\n    MOUSE_CLICK_BEHAVIOR clickBehavior;\n    memset(&clickBehavior, 0, sizeof(MOUSE_CLICK_BEHAVIOR));\n    \n    // 配置点击行为\n    clickBehavior.behaviorType = BEHAVIOR_MOUSE_CLICK;\n    clickBehavior.x = 500;\n    clickBehavior.y = 300;\n    clickBehavior.button = MOUSE_BUTTON_LEFT;\n    clickBehavior.doubleClick = false;\n    clickBehavior.holdDuration = 120;    // 按下120毫秒\n    \n    BEHAVIOR_ID clickBehaviorId;\n    if (simulator.AddBehavior(sessionId, &clickBehavior, sizeof(clickBehavior), &clickBehaviorId)) {\n        printf(\"已添加鼠标点击行为，ID: %u\\n\", clickBehaviorId);\n    }\n    \n    // 添加模拟行为 - 键盘输入\n    KEYBOARD_INPUT_BEHAVIOR keyboardBehavior;\n    memset(&keyboardBehavior, 0, sizeof(KEYBOARD_INPUT_BEHAVIOR));\n    \n    // 配置键盘行为\n    keyboardBehavior.behaviorType = BEHAVIOR_KEYBOARD_INPUT;\n    strcpy(keyboardBehavior.text, \"Hello, this is a simulated typing test.\");\n    keyboardBehavior.typingSpeed = 150;    // 每分钟150字符\n    keyboardBehavior.errorRate = 0.05;     // 5%的输入错误率\n    keyboardBehavior.autoCorrect = true;   // 自动纠错\n    \n    BEHAVIOR_ID keyboardBehaviorId;\n    if (simulator.AddBehavior(sessionId, &keyboardBehavior, sizeof(keyboardBehavior), &keyboardBehaviorId)) {\n        printf(\"已添加键盘输入行为，ID: %u\\n\", keyboardBehaviorId);\n    }\n    \n    // 添加随机延迟行为\n    DELAY_BEHAVIOR delayBehavior;\n    memset(&delayBehavior, 0, sizeof(DELAY_BEHAVIOR));\n    \n    // 配置延迟行为\n    delayBehavior.behaviorType = BEHAVIOR_DELAY;\n    delayBehavior.minDelayMs = 500;      // 最小延迟500毫秒\n    delayBehavior.maxDelayMs = 2000;     // 最大延迟2秒\n    \n    BEHAVIOR_ID delayBehaviorId;\n    if (simulator.AddBehavior(sessionId, &delayBehavior, sizeof(delayBehavior), &delayBehaviorId)) {\n        printf(\"已添加随机延迟行为，ID: %u\\n\", delayBehaviorId);\n    }\n    \n    // 开始执行模拟会话\n    printf(\"开始执行模拟行为序列...\\n\");\n    if (simulator.StartSession(sessionId)) {\n        // 等待模拟完成\n        while (simulator.IsSessionActive(sessionId)) {\n            SIMULATION_PROGRESS progress;\n            simulator.GetSessionProgress(sessionId, &progress);\n            printf(\"模拟进度: %d%%\\r\", progress.percentComplete);\n            fflush(stdout);\n            usleep(500000); // 每0.5秒更新一次进度\n        }\n        \n        printf(\"\\n模拟会话已完成\\n\");\n        \n        // 获取模拟结果\n        SIMULATION_RESULT result;\n        if (simulator.GetSessionResult(sessionId, &result)) {\n            printf(\"模拟结果:\\n\");\n            printf(\"总执行时间: %u 毫秒\\n\", result.totalExecutionTimeMs);\n            printf(\"成功行为数: %u\\n\", result.successfulBehaviors);\n            printf(\"失败行为数: %u\\n\", result.failedBehaviors);\n        }\n        \n        // 导出模拟会话报告\n        if (simulator.ExportSessionReport(sessionId, \"/tmp/simulation_report.json\")) {\n            printf(\"已导出模拟报告到 /tmp/simulation_report.json\\n\");\n        }\n    } else {\n        printf(\"启动模拟会话失败: %s\\n\", simulator.GetLastError());\n    }\n    \n    // 清理资源\n    simulator.CloseSession(sessionId);\n    printf(\"已关闭模拟会话\\n\");\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化用户行为模拟器\n      参数:\n        - 名称: config\n          类型: BEHAVIOR_SIMULATION_CONFIG*\n          描述: 模拟配置结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功初始化\n      示例: simulator.Initialize(&config)", "CreateSession": "功能: 创建模拟会话\n      参数:\n        - 名称: sessionId\n          类型: SIMULATION_SESSION_ID*\n          描述: 返回的会话ID指针\n      返回值:\n        类型: bool\n        描述: 是否成功创建会话\n      示例: simulator.CreateSession(&sessionId)", "AddBehavior": "功能: 向模拟会话添加行为\n      参数:\n        - 名称: sessionId\n          类型: SIMULATION_SESSION_ID\n          描述: 会话ID\n        - 名称: behavior\n          类型: void*\n          描述: 行为结构体指针\n        - 名称: behaviorSize\n          类型: size_t\n          描述: 行为结构体大小\n        - 名称: behaviorId\n          类型: BEHAVIOR_ID*\n          描述: 返回的行为ID指针\n      返回值:\n        类型: bool\n        描述: 是否成功添加行为\n      示例: simulator.AddBehavior(sessionId, &mouseBehavior, sizeof(mouseBehavior), &mouseBehaviorId)", "StartSession": "功能: 开始执行模拟会话\n      参数:\n        - 名称: sessionId\n          类型: SIMULATION_SESSION_ID\n          描述: 会话ID\n      返回值:\n        类型: bool\n        描述: 是否成功启动会话\n      示例: simulator.StartSession(sessionId)", "IsSessionActive": "功能: 检查模拟会话是否处于活动状态\n      参数:\n        - 名称: sessionId\n          类型: SIMULATION_SESSION_ID\n          描述: 会话ID\n      返回值:\n        类型: bool\n        描述: 会话是否活动\n      示例: simulator.IsSessionActive(sessionId)", "GetSessionProgress": "功能: 获取模拟会话进度\n      参数:\n        - 名称: sessionId\n          类型: SIMULATION_SESSION_ID\n          描述: 会话ID\n        - 名称: progress\n          类型: SIMULATION_PROGRESS*\n          描述: 进度信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取进度\n      示例: simulator.GetSessionProgress(sessionId, &progress)", "GetSessionResult": "功能: 获取模拟会话结果\n      参数:\n        - 名称: sessionId\n          类型: SIMULATION_SESSION_ID\n          描述: 会话ID\n        - 名称: result\n          类型: SIMULATION_RESULT*\n          描述: 结果信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取结果\n      示例: simulator.GetSessionResult(sessionId, &result)", "CloseSession": "功能: 关闭模拟会话\n      参数:\n        - 名称: sessionId\n          类型: SIMULATION_SESSION_ID\n          描述: 会话ID\n      返回值:\n        类型: bool\n        描述: 是否成功关闭会话\n      示例: simulator.CloseSession(sessionId)"}, "数据结构": {"BEHAVIOR_SIMULATION_CONFIG": "描述: 用户行为模拟配置结构体\n      字段:\n        - 名称: humanlikeDelay\n          类型: bool\n          描述: 是否使用类人延迟\n        - 名称: randomizeBehavior\n          类型: bool\n          描述: 是否随机化行为\n        - 名称: logSimulation\n          类型: bool\n          描述: 是否记录模拟活动\n        - 名称: logPath\n          类型: char[256]\n          描述: 日志文件路径\n        - 名称: maxConcurrentBehaviors\n          类型: uint8_t\n          描述: 最大并发行为数", "MOUSE_MOVEMENT_BEHAVIOR": "描述: 鼠标移动行为结构体\n      字段:\n        - 名称: behaviorType\n          类型: uint8_t\n          描述: 行为类型\n        - 名称: startX\n          类型: uint16_t\n          描述: 起始X坐标\n        - 名称: startY\n          类型: uint16_t\n          描述: 起始Y坐标\n        - 名称: endX\n          类型: uint16_t\n          描述: 结束X坐标\n        - 名称: endY\n          类型: uint16_t\n          描述: 结束Y坐标\n        - 名称: duration\n          类型: uint32_t\n          描述: 移动持续时间(毫秒)\n        - 名称: smoothingFactor\n          类型: float\n          描述: 平滑因子(0-1)", "MOUSE_CLICK_BEHAVIOR": "描述: 鼠标点击行为结构体\n      字段:\n        - 名称: behaviorType\n          类型: uint8_t\n          描述: 行为类型\n        - 名称: x\n          类型: uint16_t\n          描述: 点击X坐标\n        - 名称: y\n          类型: uint16_t\n          描述: 点击Y坐标\n        - 名称: button\n          类型: uint8_t\n          描述: 鼠标按钮\n        - 名称: doubleClick\n          类型: bool\n          描述: 是否双击\n        - 名称: holdDuration\n          类型: uint32_t\n          描述: 按住持续时间(毫秒)", "KEYBOARD_INPUT_BEHAVIOR": "描述: 键盘输入行为结构体\n      字段:\n        - 名称: behaviorType\n          类型: uint8_t\n          描述: 行为类型\n        - 名称: text\n          类型: char[1024]\n          描述: 要输入的文本\n        - 名称: typingSpeed\n          类型: uint16_t\n          描述: 打字速度(字符/分钟)\n        - 名称: errorRate\n          类型: float\n          描述: 输入错误率(0-1)\n        - 名称: autoCorrect\n          类型: bool\n          描述: 是否自动纠错"}}