{"示例代码": {"发送系统信息示例": "int main() {\n    // 初始化系统信息发送器\n    SystemInfoSender sender;\n    \n    // 获取系统信息\n    SYSTEM_INFO_DATA sysInfo;\n    SystemInfoCollector collector;\n    \n    if (!collector.GatherSystemInfo(&sysInfo)) {\n        printf(\"系统信息收集失败\\n\");\n        return 1;\n    }\n    \n    // 压缩并加密系统信息\n    ENCRYPTED_DATA encData;\n    if (!sender.CompressAndEncrypt(&sysInfo, &encData)) {\n        printf(\"系统信息压缩加密失败\\n\");\n        return 1;\n    }\n    \n    // 发送系统信息到控制端\n    if (sender.SendSystemInfo(SERVER_ADDRESS, SERVER_PORT, &encData)) {\n        printf(\"系统信息发送成功\\n\");\n    } else {\n        printf(\"系统信息发送失败\\n\");\n        return 1;\n    }\n    \n    // 释放加密数据\n    sender.FreeEncryptedData(&encData);\n    \n    return 0;\n}"}, "API接口": {"CompressAndEncrypt": "功能: 压缩并加密系统信息数据\n      参数:\n        - 名称: sysInfo\n          类型: SYSTEM_INFO_DATA*\n          描述: 系统信息结构体指针\n        - 名称: encData\n          类型: ENCRYPTED_DATA*\n          描述: 加密后数据的输出结构体\n      返回值:\n        类型: bool\n        描述: 压缩加密是否成功\n      示例: sender.CompressAndEncrypt(&sysInfo, &encData)", "SendSystemInfo": "功能: 将系统信息发送到控制端\n      参数:\n        - 名称: serverAddr\n          类型: const char*\n          描述: 服务器地址\n        - 名称: serverPort\n          类型: uint16_t\n          描述: 服务器端口\n        - 名称: encData\n          类型: ENCRYPTED_DATA*\n          描述: 加密的系统信息数据\n      返回值:\n        类型: bool\n        描述: 发送是否成功\n      示例: sender.SendSystemInfo(SERVER_ADDRESS, SERVER_PORT, &encData)", "FreeEncryptedData": "功能: 释放加密数据占用的内存\n      参数:\n        - 名称: encData\n          类型: ENCRYPTED_DATA*\n          描述: 加密数据结构体指针\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: sender.FreeEncryptedData(&encData)"}, "数据结构": {"SYSTEM_INFO_DATA": "描述: 系统信息数据结构\n      字段:\n        - 名称: osVersion\n          类型: char[256]\n          描述: 操作系统版本信息\n        - 名称: kernelVersion\n          类型: char[128]\n          描述: 内核版本信息\n        - 名称: hostname\n          类型: char[64]\n          描述: 主机名\n        - 名称: cpuModel\n          类型: char[256]\n          描述: CPU型号信息\n        - 名称: cpuCores\n          类型: int\n          描述: CPU核心数\n        - 名称: totalMemory\n          类型: uint64_t\n          描述: 系统总内存(字节)\n        - 名称: networkInterfaces\n          类型: NETWORK_INTERFACE[8]\n          描述: 网络接口信息", "ENCRYPTED_DATA": "描述: 加密后的数据结构\n      字段:\n        - 名称: data\n          类型: unsigned char*\n          描述: 加密数据缓冲区\n        - 名称: dataSize\n          类型: uint32_t\n          描述: 数据大小\n        - 名称: checksum\n          类型: uint32_t\n          描述: 数据校验和\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 加密时间戳"}}