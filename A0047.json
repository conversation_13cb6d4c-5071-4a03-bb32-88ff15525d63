{"示例代码": {"接收受控端更新数据示例": "int main() {\n    // 初始化更新数据接收器\n    UpdateDataReceiver receiver;\n    \n    // 接收到更新指令后，准备接收更新数据\n    if (!receiver.PrepareForUpdate()) {\n        printf(\"准备接收更新失败\\n\");\n        return 1;\n    }\n    \n    // 回复控制端已准备好接收数据\n    receiver.SendReadySignal();\n    \n    // 创建临时文件存储接收的更新数据\n    FILE* updateFile = fopen(\"/tmp/client_update.bin\", \"wb\");\n    if (!updateFile) {\n        printf(\"创建临时更新文件失败\\n\");\n        return 1;\n    }\n    \n    // 循环接收更新数据块\n    UPDATE_DATA_BLOCK dataBlock;\n    uint64_t totalReceived = 0;\n    uint64_t expectedSize = receiver.GetExpectedUpdateSize();\n    \n    printf(\"开始接收更新数据，总大小: %lu 字节\\n\", expectedSize);\n    \n    while (receiver.ReceiveUpdateDataBlock(&dataBlock)) {\n        // 验证数据块\n        if (!receiver.VerifyDataBlock(&dataBlock)) {\n            printf(\"数据块验证失败，请求重传\\n\");\n            receiver.RequestBlockRetransmission(dataBlock.blockId);\n            continue;\n        }\n        \n        // 将数据块写入文件\n        fwrite(dataBlock.data, 1, dataBlock.dataSize, updateFile);\n        totalReceived += dataBlock.dataSize;\n        \n        printf(\"接收进度: %lu / %lu 字节 (%.2f%%)\\n\", \n               totalReceived, expectedSize, (float)totalReceived * 100 / expectedSize);\n        \n        // 释放数据块资源\n        receiver.FreeDataBlock(&dataBlock);\n        \n        // 检查是否接收完成\n        if (totalReceived >= expectedSize) {\n            break;\n        }\n    }\n    \n    // 关闭文件\n    fclose(updateFile);\n    \n    // 完成更新数据接收\n    if (totalReceived == expectedSize) {\n        printf(\"更新数据接收完成\\n\");\n        receiver.FinalizeUpdate(\"/tmp/client_update.bin\");\n        return 0;\n    } else {\n        printf(\"更新数据接收不完整\\n\");\n        return 1;\n    }\n}"}, "API接口": {"PrepareForUpdate": "功能: 准备接收更新数据\n      参数:\n      返回值:\n        类型: bool\n        描述: 准备工作是否成功\n      示例: receiver.PrepareForUpdate()", "SendReadySignal": "功能: 向控制端发送准备就绪信号\n      参数:\n      返回值:\n        类型: bool\n        描述: 信号是否成功发送\n      示例: receiver.SendReadySignal()", "ReceiveUpdateDataBlock": "功能: 接收单个更新数据块\n      参数:\n        - 名称: dataBlock\n          类型: UPDATE_DATA_BLOCK*\n          描述: 数据块结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功接收数据块\n      示例: receiver.ReceiveUpdateDataBlock(&dataBlock)", "VerifyDataBlock": "功能: 验证数据块完整性\n      参数:\n        - 名称: dataBlock\n          类型: UPDATE_DATA_BLOCK*\n          描述: 数据块结构体指针\n      返回值:\n        类型: bool\n        描述: 数据块是否验证通过\n      示例: receiver.VerifyDataBlock(&dataBlock)", "RequestBlockRetransmission": "功能: 请求重传特定数据块\n      参数:\n        - 名称: blockId\n          类型: uint32_t\n          描述: 需要重传的数据块ID\n      返回值:\n        类型: bool\n        描述: 重传请求是否成功发送\n      示例: receiver.RequestBlockRetransmission(dataBlock.blockId)", "FreeDataBlock": "功能: 释放数据块资源\n      参数:\n        - 名称: dataBlock\n          类型: UPDATE_DATA_BLOCK*\n          描述: 数据块结构体指针\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: receiver.FreeDataBlock(&dataBlock)", "GetExpectedUpdateSize": "功能: 获取预期的更新数据总大小\n      参数:\n      返回值:\n        类型: uint64_t\n        描述: 更新数据的总字节数\n      示例: uint64_t expectedSize = receiver.GetExpectedUpdateSize()", "FinalizeUpdate": "功能: 完成更新数据接收\n      参数:\n        - 名称: updateFilePath\n          类型: const char*\n          描述: 更新文件路径\n      返回值:\n        类型: bool\n        描述: 更新接收是否成功完成\n      示例: receiver.FinalizeUpdate(\"/tmp/client_update.bin\")"}, "数据结构": {"UPDATE_DATA_BLOCK": "描述: 更新数据块结构体\n      字段:\n        - 名称: blockId\n          类型: uint32_t\n          描述: 数据块ID\n        - 名称: sessionId\n          类型: uint32_t\n          描述: 所属会话ID\n        - 名称: dataSize\n          类型: uint16_t\n          描述: 数据块大小\n        - 名称: data\n          类型: unsigned char*\n          描述: 数据块内容\n        - 名称: checksum\n          类型: uint32_t\n          描述: 数据校验和"}}