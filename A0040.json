{"示例代码": {"显式执行命令示例": "int main() {\n    // 初始化命令执行器\n    CommandExecutor executor;\n    \n    // 从控制端接收到命令数据\n    COMMAND_DATA cmdData;\n    // 假设cmdData已经通过网络接收并填充\n    \n    // 显式执行命令（在可见命令行窗口中执行）\n    if (executor.ExecuteVisibleCommand(&cmdData)) {\n        printf(\"命令执行成功，窗口可见\\n\");\n    } else {\n        printf(\"命令执行失败\\n\");\n        return 1;\n    }\n    \n    // 等待命令执行完成并获取结果\n    COMMAND_RESULT cmdResult;\n    if (executor.GetCommandResult(&cmdResult)) {\n        printf(\"命令输出: %s\\n\", cmdResult.output);\n        printf(\"退出代码: %d\\n\", cmdResult.exitCode);\n    }\n    \n    return 0;\n}"}, "API接口": {"ExecuteVisibleCommand": "功能: 在可见的命令行窗口中执行命令\n      参数:\n        - 名称: cmdData\n          类型: COMMAND_DATA*\n          描述: 要执行的命令数据结构\n      返回值:\n        类型: bool\n        描述: 命令是否成功启动\n      示例: executor.ExecuteVisibleCommand(&cmdData)", "GetCommandResult": "功能: 获取已执行命令的结果\n      参数:\n        - 名称: result\n          类型: COMMAND_RESULT*\n          描述: 用于存储命令执行结果的结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取结果\n      示例: executor.GetCommandResult(&cmdResult)", "SetTerminalTitle": "功能: 设置命令行窗口的标题\n      参数:\n        - 名称: title\n          类型: const char*\n          描述: 窗口标题文本\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: executor.SetTerminalTitle(\"命令执行窗口\")"}, "数据结构": {"COMMAND_DATA": "描述: 命令数据结构\n      字段:\n        - 名称: type\n          类型: BYTE\n          描述: 命令类型标识\n        - 名称: command\n          类型: char[1024]\n          描述: 命令文本内容\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 命令时间戳\n        - 名称: commandId\n          类型: uint32_t\n          描述: 命令唯一标识符", "COMMAND_RESULT": "描述: 命令执行结果结构\n      字段:\n        - 名称: exitCode\n          类型: int\n          描述: 命令执行的退出代码\n        - 名称: output\n          类型: char[8192]\n          描述: 命令的输出内容\n        - 名称: error\n          类型: char[2048]\n          描述: 命令的错误输出\n        - 名称: executionTime\n          类型: uint32_t\n          描述: 命令执行所用时间(毫秒)"}}