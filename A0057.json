{"示例代码": {"网络流量统计示例": "int main() {\n    // 初始化流量统计管理器\n    TrafficStatisticsManager statsManager;\n    \n    // 配置统计参数\n    TRAFFIC_STATS_CONFIG config;\n    memset(&config, 0, sizeof(TRAFFIC_STATS_CONFIG));\n    \n    // 设置统计选项\n    config.enableRealTimeMonitoring = true;   // 启用实时监控\n    config.sampleIntervalMs = 1000;          // 每秒采样一次\n    config.persistStatistics = true;         // 持久化存储统计数据\n    config.logFilePath = \"/var/log/portforward/traffic_stats.log\";\n    config.aggregateByRule = true;           // 按照转发规则聚合统计\n    \n    // 初始化统计模块\n    if (!statsManager.Initialize(&config)) {\n        printf(\"初始化流量统计模块失败: %s\\n\", statsManager.GetLastError());\n        return 1;\n    }\n    \n    printf(\"流量统计模块已初始化，开始收集数据...\\n\");\n    \n    // 获取当前活动的转发规则列表\n    FORWARD_RULE_ID rules[10];\n    uint8_t ruleCount = 0;\n    \n    PortForwardManager forwardManager;\n    if (forwardManager.GetConfiguredRules(rules, 10, &ruleCount)) {\n        // 为每个规则启用流量统计\n        for (int i = 0; i < ruleCount; i++) {\n            if (statsManager.EnableStatsForRule(rules[i])) {\n                printf(\"已为规则ID %u 启用流量统计\\n\", rules[i]);\n            }\n        }\n    }\n    \n    // 创建接收统计报告的回调函数\n    statsManager.SetStatsCallback(trafficStatsCallback, NULL);\n    \n    // 模拟程序运行一段时间\n    printf(\"收集统计数据中，按Ctrl+C退出...\\n\\n\");\n    \n    // 等待30秒后查询统计结果\n    sleep(30);\n    \n    // 查询特定时间范围的统计数据\n    time_t currentTime = time(NULL);\n    time_t startTime = currentTime - 30;  // 30秒前\n    \n    for (int i = 0; i < ruleCount; i++) {\n        TRAFFIC_STATISTICS stats;\n        if (statsManager.QueryStatistics(rules[i], startTime, currentTime, &stats)) {\n            printf(\"规则ID %u 的流量统计:\\n\", rules[i]);\n            printf(\"  发送数据: %lu 字节 (%lu 个包)\\n\", stats.bytesSent, stats.packetsSent);\n            printf(\"  接收数据: %lu 字节 (%lu 个包)\\n\", stats.bytesReceived, stats.packetsReceived);\n            printf(\"  平均速率: 上行 %.2f KB/s, 下行 %.2f KB/s\\n\", \n                   stats.avgUploadSpeedKBps, stats.avgDownloadSpeedKBps);\n            printf(\"  峰值速率: 上行 %.2f KB/s, 下行 %.2f KB/s\\n\", \n                   stats.peakUploadSpeedKBps, stats.peakDownloadSpeedKBps);\n            printf(\"  连接数量: %u\\n\", stats.connectionCount);\n        }\n    }\n    \n    // 导出统计数据到CSV文件\n    if (statsManager.ExportStatistics(\"/tmp/traffic_report.csv\", startTime, currentTime)) {\n        printf(\"\\n已导出统计报告到 /tmp/traffic_report.csv\\n\");\n    }\n    \n    // 清理资源\n    statsManager.Shutdown();\n    printf(\"流量统计模块已关闭\\n\");\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化流量统计模块\n      参数:\n        - 名称: config\n          类型: TRAFFIC_STATS_CONFIG*\n          描述: 统计配置结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功初始化\n      示例: statsManager.Initialize(&config)", "EnableStatsForRule": "功能: 为特定转发规则启用流量统计\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 转发规则ID\n      返回值:\n        类型: bool\n        描述: 是否成功启用统计\n      示例: statsManager.EnableStatsForRule(rules[i])", "SetStatsCallback": "功能: 设置接收统计报告的回调函数\n      参数:\n        - 名称: callback\n          类型: TrafficStatsCallback\n          描述: 回调函数指针\n        - 名称: userData\n          类型: void*\n          描述: 用户自定义数据\n      返回值:\n        类型: bool\n        描述: 是否成功设置回调\n      示例: statsManager.SetStatsCallback(trafficStatsCallback, NULL)", "QueryStatistics": "功能: 查询特定时间范围的流量统计数据\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 转发规则ID\n        - 名称: startTime\n          类型: time_t\n          描述: 起始时间戳\n        - 名称: endTime\n          类型: time_t\n          描述: 结束时间戳\n        - 名称: stats\n          类型: TRAFFIC_STATISTICS*\n          描述: 统计数据结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功查询统计数据\n      示例: statsManager.QueryStatistics(rules[i], startTime, currentTime, &stats)", "ExportStatistics": "功能: 导出统计数据到CSV文件\n      参数:\n        - 名称: filePath\n          类型: const char*\n          描述: 导出文件路径\n        - 名称: startTime\n          类型: time_t\n          描述: 起始时间戳\n        - 名称: endTime\n          类型: time_t\n          描述: 结束时间戳\n      返回值:\n        类型: bool\n        描述: 是否成功导出统计数据\n      示例: statsManager.ExportStatistics(\"/tmp/traffic_report.csv\", startTime, currentTime)", "Shutdown": "功能: 关闭流量统计模块\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否成功关闭\n      示例: statsManager.Shutdown()"}, "数据结构": {"TRAFFIC_STATS_CONFIG": "描述: 流量统计配置结构体\n      字段:\n        - 名称: enableRealTimeMonitoring\n          类型: bool\n          描述: 是否启用实时监控\n        - 名称: sampleIntervalMs\n          类型: uint32_t\n          描述: 采样间隔(毫秒)\n        - 名称: persistStatistics\n          类型: bool\n          描述: 是否持久化存储统计数据\n        - 名称: logFilePath\n          类型: char[256]\n          描述: 日志文件路径\n        - 名称: aggregateByRule\n          类型: bool\n          描述: 是否按转发规则聚合统计\n        - 名称: maxHistoryDays\n          类型: uint16_t\n          描述: 最大历史数据保留天数", "TRAFFIC_STATISTICS": "描述: 流量统计数据结构体\n      字段:\n        - 名称: ruleId\n          类型: uint32_t\n          描述: 转发规则ID\n        - 名称: bytesSent\n          类型: uint64_t\n          描述: 发送字节数\n        - 名称: bytesReceived\n          类型: uint64_t\n          描述: 接收字节数\n        - 名称: packetsSent\n          类型: uint64_t\n          描述: 发送数据包数量\n        - 名称: packetsReceived\n          类型: uint64_t\n          描述: 接收数据包数量\n        - 名称: avgUploadSpeedKBps\n          类型: float\n          描述: 平均上传速度(KB/s)\n        - 名称: avgDownloadSpeedKBps\n          类型: float\n          描述: 平均下载速度(KB/s)\n        - 名称: peakUploadSpeedKBps\n          类型: float\n          描述: 峰值上传速度(KB/s)\n        - 名称: peakDownloadSpeedKBps\n          类型: float\n          描述: 峰值下载速度(KB/s)\n        - 名称: connectionCount\n          类型: uint16_t\n          描述: 统计期间的连接数量\n        - 名称: startTimestamp\n          类型: uint64_t\n          描述: 统计起始时间戳\n        - 名称: endTimestamp\n          类型: uint64_t\n          描述: 统计结束时间戳", "TRAFFIC_SAMPLE": "描述: 流量采样数据结构体\n      字段:\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 采样时间戳\n        - 名称: uploadSpeedKBps\n          类型: float\n          描述: 上传速度(KB/s)\n        - 名称: downloadSpeedKBps\n          类型: float\n          描述: 下载速度(KB/s)\n        - 名称: activeConnections\n          类型: uint16_t\n          描述: 当前活动连接数"}}