{"示例代码": {"内存保护示例": "int main() {\n    // 初始化内存保护模块\n    MemoryProtector protector;\n    \n    // 配置内存保护参数\n    MemoryProtectionConfig config;\n    config.enableDataEncryption = true;\n    config.enableCodeEncryption = true;\n    config.enableSelfModifying = true;\n    config.enableVirtualization = true;\n    config.memoryCheckInterval = 1000; // 毫秒\n    \n    // 初始化保护器\n    if (!protector.Initialize(config)) {\n        fprintf(stderr, \"无法初始化内存保护模块\\n\");\n        return 1;\n    }\n    \n    printf(\"内存保护模块已启动\\n\");\n    \n    // 创建加密内存区域\n    EncryptedMemoryRegion region;\n    if (!protector.CreateProtectedRegion(&region, 1024)) { // 分配1KB加密内存\n        fprintf(stderr, \"无法创建加密内存区域\\n\");\n        return 1;\n    }\n    \n    printf(\"已创建加密内存区域\\n\");\n    printf(\"  - 区域地址: 0x%p\\n\", region.address);\n    printf(\"  - 区域大小: %zu 字节\\n\", region.size);\n    \n    // 将敏感数据写入加密内存区域\n    char sensitiveData[] = \"这是非常敏感的数据，需要在内存中进行保护\";\n    if (!protector.WriteToProtectedRegion(&region, sensitiveData, strlen(sensitiveData) + 1)) {\n        fprintf(stderr, \"写入加密内存失败\\n\");\n        return 1;\n    }\n    \n    // 锁定敏感内存区域（防止被转储或访问）\n    printf(\"锁定敏感内存区域...\\n\");\n    protector.LockMemoryRegion(&region);\n    \n    // 设置内存修改检测回调\n    protector.SetMemoryModificationCallback([](MemoryViolationInfo* info) {\n        printf(\"检测到内存篡改尝试!\\n\");\n        printf(\"  - 访问地址: 0x%p\\n\", info->accessAddress);\n        printf(\"  - 访问类型: %s\\n\", info->accessType == ACCESS_READ ? \"读取\" : \"写入\");\n        printf(\"  - 尝试时间: %llu ms\\n\", info->timestamp);\n    });\n    \n    // 启动内存保护监视\n    printf(\"启动内存保护监视...\\n\");\n    protector.StartMemoryMonitoring();\n    \n    // 测试读取受保护内存区域\n    printf(\"测试读取受保护内存区域...\\n\");\n    char buffer[128];\n    if (protector.ReadFromProtectedRegion(&region, buffer, sizeof(buffer))) {\n        printf(\"成功读取受保护数据: %s\\n\", buffer);\n    } else {\n        printf(\"读取受保护数据失败，可能需要先解锁\\n\");\n    }\n    \n    // 临时解锁区域以修改数据\n    printf(\"临时解锁内存区域...\\n\");\n    protector.UnlockMemoryRegion(&region);\n    \n    // 更新敏感数据\n    char updatedData[] = \"已更新的敏感数据\";\n    if (protector.WriteToProtectedRegion(&region, updatedData, strlen(updatedData) + 1)) {\n        printf(\"已更新受保护数据\\n\");\n    }\n    \n    // 重新锁定区域\n    protector.LockMemoryRegion(&region);\n    \n    // 校验内存区域完整性\n    printf(\"校验内存区域完整性...\\n\");\n    MemoryIntegrityResult integrityResult = protector.VerifyMemoryIntegrity(&region);\n    \n    if (integrityResult.isIntact) {\n        printf(\"内存区域校验通过，数据完整\\n\");\n    } else {\n        printf(\"内存区域可能已被篡改: %s\\n\", integrityResult.violationDetails);\n    }\n    \n    // 停止内存监视\n    protector.StopMemoryMonitoring();\n    \n    // 销毁加密内存区域（安全擦除）\n    printf(\"销毁加密内存区域...\\n\");\n    protector.DestroyProtectedRegion(&region);\n    \n    // 清理资源\n    protector.Cleanup();\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化内存保护模块\n      参数:\n        - 名称: config\n          类型: MemoryProtectionConfig\n          描述: 内存保护配置参数\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: protector.Initialize(config)", "CreateProtectedRegion": "功能: 创建受保护的内存区域\n      参数:\n        - 名称: region\n          类型: EncryptedMemoryRegion*\n          描述: 加密内存区域指针\n        - 名称: size\n          类型: size_t\n          描述: 内存区域大小(字节)\n      返回值:\n        类型: bool\n        描述: 创建是否成功\n      示例: protector.CreateProtectedRegion(&region, 1024)", "WriteToProtectedRegion": "功能: 写入数据到受保护的内存区域\n      参数:\n        - 名称: region\n          类型: EncryptedMemoryRegion*\n          描述: 加密内存区域指针\n        - 名称: data\n          类型: const void*\n          描述: 要写入的数据\n        - 名称: size\n          类型: size_t\n          描述: 要写入的数据大小(字节)\n      返回值:\n        类型: bool\n        描述: 写入操作是否成功\n      示例: protector.WriteToProtectedRegion(&region, data, size)", "ReadFromProtectedRegion": "功能: 从受保护的内存区域读取数据\n      参数:\n        - 名称: region\n          类型: EncryptedMemoryRegion*\n          描述: 加密内存区域指针\n        - 名称: buffer\n          类型: void*\n          描述: 输出缓冲区\n        - 名称: size\n          类型: size_t\n          描述: 要读取的数据大小\n      返回值:\n        类型: bool\n        描述: 读取操作是否成功\n      示例: protector.ReadFromProtectedRegion(&region, buffer, sizeof(buffer))", "LockMemoryRegion": "功能: 锁定内存区域防止访问\n      参数:\n        - 名称: region\n          类型: EncryptedMemoryRegion*\n          描述: 加密内存区域指针\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.LockMemoryRegion(&region)", "UnlockMemoryRegion": "功能: 解锁内存区域允许访问\n      参数:\n        - 名称: region\n          类型: EncryptedMemoryRegion*\n          描述: 加密内存区域指针\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.UnlockMemoryRegion(&region)", "SetMemoryModificationCallback": "功能: 设置内存修改检测回调\n      参数:\n        - 名称: callback\n          类型: std::function<void(MemoryViolationInfo*)>\n          描述: 检测到内存修改时调用的回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.SetMemoryModificationCallback(callbackFunction)", "StartMemoryMonitoring": "功能: 启动内存保护监视\n      参数:\n      返回值:\n        类型: bool\n        描述: 启动是否成功\n      示例: protector.StartMemoryMonitoring()", "StopMemoryMonitoring": "功能: 停止内存保护监视\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.StopMemoryMonitoring()", "VerifyMemoryIntegrity": "功能: 验证内存区域完整性\n      参数:\n        - 名称: region\n          类型: EncryptedMemoryRegion*\n          描述: 加密内存区域指针\n      返回值:\n        类型: MemoryIntegrityResult\n        描述: 内存完整性验证结果\n      示例: integrityResult = protector.VerifyMemoryIntegrity(&region)", "DestroyProtectedRegion": "功能: 销毁受保护的内存区域\n      参数:\n        - 名称: region\n          类型: EncryptedMemoryRegion*\n          描述: 加密内存区域指针\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.DestroyProtectedRegion(&region)"}, "数据结构": {"MemoryProtectionConfig": "描述: 内存保护配置参数\n      字段:\n        - 名称: enableDataEncryption\n          类型: bool\n          描述: 是否启用数据加密\n        - 名称: enableCodeEncryption\n          类型: bool\n          描述: 是否启用代码加密\n        - 名称: enableSelfModifying\n          类型: bool\n          描述: 是否启用自修改代码\n        - 名称: enableVirtualization\n          类型: bool\n          描述: 是否启用虚拟化保护\n        - 名称: memoryCheckInterval\n          类型: uint32_t\n          描述: 内存检查间隔(毫秒)", "EncryptedMemoryRegion": "描述: 加密内存区域\n      字段:\n        - 名称: address\n          类型: void*\n          描述: 内存区域地址\n        - 名称: size\n          类型: size_t\n          描述: 内存区域大小(字节)\n        - 名称: isLocked\n          类型: bool\n          描述: 是否已锁定\n        - 名称: encryptionKeyId\n          类型: uint32_t\n          描述: 加密密钥ID\n        - 名称: checksum\n          类型: uint64_t\n          描述: 内存校验和", "MemoryViolationInfo": "描述: 内存违规访问信息\n      字段:\n        - 名称: accessAddress\n          类型: void*\n          描述: 被访问的内存地址\n        - 名称: accessType\n          类型: uint8_t\n          描述: 访问类型(读/写)\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 违规时间戳(毫秒)\n        - 名称: sourceAddress\n          类型: void*\n          描述: 访问源地址(如果可获取)", "MemoryIntegrityResult": "描述: 内存完整性验证结果\n      字段:\n        - 名称: isIntact\n          类型: bool\n          描述: 内存是否完整\n        - 名称: checksumMatched\n          类型: bool\n          描述: 校验和是否匹配\n        - 名称: violationDetails\n          类型: char[128]\n          描述: 违规详细信息(如有)"}}