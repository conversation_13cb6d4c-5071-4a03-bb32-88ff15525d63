{"示例代码": {"文件隐藏示例": "int main() {\n    // 创建文件隐藏管理器\n    FileHider hider;\n    \n    // 配置文件隐藏选项\n    HidingOptions options;\n    options.useAttributeHiding = true;\n    options.useSpecialNaming = true;\n    options.useDirectoryStructure = true;\n    \n    // 初始化隐藏器\n    hider.initialize(options);\n    \n    // 隐藏单个文件\n    FileInfo payload;\n    payload.path = \"/home/<USER>/malware.bin\";\n    payload.hidingMethod = HIDE_METHOD_ATTRIBUTE | HIDE_METHOD_SPECIAL_NAME;\n    payload.newName = \".\\u200B.malware\";  // 使用零宽字符\n    \n    bool success = hider.hideFile(payload);\n    \n    // 隐藏整个目录及其内容\n    DirectoryInfo dataDir;\n    dataDir.path = \"/home/<USER>/data\";\n    dataDir.hidingMethod = HIDE_METHOD_ATTRIBUTE | HIDE_METHOD_SPECIAL_LOCATION;\n    dataDir.recursive = true;\n    \n    success = hider.hideDirectory(dataDir);\n    \n    // 检查文件是否成功隐藏\n    if (hider.isHidden(\"/home/<USER>/.\\u200B.malware\")) {\n        printf(\"File successfully hidden\\n\");\n    }\n    \n    // 恢复隐藏的文件\n    // hider.unhideFile(\"/home/<USER>/.\\u200B.malware\");\n    \n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化文件隐藏器\n      参数:\n        - 名称: options\n          类型: HidingOptions\n          描述: 隐藏选项配置\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: hider.initialize(options);", "hideFile": "功能: 隐藏单个文件\n      参数:\n        - 名称: fileInfo\n          类型: FileInfo\n          描述: 文件信息及隐藏方式\n      返回值:\n        类型: bool\n        描述: 隐藏操作是否成功\n      示例: bool success = hider.hideFile(payload);", "hideDirectory": "功能: 隐藏整个目录及其内容\n      参数:\n        - 名称: dirInfo\n          类型: DirectoryInfo\n          描述: 目录信息及隐藏方式\n      返回值:\n        类型: bool\n        描述: 隐藏操作是否成功\n      示例: bool success = hider.hideDirectory(dataDir);", "isHidden": "功能: 检查文件或目录是否已隐藏\n      参数:\n        - 名称: path\n          类型: const char*\n          描述: 文件或目录路径\n      返回值:\n        类型: bool\n        描述: 如果已隐藏则返回true，否则返回false\n      示例: bool hidden = hider.isHidden(\"/path/to/file\");", "unhideFile": "功能: 恢复隐藏的文件\n      参数:\n        - 名称: path\n          类型: const char*\n          描述: 隐藏文件的路径\n      返回值:\n        类型: bool\n        描述: 恢复操作是否成功\n      示例: bool success = hider.unhideFile(\"/path/to/hidden/file\");"}, "数据结构": {"HidingOptions": "描述: 文件隐藏选项结构体\n      字段:\n        - 名称: useAttributeHiding\n          类型: bool\n          描述: 是否使用文件属性隐藏（如隐藏属性）\n        - 名称: useSpecialNaming\n          类型: bool\n          描述: 是否使用特殊命名方式隐藏（如以点开头或使用特殊Unicode字符）\n        - 名称: useDirectoryStructure\n          类型: bool\n          描述: 是否利用目录结构隐藏（如隐藏在特殊系统目录中）\n        - 名称: specialChars\n          类型: std::vector<uint32_t>\n          描述: 用于隐藏的特殊Unicode字符", "FileInfo": "描述: 文件信息结构体\n      字段:\n        - 名称: path\n          类型: std::string\n          描述: 文件路径\n        - 名称: hidingMethod\n          类型: uint32_t\n          描述: 隐藏方法标志位组合\n        - 名称: newName\n          类型: std::string\n          描述: 隐藏后的新名称（如果使用重命名方法）\n        - 名称: attributes\n          类型: uint32_t\n          描述: 要设置的文件属性", "DirectoryInfo": "描述: 目录信息结构体\n      字段:\n        - 名称: path\n          类型: std::string\n          描述: 目录路径\n        - 名称: hidingMethod\n          类型: uint32_t\n          描述: 隐藏方法标志位组合\n        - 名称: recursive\n          类型: bool\n          描述: 是否递归隐藏子目录及文件\n        - 名称: excludePatterns\n          类型: std::vector<std::string>\n          描述: 排除的文件或目录模式"}}