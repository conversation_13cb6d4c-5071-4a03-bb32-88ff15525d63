{"示例代码": {"说明": "暂无"}, "API接口": {"BypassFortiGateNetworkRules": "功能: 配置和启用网络通信策略以尝试绕过FortiGate防火墙的检测。\n      参数:\n        - 名称: target_host\n          类型: const char*\n          描述: 目标通信主机的IP或域名。\n        - 名称: target_port\n          类型: unsigned short\n          描述: 目标通信端口。\n        - 名称: bypass_method\n          类型: FortiGateBypassMethod\n          描述: 选择的绕过方法 (例如DNS隧道, HTTP伪装)。\n        - 名称: method_params\n          类型: void*\n          描述: 特定绕过方法所需的参数结构体指针。\n      返回值:\n        类型: int\n        描述: 操作状态，0表示绕过通信尝试已启动，非0表示配置失败或方法不支持。\n      示例: DNSTunnelParams dns_p = {\"bypass.example.com\"}; if (BypassFortiGateNetworkRules(\"********\", 80, DNS_TUNNELING, &dns_p) == 0) { /* 通信已尝试 */ }"}, "数据结构": {"FortiGateBypassMethod": "描述: 枚举类型，定义可用的FortiGate绕过技术。\n      字段:", "DNSTunnelParams": "描述: DNS隧道方法所需的参数。\n      字段:\n        - 名称: tunnel_domain\n          类型: char[128]\n          描述: 用于DNS隧道的域名。\n        - 名称: max_payload_per_query\n          类型: int\n          描述: 每个DNS查询的最大数据负载。"}}