{"示例代码": {"建立网络链条示例": "int main() {\n    // 初始化网络链条管理器\n    NetworkChainManager chainManager;\n    \n    // 配置链条节点\n    ChainNodeConfig nodes[3];\n    \n    // 第一个节点（入口点）\n    strcpy(nodes[0].address, \"************\");\n    nodes[0].port = 22;\n    strcpy(nodes[0].protocol, \"ssh\");\n    strcpy(nodes[0].credentials, \"username:password\");\n    \n    // 第二个节点（中继点）\n    strcpy(nodes[1].address, \"********\");\n    nodes[1].port = 443;\n    strcpy(nodes[1].protocol, \"https\");\n    strcpy(nodes[1].credentials, \"cert:/path/to/cert.pem\");\n    \n    // 第三个节点（目标机器）\n    strcpy(nodes[2].address, \"***********\");\n    nodes[2].port = 22;\n    strcpy(nodes[2].protocol, \"ssh\");\n    strcpy(nodes[2].credentials, \"key:/path/to/private.key\");\n    \n    // 创建网络链条\n    ChainID chainId = chainManager.CreateChain(nodes, 3);\n    if (chainId == INVALID_CHAIN_ID) {\n        printf(\"创建网络链条失败\\n\");\n        return 1;\n    }\n    \n    printf(\"成功创建网络链条，ID: %llu\\n\", chainId);\n    \n    // 连接网络链条\n    if (!chainManager.ConnectChain(chainId)) {\n        printf(\"连接网络链条失败\\n\");\n        chainManager.DestroyChain(chainId);\n        return 1;\n    }\n    \n    printf(\"已成功连接网络链条\\n\");\n    \n    // 通过链条执行远程命令\n    RemoteExecutionResult result;\n    if (chainManager.ExecuteCommand(chainId, \"whoami\", &result)) {\n        printf(\"命令执行结果: %s\\n\", result.output);\n    }\n    \n    // 建立远程登录会话\n    SessionID sessionId = chainManager.EstablishInteractiveSession(chainId);\n    if (sessionId != INVALID_SESSION_ID) {\n        printf(\"已建立交互式会话，ID: %llu\\n\", sessionId);\n        \n        // 使用会话进行交互...\n        \n        // 关闭会话\n        chainManager.CloseSession(sessionId);\n    }\n    \n    // 断开并销毁链条\n    chainManager.DisconnectChain(chainId);\n    chainManager.DestroyChain(chainId);\n    \n    return 0;\n}"}, "API接口": {"CreateChain": "功能: 创建网络链条\n      参数:\n        - 名称: nodes\n          类型: ChainNodeConfig[]\n          描述: 链条节点配置数组\n        - 名称: nodeCount\n          类型: int\n          描述: 节点数量\n      返回值:\n        类型: ChainID\n        描述: 创建的链条ID，失败时返回INVALID_CHAIN_ID\n      示例: chainId = chainManager.CreateChain(nodes, 3)", "ConnectChain": "功能: 连接网络链条\n      参数:\n        - 名称: chainId\n          类型: ChainID\n          描述: 要连接的链条ID\n      返回值:\n        类型: bool\n        描述: 连接是否成功\n      示例: chainManager.ConnectChain(chainId)", "ExecuteCommand": "功能: 通过网络链条执行远程命令\n      参数:\n        - 名称: chainId\n          类型: ChainID\n          描述: 网络链条ID\n        - 名称: command\n          类型: const char*\n          描述: 要执行的命令\n        - 名称: result\n          类型: RemoteExecutionResult*\n          描述: 执行结果输出\n      返回值:\n        类型: bool\n        描述: 命令是否成功执行\n      示例: chainManager.ExecuteCommand(chainId, \"whoami\", &result)", "EstablishInteractiveSession": "功能: 通过网络链条建立交互式会话\n      参数:\n        - 名称: chainId\n          类型: ChainID\n          描述: 网络链条ID\n      返回值:\n        类型: SessionID\n        描述: 创建的会话ID，失败时返回INVALID_SESSION_ID\n      示例: sessionId = chainManager.EstablishInteractiveSession(chainId)", "DisconnectChain": "功能: 断开网络链条连接\n      参数:\n        - 名称: chainId\n          类型: ChainID\n          描述: 网络链条ID\n      返回值:\n        类型: bool\n        描述: 断开连接是否成功\n      示例: chainManager.DisconnectChain(chainId)", "DestroyChain": "功能: 销毁网络链条\n      参数:\n        - 名称: chainId\n          类型: ChainID\n          描述: 网络链条ID\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: chainManager.DestroyChain(chainId)"}, "数据结构": {"ChainNodeConfig": "描述: 网络链条节点配置\n      字段:\n        - 名称: address\n          类型: char[128]\n          描述: 节点地址（IP或域名）\n        - 名称: port\n          类型: uint16_t\n          描述: 节点端口\n        - 名称: protocol\n          类型: char[16]\n          描述: 连接协议（ssh/http/https等）\n        - 名称: credentials\n          类型: char[256]\n          描述: 认证凭据（格式：类型:值）\n        - 名称: timeout\n          类型: uint32_t\n          描述: 连接超时时间（秒）", "RemoteExecutionResult": "描述: 远程命令执行结果\n      字段:\n        - 名称: output\n          类型: char[4096]\n          描述: 命令输出内容\n        - 名称: exitCode\n          类型: int\n          描述: 命令退出码\n        - 名称: executionTime\n          类型: uint64_t\n          描述: 执行耗时（毫秒）", "ChainStatistics": "描述: 网络链条统计信息\n      字段:\n        - 名称: hopCount\n          类型: uint32_t\n          描述: 链条跳数\n        - 名称: totalLatency\n          类型: uint64_t\n          描述: 端到端延迟（毫秒）\n        - 名称: activeConnections\n          类型: uint32_t\n          描述: 活跃连接数\n        - 名称: establishedTime\n          类型: uint64_t\n          描述: 链条建立时间戳"}}