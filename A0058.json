{"示例代码": {"限制端口转发带宽示例": "int main() {\n    // 初始化带宽控制管理器\n    BandwidthController controller;\n    \n    // 获取当前活动的端口转发连接列表\n    FORWARD_CONNECTION_ID connections[10];\n    uint8_t connectionCount = 0;\n    \n    PortForwardConnector connector;\n    if (connector.ListActiveConnections(connections, 10, &connectionCount)) {\n        printf(\"当前活动的端口转发连接数量: %d\\n\", connectionCount);\n        \n        // 遍历显示所有活动连接\n        for (int i = 0; i < connectionCount; i++) {\n            FORWARD_CONNECTION_INFO info;\n            if (connector.GetConnectionInfo(connections[i], &info)) {\n                printf(\"%d. 连接ID %u: %s:%d -> %s:%d\\n\", i + 1,\n                       info.connectionId, info.localHost, info.localPort, \n                       info.remoteHost, info.remotePort);\n            }\n        }\n        \n        // 选择要限制带宽的连接\n        if (connectionCount > 0) {\n            // 以第一个连接为例\n            FORWARD_CONNECTION_ID connectionId = connections[0];\n            \n            printf(\"\\n准备限制连接ID %u 的带宽...\\n\", connectionId);\n            \n            // 创建带宽限制配置\n            BANDWIDTH_LIMITS limits;\n            memset(&limits, 0, sizeof(BANDWIDTH_LIMITS));\n            \n            limits.uploadLimitKBps = 512;    // 限制上传速度为512KB/s\n            limits.downloadLimitKBps = 1024; // 限制下载速度为1MB/s\n            limits.burstSize = 64;           // 突发缓冲区大小(KB)\n            limits.priorityLevel = 2;         // 连接优先级(1-5，1为最高)\n            limits.adaptiveLimiting = true;   // 启用自适应带宽限制\n            \n            // 应用带宽限制\n            if (controller.ApplyBandwidthLimits(connectionId, &limits)) {\n                printf(\"已成功应用带宽限制\\n\");\n                printf(\"上传限制: %u KB/s\\n\", limits.uploadLimitKBps);\n                printf(\"下载限制: %u KB/s\\n\", limits.downloadLimitKBps);\n                \n                // 配置动态带宽调整策略\n                ADAPTIVE_BANDWIDTH_POLICY policy;\n                memset(&policy, 0, sizeof(ADAPTIVE_BANDWIDTH_POLICY));\n                \n                policy.enableDynamicAdjustment = true;   // 启用动态调整\n                policy.minUploadKBps = 128;             // 最低上传速度\n                policy.maxUploadKBps = 1024;            // 最高上传速度\n                policy.minDownloadKBps = 256;           // 最低下载速度\n                policy.maxDownloadKBps = 2048;          // 最高下载速度\n                policy.adjustmentIntervalMs = 5000;     // 每5秒调整一次\n                \n                if (controller.SetAdaptiveBandwidthPolicy(connectionId, &policy)) {\n                    printf(\"已设置动态带宽调整策略\\n\");\n                }\n                \n                // 等待一段时间后检查带宽使用情况\n                printf(\"监控带宽使用情况...\\n\");\n                sleep(15);\n                \n                // 获取当前带宽使用情况\n                BANDWIDTH_USAGE usage;\n                if (controller.GetBandwidthUsage(connectionId, &usage)) {\n                    printf(\"\\n当前带宽使用情况:\\n\");\n                    printf(\"实际上传速度: %.2f KB/s (限制: %u KB/s)\\n\", \n                           usage.currentUploadKBps, usage.limitUploadKBps);\n                    printf(\"实际下载速度: %.2f KB/s (限制: %u KB/s)\\n\", \n                           usage.currentDownloadKBps, usage.limitDownloadKBps);\n                    printf(\"上传使用率: %.1f%%\\n\", \n                           usage.currentUploadKBps / usage.limitUploadKBps * 100.0f);\n                    printf(\"下载使用率: %.1f%%\\n\", \n                           usage.currentDownloadKBps / usage.limitDownloadKBps * 100.0f);\n                }\n                \n                // 移除带宽限制\n                printf(\"\\n正在移除带宽限制...\\n\");\n                if (controller.RemoveBandwidthLimits(connectionId)) {\n                    printf(\"已成功移除带宽限制\\n\");\n                }\n            } else {\n                printf(\"应用带宽限制失败: %s\\n\", controller.GetLastError());\n            }\n        } else {\n            printf(\"没有活动的端口转发连接\\n\");\n        }\n    } else {\n        printf(\"获取活动连接列表失败\\n\");\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"ApplyBandwidthLimits": "功能: 应用带宽限制到指定连接\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: limits\n          类型: BANDWIDTH_LIMITS*\n          描述: 带宽限制结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功应用带宽限制\n      示例: controller.ApplyBandwidthLimits(connectionId, &limits)", "SetAdaptiveBandwidthPolicy": "功能: 设置动态带宽调整策略\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: policy\n          类型: ADAPTIVE_BANDWIDTH_POLICY*\n          描述: 动态带宽策略结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功设置策略\n      示例: controller.SetAdaptiveBandwidthPolicy(connectionId, &policy)", "GetBandwidthUsage": "功能: 获取当前带宽使用情况\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: usage\n          类型: BANDWIDTH_USAGE*\n          描述: 带宽使用情况结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取使用情况\n      示例: controller.GetBandwidthUsage(connectionId, &usage)", "RemoveBandwidthLimits": "功能: 移除带宽限制\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n      返回值:\n        类型: bool\n        描述: 是否成功移除带宽限制\n      示例: controller.RemoveBandwidthLimits(connectionId)", "GetLastError": "功能: 获取上一操作的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: controller.GetLastError()"}, "数据结构": {"BANDWIDTH_LIMITS": "描述: 带宽限制结构体\n      字段:\n        - 名称: uploadLimitKBps\n          类型: uint32_t\n          描述: 上传带宽限制(KB/s)\n        - 名称: downloadLimitKBps\n          类型: uint32_t\n          描述: 下载带宽限制(KB/s)\n        - 名称: burstSize\n          类型: uint16_t\n          描述: 突发缓冲区大小(KB)\n        - 名称: priorityLevel\n          类型: uint8_t\n          描述: 连接优先级(1-5，1为最高)\n        - 名称: adaptiveLimiting\n          类型: bool\n          描述: 是否启用自适应带宽限制\n        - 名称: flags\n          类型: uint32_t\n          描述: 额外标志位", "ADAPTIVE_BANDWIDTH_POLICY": "描述: 自适应带宽策略结构体\n      字段:\n        - 名称: enableDynamicAdjustment\n          类型: bool\n          描述: 是否启用动态调整\n        - 名称: minUploadKBps\n          类型: uint32_t\n          描述: 最低上传速度(KB/s)\n        - 名称: maxUploadKBps\n          类型: uint32_t\n          描述: 最高上传速度(KB/s)\n        - 名称: minDownloadKBps\n          类型: uint32_t\n          描述: 最低下载速度(KB/s)\n        - 名称: maxDownloadKBps\n          类型: uint32_t\n          描述: 最高下载速度(KB/s)\n        - 名称: adjustmentIntervalMs\n          类型: uint32_t\n          描述: 调整间隔(毫秒)\n        - 名称: adaptiveAlgorithm\n          类型: uint8_t\n          描述: 自适应算法类型", "BANDWIDTH_USAGE": "描述: 带宽使用情况结构体\n      字段:\n        - 名称: connectionId\n          类型: uint32_t\n          描述: 连接ID\n        - 名称: currentUploadKBps\n          类型: float\n          描述: 当前上传速度(KB/s)\n        - 名称: currentDownloadKBps\n          类型: float\n          描述: 当前下载速度(KB/s)\n        - 名称: limitUploadKBps\n          类型: uint32_t\n          描述: 上传速度限制(KB/s)\n        - 名称: limitDownloadKBps\n          类型: uint32_t\n          描述: 下载速度限制(KB/s)\n        - 名称: avgUploadUtilization\n          类型: float\n          描述: 平均上传带宽利用率(0-1)\n        - 名称: avgDownloadUtilization\n          类型: float\n          描述: 平均下载带宽利用率(0-1)\n        - 名称: measuredTimestamp\n          类型: uint64_t\n          描述: 测量时间戳"}}