{"示例代码": {"接收命令示例": "int main() {\n    // 初始化命令接收器\n    CommandReceiver receiver;\n    \n    // 启动命令监听\n    if (!receiver.StartCommandListener()) {\n        printf(\"启动命令监听失败\\n\");\n        return 1;\n    }\n    \n    // 等待并处理接收到的命令\n    while (1) {\n        COMMAND_DATA cmdData;\n        if (receiver.ReceiveCommand(&cmdData)) {\n            printf(\"接收到命令: %s\\n\", cmdData.command);\n            // 解析并准备执行接收到的命令\n            CommandParser::ParseCommand(&cmdData);\n        }\n        usleep(100000); // 休眠100ms\n    }\n    \n    return 0;\n}"}, "API接口": {"StartCommandListener": "功能: 启动命令接收监听器\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否成功启动监听\n      示例: receiver.StartCommandListener()", "ReceiveCommand": "功能: 接收命令数据\n      参数:\n        - 名称: cmdData\n          类型: COMMAND_DATA*\n          描述: 接收命令数据的缓冲区\n      返回值:\n        类型: bool\n        描述: 是否成功接收命令\n      示例: receiver.ReceiveCommand(&cmdData)", "ParseCommand": "功能: 解析接收到的命令\n      参数:\n        - 名称: cmdData\n          类型: COMMAND_DATA*\n          描述: 需要解析的命令数据\n      返回值:\n        类型: PARSED_COMMAND\n        描述: 解析后的命令结构\n      示例: CommandParser::ParseCommand(&cmdData)"}, "数据结构": {"COMMAND_DATA": "描述: 命令数据结构\n      字段:\n        - 名称: type\n          类型: BYTE\n          描述: 命令类型标识\n        - 名称: command\n          类型: char[1024]\n          描述: 命令文本内容\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 命令时间戳\n        - 名称: commandId\n          类型: uint32_t\n          描述: 命令唯一标识符", "PARSED_COMMAND": "描述: 解析后的命令结构\n      字段:\n        - 名称: commandType\n          类型: int\n          描述: 解析后的命令类型\n        - 名称: argc\n          类型: int\n          描述: 参数数量\n        - 名称: argv\n          类型: char*[64]\n          描述: 参数数组\n        - 名称: rawCommand\n          类型: char[1024]\n          描述: 原始命令文本"}}