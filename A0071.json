{"示例代码": {"ARP嗅探示例": "// Linux系统中使用C实现ARP嗅探功能\n#include <stdio.h>\n#include <stdlib.h>\n#include <string.h>\n#include <unistd.h>\n#include <net/if.h>\n#include <net/ethernet.h>\n#include <netinet/if_ether.h>\n#include <sys/socket.h>\n#include <sys/ioctl.h>\n#include <arpa/inet.h>\n#include <linux/if_packet.h>\n\n#define BUFFER_SIZE 2048\n\nvoid print_mac_address(unsigned char *mac) {\n    printf(\"%02X:%02X:%02X:%02X:%02X:%02X\", \n           mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);\n}\n\nint main(int argc, char *argv[]) {\n    char *interface = \"eth0\"; // 默认接口\n    if (argc > 1) {\n        interface = argv[1];\n    }\n    \n    // 创建原始套接字\n    int sock = socket(AF_PACKET, SOCK_RAW, htons(ETH_P_ARP));\n    if (sock < 0) {\n        perror(\"创建套接字失败\");\n        return 1;\n    }\n    \n    // 设置接口为混杂模式\n    struct ifreq ifr;\n    memset(&ifr, 0, sizeof(ifr));\n    strncpy(ifr.ifr_name, interface, IFNAMSIZ - 1);\n    \n    if (ioctl(sock, SIOCGIFFLAGS, &ifr) < 0) {\n        perror(\"获取接口标志失败\");\n        close(sock);\n        return 1;\n    }\n    \n    ifr.ifr_flags |= IFF_PROMISC;\n    \n    if (ioctl(sock, SIOCSIFFLAGS, &ifr) < 0) {\n        perror(\"设置混杂模式失败\");\n        close(sock);\n        return 1;\n    }\n    \n    printf(\"开始在 %s 接口监听ARP数据包...\\n\", interface);\n    \n    unsigned char buffer[BUFFER_SIZE];\n    struct ether_arp *arp_packet;\n    struct ether_header *eth_header;\n    \n    while (1) {\n        // 接收数据包\n        int data_size = recvfrom(sock, buffer, BUFFER_SIZE, 0, NULL, NULL);\n        if (data_size < 0) {\n            perror(\"接收数据包失败\");\n            break;\n        }\n        \n        // 解析以太网帧头部\n        eth_header = (struct ether_header *)buffer;\n        \n        // 检查是否是ARP数据包\n        if (ntohs(eth_header->ether_type) == ETHERTYPE_ARP) {\n            arp_packet = (struct ether_arp *)(buffer + sizeof(struct ether_header));\n            \n            // 解析ARP操作类型\n            unsigned short arp_opcode = ntohs(arp_packet->ea_hdr.ar_op);\n            \n            printf(\"\\n[ARP] \");\n            \n            // 根据操作类型输出信息\n            if (arp_opcode == ARPOP_REQUEST) {\n                printf(\"请求: 谁有 %d.%d.%d.%d? 请告诉 %d.%d.%d.%d\\n\",\n                       arp_packet->arp_tpa[0], arp_packet->arp_tpa[1],\n                       arp_packet->arp_tpa[2], arp_packet->arp_tpa[3],\n                       arp_packet->arp_spa[0], arp_packet->arp_spa[1],\n                       arp_packet->arp_spa[2], arp_packet->arp_spa[3]);\n            } else if (arp_opcode == ARPOP_REPLY) {\n                printf(\"响应: %d.%d.%d.%d 位于 \", \n                       arp_packet->arp_spa[0], arp_packet->arp_spa[1],\n                       arp_packet->arp_spa[2], arp_packet->arp_spa[3]);\n                \n                print_mac_address(arp_packet->arp_sha);\n                printf(\"\\n\");\n            }\n            \n            printf(\"源MAC: \");\n            print_mac_address(eth_header->ether_shost);\n            printf(\"\\n目标MAC: \");\n            print_mac_address(eth_header->ether_dhost);\n            printf(\"\\n\");\n        }\n    }\n    \n    // 关闭套接字\n    close(sock);\n    \n    return 0;\n}"}, "API接口": {"InitializeARPSniffer": "功能: 初始化ARP嗅探器\n      参数:\n        - 名称: interface\n          类型: const char*\n          描述: 网络接口名称\n        - 名称: promisc_mode\n          类型: bool\n          描述: 是否设置为混杂模式\n      返回值:\n        类型: ARPSniffer*\n        描述: ARP嗅探器句柄，失败返回NULL\n      示例: ARPSniffer* sniffer = InitializeARPSniffer(\"eth0\", true)", "StartARPSniffing": "功能: 开始ARP嗅探\n      参数:\n        - 名称: sniffer\n          类型: ARPSniffer*\n          描述: ARP嗅探器句柄\n        - 名称: callback\n          类型: ARPPacketCallback\n          描述: ARP数据包回调函数\n      返回值:\n        类型: bool\n        描述: 是否成功启动嗅探\n      示例: StartARPSniffing(sniffer, OnARPPacketReceived)", "StopARPSniffing": "功能: 停止ARP嗅探\n      参数:\n        - 名称: sniffer\n          类型: ARPSniffer*\n          描述: ARP嗅探器句柄\n      返回值:\n        类型: bool\n        描述: 是否成功停止嗅探\n      示例: StopARPSniffing(sniffer)", "SendARPPacket": "功能: 发送ARP数据包\n      参数:\n        - 名称: sniffer\n          类型: ARPSniffer*\n          描述: ARP嗅探器句柄\n        - 名称: target_ip\n          类型: const char*\n          描述: 目标IP地址\n        - 名称: target_mac\n          类型: const unsigned char*\n          描述: 目标MAC地址，为NULL则为广播\n        - 名称: operation\n          类型: int\n          描述: 操作类型：1=请求, 2=响应\n      返回值:\n        类型: bool\n        描述: 是否成功发送\n      示例: SendARPPacket(sniffer, \"***********\", NULL, 1)"}, "数据结构": {"ARPPacket": "描述: ARP数据包结构体\n      字段:\n        - 名称: operation\n          类型: int\n          描述: 操作类型：1=请求, 2=响应\n        - 名称: sender_mac\n          类型: unsigned char[6]\n          描述: 发送者MAC地址\n        - 名称: sender_ip\n          类型: unsigned char[4]\n          描述: 发送者IP地址\n        - 名称: target_mac\n          类型: unsigned char[6]\n          描述: 目标MAC地址\n        - 名称: target_ip\n          类型: unsigned char[4]\n          描述: 目标IP地址", "ARPHostEntry": "描述: ARP主机条目结构体\n      字段:\n        - 名称: ip_address\n          类型: char[16]\n          描述: IP地址字符串\n        - 名称: mac_address\n          类型: unsigned char[6]\n          描述: MAC地址\n        - 名称: last_seen\n          类型: time_t\n          描述: 最后发现时间\n        - 名称: is_static\n          类型: bool\n          描述: 是否为静态条目\n        - 名称: hostname\n          类型: char[64]\n          描述: 主机名(如果能解析)", "ARPScanResult": "描述: ARP扫描结果结构体\n      字段:\n        - 名称: network\n          类型: char[16]\n          描述: 扫描的网络地址\n        - 名称: netmask\n          类型: char[16]\n          描述: 网络掩码\n        - 名称: hosts_count\n          类型: int\n          描述: 发现的主机数量\n        - 名称: scan_time\n          类型: time_t\n          描述: 扫描时间\n        - 名称: hosts\n          类型: ARPHostEntry[]\n          描述: 发现的主机数组"}}