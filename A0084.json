{"示例代码": {"数据加密保护示例": "int main() {\n    // 初始化数据加密保护模块\n    DataEncryptionProtector protector;\n    \n    // 配置加密保护参数\n    EncryptionConfig config;\n    config.enableAES256 = true;\n    config.enableChaCha20 = true;\n    config.enableRSA = true;\n    config.enableEllipticCurve = true;\n    config.enableWhiteBoxCrypto = true;\n    \n    // 初始化加密保护器\n    if (!protector.Initialize(config)) {\n        fprintf(stderr, \"无法初始化数据加密保护模块\\n\");\n        return 1;\n    }\n    \n    printf(\"数据加密保护模块已启动\\n\");\n    \n    // 创建加密上下文\n    EncryptionContext context;\n    if (!protector.CreateContext(&context)) {\n        fprintf(stderr, \"无法创建加密上下文\\n\");\n        return 1;\n    }\n    \n    // 生成密钥对\n    printf(\"生成密钥对...\\n\");\n    if (!protector.GenerateKeyPair(&context)) {\n        fprintf(stderr, \"密钥对生成失败\\n\");\n        return 1;\n    }\n    \n    // 准备要加密的数据\n    uint8_t sensitiveData[] = \"这是需要严格保护的敏感数据和关键信息\";\n    size_t dataSize = strlen((char*)sensitiveData);\n    \n    printf(\"原始数据大小: %zu 字节\\n\", dataSize);\n    \n    // 对数据进行分层加密保护\n    EncryptedData encryptedData;\n    if (!protector.EncryptData(&context, sensitiveData, dataSize, &encryptedData)) {\n        fprintf(stderr, \"数据加密保护失败\\n\");\n        return 1;\n    }\n    \n    printf(\"数据已加密保护\\n\");\n    printf(\"  - 加密后大小: %zu 字节\\n\", encryptedData.size);\n    printf(\"  - 使用的加密算法: %s\\n\", encryptedData.primaryAlgorithm);\n    printf(\"  - 加密层数: %d\\n\", encryptedData.layerCount);\n    \n    // 存储加密数据到文件\n    printf(\"保存加密数据到文件...\\n\");\n    if (!protector.SaveEncryptedData(&encryptedData, \"protected_data.bin\")) {\n        fprintf(stderr, \"加密数据保存失败\\n\");\n        return 1;\n    }\n    \n    // 验证加密结果\n    printf(\"验证加密保护...\\n\");\n    EncryptionVerificationResult verResult = protector.VerifyProtection(&encryptedData);\n    if (verResult.isValid) {\n        printf(\"数据加密保护验证成功\\n\");\n        printf(\"  - 加密强度评分: %.2f/10\\n\", verResult.encryptionStrength);\n    } else {\n        printf(\"数据加密保护验证失败: %s\\n\", verResult.errorMessage);\n    }\n    \n    // 从文件加载加密数据\n    printf(\"从文件加载加密数据...\\n\");\n    EncryptedData loadedData;\n    if (!protector.LoadEncryptedData(\"protected_data.bin\", &loadedData)) {\n        fprintf(stderr, \"加密数据加载失败\\n\");\n        return 1;\n    }\n    \n    // 解密数据\n    printf(\"解密数据...\\n\");\n    uint8_t* decryptedData;\n    size_t decryptedSize;\n    if (protector.DecryptData(&context, &loadedData, &decryptedData, &decryptedSize)) {\n        printf(\"数据解密成功\\n\");\n        printf(\"解密后数据: %.*s\\n\", (int)decryptedSize, decryptedData);\n        \n        // 释放解密数据内存\n        protector.FreeDecryptedData(decryptedData);\n    } else {\n        printf(\"数据解密失败\\n\");\n    }\n    \n    // 销毁加密数据\n    protector.DestroyEncryptedData(&encryptedData);\n    protector.DestroyEncryptedData(&loadedData);\n    \n    // 清理资源\n    protector.DestroyContext(&context);\n    protector.Cleanup();\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化数据加密保护模块\n      参数:\n        - 名称: config\n          类型: EncryptionConfig\n          描述: 加密配置参数\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: protector.Initialize(config)", "CreateContext": "功能: 创建加密上下文\n      参数:\n        - 名称: context\n          类型: EncryptionContext*\n          描述: 加密上下文指针\n      返回值:\n        类型: bool\n        描述: 创建是否成功\n      示例: protector.CreateContext(&context)", "GenerateKeyPair": "功能: 生成加密密钥对\n      参数:\n        - 名称: context\n          类型: EncryptionContext*\n          描述: 加密上下文指针\n      返回值:\n        类型: bool\n        描述: 密钥生成是否成功\n      示例: protector.GenerateKeyPair(&context)", "EncryptData": "功能: 加密敏感数据\n      参数:\n        - 名称: context\n          类型: EncryptionContext*\n          描述: 加密上下文指针\n        - 名称: data\n          类型: uint8_t*\n          描述: 原始数据\n        - 名称: dataSize\n          类型: size_t\n          描述: 原始数据大小\n        - 名称: encryptedData\n          类型: EncryptedData*\n          描述: 加密后的数据结构\n      返回值:\n        类型: bool\n        描述: 加密操作是否成功\n      示例: protector.EncryptData(&context, sensitiveData, dataSize, &encryptedData)", "SaveEncryptedData": "功能: 保存加密数据到文件\n      参数:\n        - 名称: encryptedData\n          类型: EncryptedData*\n          描述: 加密数据结构\n        - 名称: filename\n          类型: const char*\n          描述: 目标文件名\n      返回值:\n        类型: bool\n        描述: 保存操作是否成功\n      示例: protector.SaveEncryptedData(&encryptedData, \"protected_data.bin\")", "VerifyProtection": "功能: 验证加密保护强度\n      参数:\n        - 名称: encryptedData\n          类型: EncryptedData*\n          描述: 加密数据结构\n      返回值:\n        类型: EncryptionVerificationResult\n        描述: 验证结果\n      示例: verResult = protector.VerifyProtection(&encryptedData)", "LoadEncryptedData": "功能: 从文件加载加密数据\n      参数:\n        - 名称: filename\n          类型: const char*\n          描述: 源文件名\n        - 名称: encryptedData\n          类型: EncryptedData*\n          描述: 加密数据结构指针\n      返回值:\n        类型: bool\n        描述: 加载操作是否成功\n      示例: protector.LoadEncryptedData(\"protected_data.bin\", &loadedData)", "DecryptData": "功能: 解密加密数据\n      参数:\n        - 名称: context\n          类型: EncryptionContext*\n          描述: 加密上下文指针\n        - 名称: encryptedData\n          类型: EncryptedData*\n          描述: 加密数据结构\n        - 名称: decryptedData\n          类型: uint8_t**\n          描述: 解密后的数据指针\n        - 名称: decryptedSize\n          类型: size_t*\n          描述: 解密后的数据大小\n      返回值:\n        类型: bool\n        描述: 解密操作是否成功\n      示例: protector.DecryptData(&context, &loadedData, &decryptedData, &decryptedSize)", "FreeDecryptedData": "功能: 释放解密数据内存\n      参数:\n        - 名称: data\n          类型: uint8_t*\n          描述: 解密数据指针\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.FreeDecryptedData(decryptedData)", "DestroyEncryptedData": "功能: 销毁加密数据结构\n      参数:\n        - 名称: encryptedData\n          类型: EncryptedData*\n          描述: 加密数据结构指针\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.DestroyEncryptedData(&encryptedData)", "DestroyContext": "功能: 销毁加密上下文\n      参数:\n        - 名称: context\n          类型: EncryptionContext*\n          描述: 加密上下文指针\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.DestroyContext(&context)"}, "数据结构": {"EncryptionConfig": "描述: 加密配置参数\n      字段:\n        - 名称: enableAES256\n          类型: bool\n          描述: 是否启用AES-256算法\n        - 名称: enableChaCha20\n          类型: bool\n          描述: 是否启用ChaCha20算法\n        - 名称: enableRSA\n          类型: bool\n          描述: 是否启用RSA算法\n        - 名称: enableEllipticCurve\n          类型: bool\n          描述: 是否启用椭圆曲线算法\n        - 名称: enableWhiteBoxCrypto\n          类型: bool\n          描述: 是否启用白盒密码学", "EncryptionContext": "描述: 加密上下文\n      字段:\n        - 名称: contextId\n          类型: uint64_t\n          描述: 上下文ID\n        - 名称: publicKey\n          类型: uint8_t[1024]\n          描述: 公钥数据\n        - 名称: publicKeySize\n          类型: size_t\n          描述: 公钥长度\n        - 名称: privateKey\n          类型: uint8_t[2048]\n          描述: 私钥数据(敏感)\n        - 名称: privateKeySize\n          类型: size_t\n          描述: 私钥长度", "EncryptedData": "描述: 加密数据结构\n      字段:\n        - 名称: data\n          类型: uint8_t*\n          描述: 加密数据指针\n        - 名称: size\n          类型: size_t\n          描述: 加密数据大小\n        - 名称: primaryAlgorithm\n          类型: char[32]\n          描述: 主加密算法\n        - 名称: layerCount\n          类型: int\n          描述: 加密层数\n        - 名称: keyFingerprint\n          类型: uint8_t[32]\n          描述: 密钥指纹", "EncryptionVerificationResult": "描述: 加密验证结果\n      字段:\n        - 名称: isValid\n          类型: bool\n          描述: 加密是否有效\n        - 名称: encryptionStrength\n          类型: float\n          描述: 加密强度评分(0-10)\n        - 名称: errorMessage\n          类型: char[128]\n          描述: 错误信息(如有)"}}