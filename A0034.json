{"示例代码": {"代码注入示例": "int main() {\n    // 创建代码注入管理器\n    CodeInjector injector;\n    \n    // 配置代码注入选项\n    InjectionConfig config;\n    config.targetProcess = \"target_application\";\n    \n    // 可以通过进程ID或名称指定目标\n    // config.targetPid = 1234;\n    \n    // 设置要注入的代码类型\n    config.injectionType = INJECTION_SHARED_LIBRARY;\n    config.payloadPath = \"/path/to/malicious.so\";\n    \n    // 配置注入方法\n    InjectionMethod method;\n    method.primaryMethod = METHOD_PTRACE;\n    method.fallbackMethods = {METHOD_LD_PRELOAD, METHOD_FUNCTION_HOOKING};\n    \n    config.injectionMethod = method;\n    \n    // 设置高级注入选项\n    AdvancedInjectionOptions advOptions;\n    advOptions.waitForProcessIfNotExist = true;\n    advOptions.injectOnProcessStart = false;\n    advOptions.hideInjectionTraces = true;\n    advOptions.bypassSelinux = true;\n    \n    config.advancedOptions = advOptions;\n    \n    // 初始化注入器\n    if (!injector.initialize(config)) {\n        printf(\"Failed to initialize code injector: %s\\n\", \n               injector.getLastError().c_str());\n        return 1;\n    }\n    \n    printf(\"Code injector initialized\\n\");\n    printf(\"Target: %s\\n\", config.targetProcess.c_str());\n    printf(\"Injection type: %s\\n\", \n           injector.getInjectionTypeString(config.injectionType).c_str());\n    \n    // 预先检查注入条件\n    InjectionVerificationResult verifyResult = injector.verifyInjectionConditions();\n    if (!verifyResult.canProceed) {\n        printf(\"Cannot proceed with injection: %s\\n\", verifyResult.reason.c_str());\n        printf(\"Recommended actions:\\n\");\n        for (const auto& action : verifyResult.recommendedActions) {\n            printf(\"- %s\\n\", action.c_str());\n        }\n        return 1;\n    }\n    \n    // 执行代码注入\n    printf(\"\\nInjecting code...\\n\");\n    InjectionResult result = injector.inject();\n    \n    if (result.success) {\n        printf(\"Code injection successful!\\n\");\n        printf(\"Injection details:\\n\");\n        printf(\"- Target process ID: %d\\n\", result.targetPid);\n        printf(\"- Injection method used: %s\\n\", result.methodUsed.c_str());\n        printf(\"- Memory address: 0x%lx\\n\", result.injectedAddress);\n        \n        // 等待注入代码执行\n        printf(\"\\nWaiting for injected code to execute...\\n\");\n        InjectionExecutionStatus status = injector.waitForExecution(10000);\n        \n        if (status == EXEC_STATUS_COMPLETED) {\n            printf(\"Injected code executed successfully\\n\");\n            \n            // 获取执行结果（如果有）\n            ExecutionResult execResult = injector.getExecutionResult();\n            printf(\"Execution result code: %d\\n\", execResult.returnCode);\n            if (!execResult.output.empty()) {\n                printf(\"Output: %s\\n\", execResult.output.c_str());\n            }\n        } else if (status == EXEC_STATUS_RUNNING) {\n            printf(\"Injected code is still running (background thread)\\n\");\n        } else {\n            printf(\"Injected code execution failed or timed out\\n\");\n        }\n        \n        // 是否需要保持注入状态\n        bool keepInjected = true;\n        if (keepInjected) {\n            printf(\"\\nMaintaining injection...\\n\");\n            injector.maintainInjection();\n        } else {\n            // 移除注入的代码\n            printf(\"\\nRemoving injected code...\\n\");\n            bool removed = injector.removeInjection();\n            printf(\"Injected code %s\\n\", removed ? \"removed successfully\" : \"removal failed\");\n        }\n    } else {\n        printf(\"Code injection failed: %s\\n\", result.errorMessage.c_str());\n        printf(\"Attempted methods: %s\\n\", result.attemptedMethods.c_str());\n    }\n    \n    // 清理注入器资源\n    injector.cleanup();\n    \n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化代码注入器\n      参数:\n        - 名称: config\n          类型: InjectionConfig\n          描述: 注入配置\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: bool success = injector.initialize(config);", "verifyInjectionConditions": "功能: 验证代码注入条件\n      参数:\n      返回值:\n        类型: InjectionVerificationResult\n        描述: 验证结果\n      示例: InjectionVerificationResult result = injector.verifyInjectionConditions();", "inject": "功能: 执行代码注入\n      参数:\n      返回值:\n        类型: InjectionResult\n        描述: 注入结果\n      示例: InjectionResult result = injector.inject();", "waitForExecution": "功能: 等待注入代码执行完成\n      参数:\n        - 名称: timeoutMs\n          类型: uint32_t\n          描述: 超时时间（毫秒）\n      返回值:\n        类型: InjectionExecutionStatus\n        描述: 执行状态\n      示例: InjectionExecutionStatus status = injector.waitForExecution(10000);", "getExecutionResult": "功能: 获取执行结果\n      参数:\n      返回值:\n        类型: ExecutionResult\n        描述: 执行结果\n      示例: ExecutionResult result = injector.getExecutionResult();", "maintainInjection": "功能: 维持注入状态\n      参数:\n      返回值:\n        类型: bool\n        描述: 操作是否成功\n      示例: bool maintained = injector.maintainInjection();", "removeInjection": "功能: 移除注入的代码\n      参数:\n      返回值:\n        类型: bool\n        描述: 移除是否成功\n      示例: bool removed = injector.removeInjection();", "cleanup": "功能: 清理注入器资源\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: injector.cleanup();"}, "数据结构": {"InjectionConfig": "描述: 代码注入配置结构体\n      字段:\n        - 名称: targetProcess\n          类型: std::string\n          描述: 目标进程名称\n        - 名称: targetPid\n          类型: pid_t\n          描述: 目标进程ID\n        - 名称: injectionType\n          类型: InjectionType\n          描述: 注入类型\n        - 名称: payloadPath\n          类型: std::string\n          描述: payload路径\n        - 名称: injectionMethod\n          类型: InjectionMethod\n          描述: 注入方法\n        - 名称: advancedOptions\n          类型: AdvancedInjectionOptions\n          描述: 高级注入选项", "InjectionType": "描述: 注入类型枚举\n      字段:\n        - 名称: INJECTION_SHARED_LIBRARY\n          类型: enum\n          描述: 共享库注入\n        - 名称: INJECTION_SHELLCODE\n          类型: enum\n          描述: Shellcode注入\n        - 名称: INJECTION_FUNCTION_REPLACEMENT\n          类型: enum\n          描述: 函数替换\n        - 名称: INJECTION_CODE_CAVE\n          类型: enum\n          描述: 代码洞注入", "InjectionMethod": "描述: 注入方法结构体\n      字段:\n        - 名称: primaryMethod\n          类型: MethodType\n          描述: 主要注入方法\n        - 名称: fallbackMethods\n          类型: std::vector<MethodType>\n          描述: 备用注入方法", "MethodType": "描述: 注入方法类型枚举\n      字段:\n        - 名称: METHOD_PTRACE\n          类型: enum\n          描述: 使用ptrace注入\n        - 名称: METHOD_LD_PRELOAD\n          类型: enum\n          描述: 使用LD_PRELOAD注入\n        - 名称: METHOD_FUNCTION_HOOKING\n          类型: enum\n          描述: 函数钩子注入\n        - 名称: METHOD_MEMORY_MAPPING\n          类型: enum\n          描述: 内存映射注入\n        - 名称: METHOD_LINKER_HIJACK\n          类型: enum\n          描述: 链接器劫持", "AdvancedInjectionOptions": "描述: 高级注入选项结构体\n      字段:\n        - 名称: waitForProcessIfNotExist\n          类型: bool\n          描述: 如果进程不存在是否等待\n        - 名称: injectOnProcessStart\n          类型: bool\n          描述: 在进程启动时注入\n        - 名称: hideInjectionTraces\n          类型: bool\n          描述: 隐藏注入痕迹\n        - 名称: bypassSelinux\n          类型: bool\n          描述: 绕过SELinux\n        - 名称: customEntryPoint\n          类型: std::string\n          描述: 自定义入口点", "InjectionVerificationResult": "描述: 注入验证结果结构体\n      字段:\n        - 名称: canProceed\n          类型: bool\n          描述: 是否可以继续注入\n        - 名称: reason\n          类型: std::string\n          描述: 原因描述\n        - 名称: recommendedActions\n          类型: std::vector<std::string>\n          描述: 建议的操作", "InjectionResult": "描述: 注入结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 注入是否成功\n        - 名称: targetPid\n          类型: pid_t\n          描述: 目标进程ID\n        - 名称: methodUsed\n          类型: std::string\n          描述: 使用的注入方法\n        - 名称: injectedAddress\n          类型: uintptr_t\n          描述: 注入的内存地址\n        - 名称: attemptedMethods\n          类型: std::string\n          描述: 尝试过的注入方法\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误消息", "InjectionExecutionStatus": "描述: 注入执行状态枚举\n      字段:\n        - 名称: EXEC_STATUS_UNKNOWN\n          类型: enum\n          描述: 未知状态\n        - 名称: EXEC_STATUS_RUNNING\n          类型: enum\n          描述: 运行中\n        - 名称: EXEC_STATUS_COMPLETED\n          类型: enum\n          描述: 已完成\n        - 名称: EXEC_STATUS_FAILED\n          类型: enum\n          描述: 执行失败\n        - 名称: EXEC_STATUS_TIMEOUT\n          类型: enum\n          描述: 执行超时", "ExecutionResult": "描述: 执行结果结构体\n      字段:\n        - 名称: returnCode\n          类型: int\n          描述: 返回码\n        - 名称: output\n          类型: std::string\n          描述: 输出内容\n        - 名称: executionTime\n          类型: uint64_t\n          描述: 执行时间（毫秒）"}}