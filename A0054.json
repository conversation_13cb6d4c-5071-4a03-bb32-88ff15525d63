{"示例代码": {"删除端口转发规则示例": "int main() {\n    // 初始化端口转发管理器\n    PortForwardManager forwardManager;\n    \n    // 加载当前配置的转发规则\n    if (forwardManager.LoadForwardRules(\"/etc/portforward/rules.conf\")) {\n        // 获取所有已配置规则列表\n        FORWARD_RULE_ID rules[20];\n        uint8_t ruleCount = 0;\n        \n        if (forwardManager.GetConfiguredRules(rules, 20, &ruleCount)) {\n            printf(\"当前已配置的转发规则数量: %d\\n\", ruleCount);\n            \n            // 显示所有规则\n            for (int i = 0; i < ruleCount; i++) {\n                PORT_FORWARD_INFO info;\n                if (forwardManager.GetForwardRuleInfo(rules[i], &info)) {\n                    printf(\"%d. 规则ID %u: %s:%d -> %s:%d\\n\", i + 1, \n                           info.ruleId, info.localAddress, info.localPort, \n                           info.remoteAddress, info.remotePort);\n                }\n            }\n            \n            // 选择要删除的规则ID\n            if (ruleCount > 0) {\n                // 以第一个规则为例\n                FORWARD_RULE_ID ruleToDelete = rules[0];\n                \n                printf(\"\\n准备删除转发规则ID: %u\\n\", ruleToDelete);\n                \n                // 检查该规则是否有活动连接\n                uint8_t activeConnections = 0;\n                if (forwardManager.GetActiveConnectionCount(ruleToDelete, &activeConnections)) {\n                    printf(\"该规则当前有 %d 个活动连接\\n\", activeConnections);\n                    \n                    if (activeConnections > 0) {\n                        // 断开所有活动连接\n                        printf(\"正在断开相关连接...\\n\");\n                        forwardManager.DisconnectAllForwardConnections(ruleToDelete);\n                    }\n                }\n                \n                // 执行规则删除操作\n                DELETE_RULE_OPTIONS options;\n                memset(&options, 0, sizeof(DELETE_RULE_OPTIONS));\n                options.removeRelatedFiles = true;  // 移除相关文件\n                options.updateConfigFile = true;    // 更新配置文件\n                \n                if (forwardManager.DeleteForwardRule(ruleToDelete, &options)) {\n                    printf(\"转发规则删除成功\\n\");\n                    \n                    // 保存配置变更\n                    forwardManager.SaveForwardRules(\"/etc/portforward/rules.conf\");\n                    printf(\"配置已更新并保存\\n\");\n                } else {\n                    printf(\"删除转发规则失败: %s\\n\", forwardManager.GetLastError());\n                    return 1;\n                }\n            } else {\n                printf(\"没有配置任何转发规则\\n\");\n            }\n        } else {\n            printf(\"获取转发规则列表失败: %s\\n\", forwardManager.GetLastError());\n            return 1;\n        }\n    } else {\n        printf(\"加载转发规则配置失败: %s\\n\", forwardManager.GetLastError());\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"LoadForwardRules": "功能: 从配置文件加载转发规则\n      参数:\n        - 名称: configPath\n          类型: const char*\n          描述: 配置文件路径\n      返回值:\n        类型: bool\n        描述: 是否成功加载配置\n      示例: forwardManager.LoadForwardRules(\"/etc/portforward/rules.conf\")", "GetConfiguredRules": "功能: 获取所有已配置的转发规则列表\n      参数:\n        - 名称: rules\n          类型: FORWARD_RULE_ID*\n          描述: 规则ID数组\n        - 名称: maxCount\n          类型: uint8_t\n          描述: 数组最大容量\n        - 名称: count\n          类型: uint8_t*\n          描述: 返回实际规则数量\n      返回值:\n        类型: bool\n        描述: 是否成功获取规则列表\n      示例: forwardManager.GetConfiguredRules(rules, 20, &ruleCount)", "GetForwardRuleInfo": "功能: 获取转发规则信息\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 规则ID\n        - 名称: info\n          类型: PORT_FORWARD_INFO*\n          描述: 转发规则信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取规则信息\n      示例: forwardManager.GetForwardRuleInfo(rules[i], &info)", "GetActiveConnectionCount": "功能: 获取规则的活动连接数\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 规则ID\n        - 名称: count\n          类型: uint8_t*\n          描述: 返回活动连接数\n      返回值:\n        类型: bool\n        描述: 是否成功获取连接数\n      示例: forwardManager.GetActiveConnectionCount(ruleToDelete, &activeConnections)", "DisconnectAllForwardConnections": "功能: 断开指定规则的所有活动连接\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 规则ID\n      返回值:\n        类型: bool\n        描述: 是否成功断开所有连接\n      示例: forwardManager.DisconnectAllForwardConnections(ruleToDelete)", "DeleteForwardRule": "功能: 删除转发规则\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 规则ID\n        - 名称: options\n          类型: DELETE_RULE_OPTIONS*\n          描述: 删除选项结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功删除规则\n      示例: forwardManager.DeleteForwardRule(ruleToDelete, &options)", "SaveForwardRules": "功能: 保存转发规则配置\n      参数:\n        - 名称: configPath\n          类型: const char*\n          描述: 配置文件保存路径\n      返回值:\n        类型: bool\n        描述: 是否成功保存配置\n      示例: forwardManager.SaveForwardRules(\"/etc/portforward/rules.conf\")"}, "数据结构": {"DELETE_RULE_OPTIONS": "描述: 删除规则选项结构体\n      字段:\n        - 名称: removeRelatedFiles\n          类型: bool\n          描述: 是否移除相关文件\n        - 名称: updateConfigFile\n          类型: bool\n          描述: 是否更新配置文件\n        - 名称: forceDelete\n          类型: bool\n          描述: 是否强制删除(即使有活动连接)\n        - 名称: keepBackup\n          类型: bool\n          描述: 是否保留规则备份", "DELETE_RULE_RESULT": "描述: 删除规则结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 删除操作是否成功\n        - 名称: removedConnections\n          类型: uint8_t\n          描述: 移除的连接数量\n        - 名称: removedFiles\n          类型: uint8_t\n          描述: 移除的相关文件数量\n        - 名称: errorCode\n          类型: uint16_t\n          描述: 错误代码(如果失败)"}}