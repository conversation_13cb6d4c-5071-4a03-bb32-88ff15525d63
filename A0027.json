{"示例代码": {"文件加密示例": "int main() {\n    // 创建文件加密管理器\n    FileEncryptor encryptor;\n    \n    // 配置加密选项\n    EncryptionConfig config;\n    config.inputFilePath = \"/opt/sensitive/data.txt\";\n    config.outputFilePath = \"/opt/sensitive/data.enc\";\n    config.algorithm = ALGORITHM_AES_256_GCM;\n    config.deleteOriginal = true;\n    config.secureDelete = true;  // 安全删除原文件\n    \n    // 设置密钥\n    // 在实际应用中应使用安全的密钥生成方法\n    KeyOptions keyOpts;\n    keyOpts.usePassphrase = true;\n    keyOpts.passphrase = \"complex-passphrase-example\";\n    keyOpts.useKeyDerivation = true;\n    keyOpts.keyDerivationIterations = 10000;\n    keyOpts.useSalt = true;\n    \n    config.keyOptions = keyOpts;\n    \n    // 高级加密选项\n    AdvancedEncryptionOptions advOpts;\n    advOpts.embeddedMetadata = true;\n    advOpts.compressionBeforeEncryption = true;\n    advOpts.hiddenVolumeMode = false;\n    advOpts.resistFingerprinting = true;\n    \n    config.advancedOptions = advOpts;\n    \n    // 执行加密\n    EncryptionResult result = encryptor.encryptFile(config);\n    \n    if (result.success) {\n        printf(\"File encryption successful\\n\");\n        printf(\"Output file: %s\\n\", config.outputFilePath.c_str());\n        printf(\"Original size: %zu bytes\\n\", result.originalSize);\n        printf(\"Encrypted size: %zu bytes\\n\", result.encryptedSize);\n        printf(\"Encryption algorithm: %s\\n\", result.algorithmUsed.c_str());\n    } else {\n        printf(\"Encryption failed: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 解密示例\n    DecryptionConfig decConfig;\n    decConfig.inputFilePath = config.outputFilePath;\n    decConfig.outputFilePath = \"/opt/sensitive/data_decrypted.txt\";\n    \n    // 使用与加密相同的密钥选项\n    decConfig.keyOptions = keyOpts;\n    \n    // 执行解密\n    DecryptionResult decResult = encryptor.decryptFile(decConfig);\n    \n    if (decResult.success) {\n        printf(\"File decryption successful\\n\");\n        printf(\"Output file: %s\\n\", decConfig.outputFilePath.c_str());\n    } else {\n        printf(\"Decryption failed: %s\\n\", decResult.errorMessage.c_str());\n    }\n    \n    return 0;\n}"}, "API接口": {"encryptFile": "功能: 加密文件\n      参数:\n        - 名称: config\n          类型: EncryptionConfig\n          描述: 加密配置\n      返回值:\n        类型: EncryptionResult\n        描述: 加密结果\n      示例: EncryptionResult result = encryptor.encryptFile(config);", "decryptFile": "功能: 解密文件\n      参数:\n        - 名称: config\n          类型: DecryptionConfig\n          描述: 解密配置\n      返回值:\n        类型: DecryptionResult\n        描述: 解密结果\n      示例: DecryptionResult result = encryptor.decryptFile(config);", "generateKey": "功能: 生成加密密钥\n      参数:\n        - 名称: keyOptions\n          类型: KeyOptions\n          描述: 密钥生成选项\n      返回值:\n        类型: KeyResult\n        描述: 密钥生成结果\n      示例: KeyResult keyResult = encryptor.generateKey(keyOptions);", "getSupportedAlgorithms": "功能: 获取支持的加密算法\n      参数:\n      返回值:\n        类型: std::vector<AlgorithmInfo>\n        描述: 支持的算法信息列表\n      示例: auto algorithms = encryptor.getSupportedAlgorithms();", "verifyFileIntegrity": "功能: 验证加密文件的完整性\n      参数:\n        - 名称: filePath\n          类型: const std::string&\n          描述: 加密文件路径\n        - 名称: keyOptions\n          类型: KeyOptions\n          描述: 密钥选项\n      返回值:\n        类型: IntegrityResult\n        描述: 完整性验证结果\n      示例: IntegrityResult result = encryptor.verifyFileIntegrity(filePath, keyOpts);"}, "数据结构": {"EncryptionConfig": "描述: 文件加密配置结构体\n      字段:\n        - 名称: inputFilePath\n          类型: std::string\n          描述: 输入文件路径\n        - 名称: outputFilePath\n          类型: std::string\n          描述: 输出文件路径\n        - 名称: algorithm\n          类型: EncryptionAlgorithm\n          描述: 加密算法\n        - 名称: deleteOriginal\n          类型: bool\n          描述: 是否删除原始文件\n        - 名称: secureDelete\n          类型: bool\n          描述: 是否安全删除原始文件\n        - 名称: keyOptions\n          类型: KeyOptions\n          描述: 密钥选项\n        - 名称: advancedOptions\n          类型: AdvancedEncryptionOptions\n          描述: 高级加密选项", "EncryptionAlgorithm": "描述: 加密算法枚举\n      字段:\n        - 名称: ALGORITHM_AES_256_GCM\n          类型: enum\n          描述: AES-256-GCM加密\n        - 名称: ALGORITHM_AES_256_CBC\n          类型: enum\n          描述: AES-256-CBC加密\n        - 名称: ALGORITHM_CHACHA20_POLY1305\n          类型: enum\n          描述: ChaCha20-Poly1305加密\n        - 名称: ALGORITHM_SERPENT_256\n          类型: enum\n          描述: Serpent-256加密\n        - 名称: ALGORITHM_TWOFISH_256\n          类型: enum\n          描述: Twofish-256加密", "KeyOptions": "描述: 密钥选项结构体\n      字段:\n        - 名称: usePassphrase\n          类型: bool\n          描述: 是否使用口令\n        - 名称: passphrase\n          类型: std::string\n          描述: 密码口令\n        - 名称: useKeyDerivation\n          类型: bool\n          描述: 是否使用密钥派生\n        - 名称: keyDerivationIterations\n          类型: uint32_t\n          描述: 密钥派生迭代次数\n        - 名称: useSalt\n          类型: bool\n          描述: 是否使用盐值\n        - 名称: customSalt\n          类型: std::vector<uint8_t>\n          描述: 自定义盐值", "AdvancedEncryptionOptions": "描述: 高级加密选项结构体\n      字段:\n        - 名称: embeddedMetadata\n          类型: bool\n          描述: 是否嵌入元数据\n        - 名称: compressionBeforeEncryption\n          类型: bool\n          描述: 是否在加密前压缩\n        - 名称: hiddenVolumeMode\n          类型: bool\n          描述: 是否使用隐藏卷模式\n        - 名称: resistFingerprinting\n          类型: bool\n          描述: 是否抵抗指纹识别\n        - 名称: customIV\n          类型: std::vector<uint8_t>\n          描述: 自定义初始化向量", "EncryptionResult": "描述: 加密结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 加密是否成功\n        - 名称: originalSize\n          类型: size_t\n          描述: 原始文件大小\n        - 名称: encryptedSize\n          类型: size_t\n          描述: 加密后文件大小\n        - 名称: algorithmUsed\n          类型: std::string\n          描述: 使用的加密算法\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: keyFingerprint\n          类型: std::string\n          描述: 密钥指纹（用于验证）", "DecryptionConfig": "描述: 文件解密配置结构体\n      字段:\n        - 名称: inputFilePath\n          类型: std::string\n          描述: 输入加密文件路径\n        - 名称: outputFilePath\n          类型: std::string\n          描述: 输出解密文件路径\n        - 名称: keyOptions\n          类型: KeyOptions\n          描述: 密钥选项\n        - 名称: validateIntegrity\n          类型: bool\n          描述: 是否验证完整性", "DecryptionResult": "描述: 解密结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 解密是否成功\n        - 名称: encryptedSize\n          类型: size_t\n          描述: 加密文件大小\n        - 名称: decryptedSize\n          类型: size_t\n          描述: 解密文件大小\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: integrityVerified\n          类型: bool\n          描述: 完整性是否验证"}}