{"示例代码": {"检测当前权限示例": "// Linux系统中使用C语言检测当前运行权限\n#include <stdio.h>\n#include <stdlib.h>\n#include <unistd.h>\n#include <pwd.h>\n#include <grp.h>\n\nint main() {\n    // 获取进程用户ID和组ID\n    uid_t uid = getuid();\n    uid_t euid = geteuid();\n    gid_t gid = getgid();\n    gid_t egid = getegid();\n    \n    // 获取用户信息\n    struct passwd *pw = getpwuid(uid);\n    if (pw == NULL) {\n        perror(\"getpwuid失败\");\n        return 1;\n    }\n    \n    // 判断是否为root权限\n    int is_root = (euid == 0) ? 1 : 0;\n    \n    // 输出权限信息\n    printf(\"用户ID: %d\\n\", uid);\n    printf(\"有效用户ID: %d\\n\", euid);\n    printf(\"组ID: %d\\n\", gid);\n    printf(\"有效组ID: %d\\n\", egid);\n    printf(\"用户名: %s\\n\", pw->pw_name);\n    printf(\"是否拥有root权限: %s\\n\", is_root ? \"是\" : \"否\");\n    \n    // 检测可用组\n    int ngroups = 0;\n    getgroups(0, NULL); // 获取组数量\n    gid_t *groups = malloc(ngroups * sizeof(gid_t));\n    if (groups == NULL) {\n        perror(\"内存分配失败\");\n        return 1;\n    }\n    \n    if (getgroups(ngroups, groups) != -1) {\n        printf(\"用户所在组: \");\n        for (int i = 0; i < ngroups; i++) {\n            struct group *gr = getgrgid(groups[i]);\n            if (gr != NULL) {\n                printf(\"%s \", gr->gr_name);\n            }\n        }\n        printf(\"\\n\");\n    }\n    \n    free(groups);\n    return 0;\n}"}, "API接口": {"CheckCurrentPrivileges": "功能: 检测当前进程的权限级别\n      参数:\n      返回值:\n        类型: int\n        描述: 权限级别：0=普通用户, 1=root权限, -1=检测失败\n      示例: int privilege_level = CheckCurrentPrivileges()", "GetUserInfo": "功能: 获取当前用户信息\n      参数:\n      返回值:\n        类型: UserInfo*\n        描述: 用户信息结构体指针\n      示例: UserInfo* info = GetUserInfo()", "GetGroupMembership": "功能: 获取当前进程所属的组列表\n      参数:\n      返回值:\n        类型: GroupList*\n        描述: 组列表结构体指针\n      示例: GroupList* groups = GetGroupMembership()", "CanExecuteCommand": "功能: 检测是否有权限执行指定命令\n      参数:\n        - 名称: command_path\n          类型: const char*\n          描述: 命令路径\n      返回值:\n        类型: bool\n        描述: 是否有执行权限\n      示例: bool can_execute = CanExecuteCommand(\"/usr/bin/sudo\")"}, "数据结构": {"UserInfo": "描述: 用户信息结构体\n      字段:\n        - 名称: uid\n          类型: uid_t\n          描述: 用户ID\n        - 名称: euid\n          类型: uid_t\n          描述: 有效用户ID\n        - 名称: username\n          类型: char[64]\n          描述: 用户名\n        - 名称: home_dir\n          类型: char[256]\n          描述: 用户主目录\n        - 名称: is_root\n          类型: bool\n          描述: 是否具有root权限", "GroupList": "描述: 用户组列表结构体\n      字段:\n        - 名称: primary_gid\n          类型: gid_t\n          描述: 主组ID\n        - 名称: primary_group_name\n          类型: char[64]\n          描述: 主组名称\n        - 名称: group_count\n          类型: int\n          描述: 组数量\n        - 名称: groups\n          类型: gid_t[]\n          描述: 组ID数组\n        - 名称: group_names\n          类型: char*[]\n          描述: 组名称数组", "PermissionStatus": "描述: 权限状态结构体\n      字段:\n        - 名称: can_read_system_files\n          类型: bool\n          描述: 是否可读系统文件\n        - 名称: can_write_system_files\n          类型: bool\n          描述: 是否可写系统文件\n        - 名称: can_modify_services\n          类型: bool\n          描述: 是否可修改系统服务\n        - 名称: can_access_devices\n          类型: bool\n          描述: 是否可访问设备文件\n        - 名称: sudo_access\n          类型: bool\n          描述: 是否有sudo权限"}}