{"示例代码": {"查看漏洞结果示例": "int main() {\n    // 初始化漏洞结果查询模块\n    ExploitResultViewer viewer;\n    \n    // 连接到目标系统或日志服务器\n    if (!viewer.Connect(\"*************\", 8080)) {\n        printf(\"连接失败，无法获取漏洞结果\\n\");\n        return 1;\n    }\n    \n    // 设置查询参数\n    ResultQueryParams params;\n    params.exploitId = \"EXP-20230512-001\";\n    params.timeRange = 3600; // 过去一小时内的结果\n    params.maxResults = 50;\n    \n    // 执行查询\n    ResultCollection results;\n    if (viewer.QueryResults(params, &results)) {\n        // 遍历并显示结果\n        printf(\"共找到 %d 条漏洞执行结果\\n\", results.count);\n        \n        for (int i = 0; i < results.count; i++) {\n            ExploitResultEntry* entry = &results.entries[i];\n            printf(\"结果 #%d:\\n\", i + 1);\n            printf(\"  时间戳: %s\\n\", entry->timestamp);\n            printf(\"  目标系统: %s\\n\", entry->targetSystem);\n            printf(\"  漏洞ID: %s\\n\", entry->vulnerabilityId);\n            printf(\"  执行状态: %s\\n\", entry->status ? \"成功\" : \"失败\");\n            printf(\"  获取权限: %s\\n\", entry->accessLevel);\n            printf(\"  持久化状态: %s\\n\", entry->persistenceEstablished ? \"已建立\" : \"未建立\");\n        }\n        \n        // 导出详细结果报告\n        viewer.ExportResultsReport(results, \"exploit_report.json\");\n    } else {\n        printf(\"查询漏洞结果失败\\n\");\n    }\n    \n    // 断开连接\n    viewer.Disconnect();\n    \n    return 0;\n}"}, "API接口": {"Connect": "功能: 连接到结果收集服务器或目标系统\n      参数:\n        - 名称: host\n          类型: const char*\n          描述: 服务器主机名或IP地址\n        - 名称: port\n          类型: uint16_t\n          描述: 服务器端口\n      返回值:\n        类型: bool\n        描述: 连接是否成功\n      示例: viewer.Connect(\"*************\", 8080)", "QueryResults": "功能: 查询漏洞执行结果\n      参数:\n        - 名称: params\n          类型: ResultQueryParams\n          描述: 查询参数配置\n        - 名称: results\n          类型: ResultCollection*\n          描述: 结果集合输出指针\n      返回值:\n        类型: bool\n        描述: 查询是否成功\n      示例: viewer.QueryResults(params, &results)", "GetResultDetail": "功能: 获取特定漏洞执行结果的详细信息\n      参数:\n        - 名称: resultId\n          类型: const char*\n          描述: 结果标识符\n        - 名称: detail\n          类型: ExploitResultDetail*\n          描述: 详细结果输出指针\n      返回值:\n        类型: bool\n        描述: 获取详情是否成功\n      示例: viewer.GetResultDetail(resultId, &detail)", "ExportResultsReport": "功能: 导出漏洞执行结果报告\n      参数:\n        - 名称: results\n          类型: ResultCollection\n          描述: 结果集合\n        - 名称: filePath\n          类型: const char*\n          描述: 报告文件保存路径\n      返回值:\n        类型: bool\n        描述: 导出是否成功\n      示例: viewer.ExportResultsReport(results, \"exploit_report.json\")", "Disconnect": "功能: 断开与结果服务器的连接\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: viewer.Disconnect()"}, "数据结构": {"ResultQueryParams": "描述: 漏洞结果查询参数\n      字段:\n        - 名称: exploitId\n          类型: char[64]\n          描述: 漏洞攻击ID标识符\n        - 名称: timeRange\n          类型: uint32_t\n          描述: 查询时间范围（秒）\n        - 名称: maxResults\n          类型: uint32_t\n          描述: 最大返回结果数量\n        - 名称: filterOptions\n          类型: uint32_t\n          描述: 过滤选项标志位", "ExploitResultEntry": "描述: 漏洞执行结果条目\n      字段:\n        - 名称: resultId\n          类型: char[64]\n          描述: 结果唯一标识符\n        - 名称: timestamp\n          类型: char[32]\n          描述: 执行时间戳\n        - 名称: targetSystem\n          类型: char[128]\n          描述: 目标系统标识\n        - 名称: vulnerabilityId\n          类型: char[64]\n          描述: 漏洞CVE编号或标识符\n        - 名称: status\n          类型: bool\n          描述: 执行状态（成功/失败）\n        - 名称: accessLevel\n          类型: char[32]\n          描述: 获取的权限级别\n        - 名称: persistenceEstablished\n          类型: bool\n          描述: 是否建立了持久化", "ResultCollection": "描述: 漏洞执行结果集合\n      字段:\n        - 名称: count\n          类型: uint32_t\n          描述: 结果条目数量\n        - 名称: entries\n          类型: ExploitResultEntry[]\n          描述: 结果条目数组\n        - 名称: hasMore\n          类型: bool\n          描述: 是否还有更多结果\n        - 名称: queryTime\n          类型: uint64_t\n          描述: 查询执行时间戳"}}