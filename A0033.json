{"示例代码": {"日志伪造示例": "int main() {\n    // 创建日志伪造管理器\n    LogForger forger;\n    \n    // 设置基本伪造选项\n    LogForgingConfig config;\n    config.targetLogFile = \"/var/log/auth.log\";\n    config.backupOriginal = true;  // 备份原始文件\n    config.preserveTimestamps = true;  // 保留文件时间戳\n    \n    // 设置日志修改选项\n    ModificationOptions modOptions;\n    modOptions.removePatterns = true;\n    modOptions.addFakeEntries = true;\n    modOptions.preserveFileSize = false;  // 允许文件大小变化\n    \n    // 添加要删除的模式\n    modOptions.addRemovalPattern(\"Failed password for user1\");  // 移除登录失败记录\n    modOptions.addRemovalPattern(\"session opened for user root\");  // 移除root登录记录\n    modOptions.addRemovalPattern(\"sshd\\\\[\\\\d+\\\\]: Invalid user attacker\");  // 使用正则表达式\n    \n    // 添加要伪造的条目\n    LogEntry fakeEntry1;\n    fakeEntry1.timestamp = \"Jun 15 08:32:15\";\n    fakeEntry1.hostname = \"server1\";\n    fakeEntry1.process = \"sshd[12345]\";\n    fakeEntry1.content = \"Invalid user baduser from ******* port 49812\";\n    \n    LogEntry fakeEntry2;\n    fakeEntry2.timestamp = \"Jun 15 20:45:30\";\n    fakeEntry2.hostname = \"server1\";\n    fakeEntry2.process = \"sudo\";\n    fakeEntry2.content = \"pam_unix(sudo:auth): authentication failure; logname=legituser uid=1000 euid=0 tty=/dev/pts/0 ruser=legituser rhost= user=legituser\";\n    \n    modOptions.addFakeEntry(fakeEntry1);\n    modOptions.addFakeEntry(fakeEntry2);\n    \n    config.modificationOptions = modOptions;\n    \n    // 设置日志文件工具特定选项\n    LogToolOptions toolOptions;\n    toolOptions.handleLogrotate = true;  // 处理logrotate情况\n    toolOptions.handleCompressedLogs = true;  // 处理压缩日志\n    toolOptions.handleJournald = true;  // 处理systemd日志\n    \n    config.toolOptions = toolOptions;\n    \n    // 初始化日志伪造器\n    if (!forger.initialize(config)) {\n        printf(\"Failed to initialize log forger: %s\\n\", forger.getLastError().c_str());\n        return 1;\n    }\n    \n    // 执行日志伪造\n    printf(\"Forging logs...\\n\");\n    ForgingResult result = forger.forgeLog();\n    \n    if (result.success) {\n        printf(\"Log forging completed successfully\\n\");\n        printf(\"Modified log file: %s\\n\", result.modifiedLogPath.c_str());\n        printf(\"Removed entries: %d\\n\", result.removedEntries);\n        printf(\"Added fake entries: %d\\n\", result.addedEntries);\n        \n        if (!result.backupPath.empty()) {\n            printf(\"Original log backed up to: %s\\n\", result.backupPath.c_str());\n        }\n    } else {\n        printf(\"Log forging failed: %s\\n\", result.errorMessage.c_str());\n    }\n    \n    // 验证伪造效果\n    VerificationResult verifyResult = forger.verifyForging();\n    if (verifyResult.forgingDetectable) {\n        printf(\"\\nWarning: Forging may be detectable:\\n\");\n        for (const auto& issue : verifyResult.detectionVectors) {\n            printf(\"- %s\\n\", issue.c_str());\n        }\n        printf(\"Stealth score: %.1f/10.0\\n\", verifyResult.stealthScore);\n    } else {\n        printf(\"\\nForging appears undetectable\\n\");\n        printf(\"Stealth score: %.1f/10.0\\n\", verifyResult.stealthScore);\n    }\n    \n    // 清理操作（可选）\n    if (result.success) {\n        bool cleanupSuccess = forger.cleanupTraces();\n        printf(\"\\nTrace cleanup %s\\n\", cleanupSuccess ? \"succeeded\" : \"failed\");\n    }\n    \n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化日志伪造器\n      参数:\n        - 名称: config\n          类型: LogForgingConfig\n          描述: 日志伪造配置\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: bool success = forger.initialize(config);", "forgeLog": "功能: 执行日志伪造操作\n      参数:\n      返回值:\n        类型: ForgingResult\n        描述: 伪造操作结果\n      示例: ForgingResult result = forger.forgeLog();", "verifyForging": "功能: 验证伪造效果\n      参数:\n      返回值:\n        类型: VerificationResult\n        描述: 验证结果\n      示例: VerificationResult result = forger.verifyForging();", "addRemovalPattern": "功能: 添加要移除的日志模式\n      参数:\n        - 名称: pattern\n          类型: const std::string&\n          描述: 匹配模式（支持正则表达式）\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: modOptions.addRemovalPattern(\"Failed password for user1\");", "addFakeEntry": "功能: 添加伪造的日志条目\n      参数:\n        - 名称: entry\n          类型: LogEntry\n          描述: 伪造的日志条目\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: modOptions.addFakeEntry(fakeEntry);", "cleanupTraces": "功能: 清理伪造操作痕迹\n      参数:\n      返回值:\n        类型: bool\n        描述: 清理是否成功\n      示例: bool success = forger.cleanupTraces();"}, "数据结构": {"LogForgingConfig": "描述: 日志伪造配置结构体\n      字段:\n        - 名称: targetLogFile\n          类型: std::string\n          描述: 目标日志文件路径\n        - 名称: backupOriginal\n          类型: bool\n          描述: 是否备份原始文件\n        - 名称: preserveTimestamps\n          类型: bool\n          描述: 是否保留文件时间戳\n        - 名称: modificationOptions\n          类型: ModificationOptions\n          描述: 修改选项\n        - 名称: toolOptions\n          类型: LogToolOptions\n          描述: 日志工具特定选项", "ModificationOptions": "描述: 日志修改选项结构体\n      字段:\n        - 名称: removePatterns\n          类型: bool\n          描述: 是否删除匹配的模式\n        - 名称: addFakeEntries\n          类型: bool\n          描述: 是否添加伪造条目\n        - 名称: preserveFileSize\n          类型: bool\n          描述: 是否保持文件大小不变\n        - 名称: removalPatterns\n          类型: std::vector<std::string>\n          描述: 要移除的模式列表\n        - 名称: fakeEntries\n          类型: std::vector<LogEntry>\n          描述: 伪造条目列表", "LogEntry": "描述: 日志条目结构体\n      字段:\n        - 名称: timestamp\n          类型: std::string\n          描述: 时间戳\n        - 名称: hostname\n          类型: std::string\n          描述: 主机名\n        - 名称: process\n          类型: std::string\n          描述: 进程名称和PID\n        - 名称: content\n          类型: std::string\n          描述: 日志内容\n        - 名称: priority\n          类型: LogPriority\n          描述: 日志优先级", "LogPriority": "描述: 日志优先级枚举\n      字段:\n        - 名称: LOG_EMERG\n          类型: enum\n          描述: 紧急情况\n        - 名称: LOG_ALERT\n          类型: enum\n          描述: 需要立即采取行动\n        - 名称: LOG_CRIT\n          类型: enum\n          描述: 临界条件\n        - 名称: LOG_ERR\n          类型: enum\n          描述: 错误条件\n        - 名称: LOG_WARNING\n          类型: enum\n          描述: 警告条件\n        - 名称: LOG_NOTICE\n          类型: enum\n          描述: 正常但重要的条件\n        - 名称: LOG_INFO\n          类型: enum\n          描述: 信息消息\n        - 名称: LOG_DEBUG\n          类型: enum\n          描述: 调试信息", "LogToolOptions": "描述: 日志工具特定选项结构体\n      字段:\n        - 名称: handleLogrotate\n          类型: bool\n          描述: 是否处理logrotate情况\n        - 名称: handleCompressedLogs\n          类型: bool\n          描述: 是否处理压缩日志\n        - 名称: handleJournald\n          类型: bool\n          描述: 是否处理systemd日志\n        - 名称: useAtomicOperations\n          类型: bool\n          描述: 是否使用原子操作", "ForgingResult": "描述: 伪造操作结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 操作是否成功\n        - 名称: modifiedLogPath\n          类型: std::string\n          描述: 已修改的日志路径\n        - 名称: backupPath\n          类型: std::string\n          描述: 备份文件路径\n        - 名称: removedEntries\n          类型: int\n          描述: 移除的条目数量\n        - 名称: addedEntries\n          类型: int\n          描述: 添加的伪造条目数量\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息", "VerificationResult": "描述: 验证结果结构体\n      字段:\n        - 名称: forgingDetectable\n          类型: bool\n          描述: 伪造是否可被检测\n        - 名称: stealthScore\n          类型: float\n          描述: 隐蔽性评分（0-10）\n        - 名称: detectionVectors\n          类型: std::vector<std::string>\n          描述: 可能的检测向量\n        - 名称: suggestedImprovements\n          类型: std::vector<std::string>\n          描述: 建议的改进措施"}}