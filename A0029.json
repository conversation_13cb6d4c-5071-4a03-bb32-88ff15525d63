{"示例代码": {"进程隐藏示例": "int main() {\n    // 创建进程隐藏管理器\n    ProcessHider hider;\n    \n    // 配置进程隐藏选项\n    ProcessHidingConfig config;\n    config.hideSelf = true;  // 隐藏自身进程\n    config.hideChildren = true;  // 隐藏子进程\n    \n    // 添加要隐藏的额外进程\n    config.addProcessToHide(\"malware_process\"); // 通过进程名\n    config.addPidToHide(1234);  // 通过PID\n    \n    // 设置隐藏技术\n    HidingTechniques techniques;\n    techniques.usePtraceDetach = true;  // 使用ptrace分离技术\n    techniques.useProcFSHook = true;   // hook /proc文件系统访问\n    techniques.useProcessListHook = true;  // hook进程列表函数\n    techniques.useSyscallHook = true;  // hook系统调用\n    \n    config.techniques = techniques;\n    \n    // 设置高级隐藏选项\n    AdvancedHidingOptions advOptions;\n    advOptions.hideNetworkConnections = true;  // 隐藏网络连接\n    advOptions.hideFileHandles = true;  // 隐藏文件句柄\n    advOptions.hideMemoryUsage = true;  // 隐藏内存使用\n    advOptions.hideCpuUsage = true;  // 隐藏CPU使用\n    \n    config.advancedOptions = advOptions;\n    \n    // 设置隐藏持久化选项\n    PersistenceOptions persistence;\n    persistence.reattachOnDiscovery = true;  // 被发现时重新隐藏\n    persistence.useBackupTechnique = true;  // 使用备用隐藏技术\n    \n    config.persistenceOptions = persistence;\n    \n    // 初始化进程隐藏\n    HidingResult result = hider.initializeHiding(config);\n    \n    if (result.success) {\n        printf(\"Process hiding initialized successfully\\n\");\n        printf(\"Applied techniques: %d\\n\", result.techniquesApplied);\n    } else {\n        printf(\"Failed to initialize process hiding: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 验证隐藏效果\n    VerificationResult verifyResult = hider.verifyHiding();\n    if (verifyResult.isHidden) {\n        printf(\"Verification passed: Process is hidden\\n\");\n        printf(\"Hiding score: %.1f/10.0\\n\", verifyResult.hidingScore);\n    } else {\n        printf(\"Verification failed: Process is still visible\\n\");\n        printf(\"Visible to: %s\\n\", verifyResult.visibleTo.c_str());\n    }\n    \n    // 程序主循环\n    printf(\"Running hidden. Press Ctrl+C to exit...\\n\");\n    while (true) {\n        // 维持隐藏状态\n        hider.maintainHiding();\n        sleep(5);\n    }\n    \n    // 注意：此代码实际上不会执行，因为上面是无限循环\n    // 关闭隐藏功能\n    hider.stopHiding();\n    \n    return 0;\n}"}, "API接口": {"initializeHiding": "功能: 初始化进程隐藏功能\n      参数:\n        - 名称: config\n          类型: ProcessHidingConfig\n          描述: 进程隐藏配置\n      返回值:\n        类型: HidingResult\n        描述: 隐藏初始化结果\n      示例: HidingResult result = hider.initializeHiding(config);", "addProcessToHide": "功能: 添加要隐藏的进程名称\n      参数:\n        - 名称: processName\n          类型: const std::string&\n          描述: 进程名称\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: config.addProcessToHide(\"malware_process\");", "addPidToHide": "功能: 添加要隐藏的进程ID\n      参数:\n        - 名称: pid\n          类型: pid_t\n          描述: 进程ID\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: config.addPidToHide(1234);", "verifyHiding": "功能: 验证进程隐藏效果\n      参数:\n      返回值:\n        类型: VerificationResult\n        描述: 验证结果\n      示例: VerificationResult result = hider.verifyHiding();", "maintainHiding": "功能: 维持进程隐藏状态\n      参数:\n      返回值:\n        类型: bool\n        描述: 维持操作是否成功\n      示例: bool maintained = hider.maintainHiding();", "stopHiding": "功能: 停止进程隐藏\n      参数:\n      返回值:\n        类型: bool\n        描述: 停止操作是否成功\n      示例: bool stopped = hider.stopHiding();"}, "数据结构": {"ProcessHidingConfig": "描述: 进程隐藏配置结构体\n      字段:\n        - 名称: hideSelf\n          类型: bool\n          描述: 是否隐藏自身进程\n        - 名称: hideChildren\n          类型: bool\n          描述: 是否隐藏子进程\n        - 名称: processesToHide\n          类型: std::vector<std::string>\n          描述: 要隐藏的进程名称列表\n        - 名称: pidsToHide\n          类型: std::vector<pid_t>\n          描述: 要隐藏的进程ID列表\n        - 名称: techniques\n          类型: HidingTechniques\n          描述: 使用的隐藏技术\n        - 名称: advancedOptions\n          类型: AdvancedHidingOptions\n          描述: 高级隐藏选项\n        - 名称: persistenceOptions\n          类型: PersistenceOptions\n          描述: 隐藏持久化选项", "HidingTechniques": "描述: 隐藏技术结构体\n      字段:\n        - 名称: usePtraceDetach\n          类型: bool\n          描述: 是否使用ptrace分离技术\n        - 名称: useProcFSHook\n          类型: bool\n          描述: 是否hook /proc文件系统访问\n        - 名称: useProcessListHook\n          类型: bool\n          描述: 是否hook进程列表函数\n        - 名称: useSyscallHook\n          类型: bool\n          描述: 是否hook系统调用\n        - 名称: useLibraryInjection\n          类型: bool\n          描述: 是否使用库注入技术", "AdvancedHidingOptions": "描述: 高级隐藏选项结构体\n      字段:\n        - 名称: hideNetworkConnections\n          类型: bool\n          描述: 是否隐藏网络连接\n        - 名称: hideFileHandles\n          类型: bool\n          描述: 是否隐藏文件句柄\n        - 名称: hideMemoryUsage\n          类型: bool\n          描述: 是否隐藏内存使用\n        - 名称: hideCpuUsage\n          类型: bool\n          描述: 是否隐藏CPU使用\n        - 名称: fakeProcessName\n          类型: std::string\n          描述: 伪装的进程名称", "PersistenceOptions": "描述: 隐藏持久化选项结构体\n      字段:\n        - 名称: reattachOnDiscovery\n          类型: bool\n          描述: 被发现时是否重新隐藏\n        - 名称: useBackupTechnique\n          类型: bool\n          描述: 是否使用备用隐藏技术\n        - 名称: checkInterval\n          类型: uint32_t\n          描述: 检查隐藏状态的间隔（秒）\n        - 名称: maxReattachAttempts\n          类型: uint32_t\n          描述: 最大重新隐藏尝试次数", "HidingResult": "描述: 隐藏结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 隐藏是否成功\n        - 名称: techniquesApplied\n          类型: uint32_t\n          描述: 应用的技术数量\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: hiddenPids\n          类型: std::vector<pid_t>\n          描述: 成功隐藏的进程ID列表\n        - 名称: failedPids\n          类型: std::vector<pid_t>\n          描述: 隐藏失败的进程ID列表", "VerificationResult": "描述: 验证结果结构体\n      字段:\n        - 名称: isHidden\n          类型: bool\n          描述: 进程是否成功隐藏\n        - 名称: hidingScore\n          类型: float\n          描述: 隐藏效果评分（0-10）\n        - 名称: visibleTo\n          类型: std::string\n          描述: 如果可见，说明对哪些工具可见\n        - 名称: detectionMethods\n          类型: std::vector<std::string>\n          描述: 可被检测到的方法列表\n        - 名称: suggestedImprovements\n          类型: std::vector<std::string>\n          描述: 建议的改进措施"}}