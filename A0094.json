{"示例代码": {"说明": "暂无"}, "API接口": {"RemoteLoadSharedObject": "功能: 远程加载指定的共享对象(.so)文件并执行其中的函数。\n      参数:\n        - 名称: so_file_path_on_target\n          类型: const char*\n          描述: 目标机器上.so文件的路径。如果文件不存在，可能需要先传输。\n        - 名称: function_name\n          类型: const char*\n          描述: .so文件中要调用的函数名。\n        - 名称: function_arg\n          类型: void*\n          描述: 传递给目标函数的参数 (可选，类型需与目标函数匹配)。\n        - 名称: return_value_out\n          类型: void**\n          描述: 用于接收目标函数返回值的指针 (可选，类型需与目标函数匹配)。\n      返回值:\n        类型: int\n        描述: 操作状态，0表示成功加载并调用，非0表示失败 (例如文件未找到、函数未找到、执行错误)。\n      示例: void* func_ret_val = NULL; if (RemoteLoadSharedObject(\"/tmp/payload.so\", \"exported_function\", (void*)\"arg_string\", &func_ret_val) == 0) { /* 函数调用成功 */ }"}}