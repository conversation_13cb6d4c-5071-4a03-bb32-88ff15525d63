{"示例代码": {"发送命令示例": "int main() {\n    // 初始化命令控制器\n    CommandController controller;\n    \n    // 创建命令数据\n    COMMAND_DATA cmdData;\n    strcpy(cmdData.command, \"ls -la\");\n    cmdData.type = CMD_TYPE_SHELL;\n    \n    // 发送命令到受控端\n    if (!controller.SendCommand(targetClient, &cmdData)) {\n        printf(\"命令发送失败\\n\");\n        return 1;\n    }\n    \n    printf(\"命令已发送，等待执行结果\\n\");\n    return 0;\n}"}, "API接口": {"SendCommand": "功能: 向受控端发送Shell指令\n      参数:\n        - 名称: targetClient\n          类型: CLIENT_HANDLE\n          描述: 目标客户端句柄\n        - 名称: cmdData\n          类型: COMMAND_DATA*\n          描述: 命令数据结构指针\n      返回值:\n        类型: bool\n        描述: 命令是否成功发送\n      示例: controller.SendCommand(targetClient, &cmdData)", "CreateShellCommand": "功能: 创建Shell命令对象\n      参数:\n        - 名称: commandText\n          类型: const char*\n          描述: 要执行的Shell命令文本\n      返回值:\n        类型: COMMAND_DATA\n        描述: 包含命令信息的数据结构\n      示例: COMMAND_DATA cmd = controller.CreateShellCommand(\"ps -ef\");"}, "数据结构": {"COMMAND_DATA": "描述: Shell命令数据结构\n      字段:\n        - 名称: type\n          类型: BYTE\n          描述: 命令类型，如CMD_TYPE_SHELL\n        - 名称: command\n          类型: char[1024]\n          描述: Shell命令文本内容\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 命令发送时间戳\n        - 名称: commandId\n          类型: uint32_t\n          描述: 命令唯一标识符", "CMD_TYPE": "描述: 命令类型枚举\n      字段:\n        - 名称: CMD_TYPE_SHELL\n          类型: BYTE\n          描述: Shell命令类型，值为0x01\n        - 名称: CMD_TYPE_SCRIPT\n          类型: BYTE\n          描述: 脚本命令类型，值为0x02\n        - 名称: CMD_TYPE_BUILTIN\n          类型: BYTE\n          描述: 内置命令类型，值为0x03"}}