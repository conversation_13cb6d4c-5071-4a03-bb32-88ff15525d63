{"示例代码": {"反调试保护示例": "int main() {\n    // 初始化反调试保护模块\n    AntiDebugger protector;\n    \n    // 配置反调试保护参数\n    AntiDebugConfig config;\n    config.enablePtraceDetection = true;\n    config.enableBreakpointDetection = true;\n    config.enableTimingChecks = true;\n    config.enableParentProcessCheck = true;\n    config.enableMemoryChecks = true;\n    config.detectionResponseLevel = RESPONSE_LEVEL_MEDIUM;\n    \n    // 初始化保护器\n    if (!protector.Initialize(config)) {\n        fprintf(stderr, \"无法初始化反调试保护模块\\n\");\n        return 1;\n    }\n    \n    printf(\"反调试保护模块已启动\\n\");\n    \n    // 设置调试检测回调\n    protector.SetDebugDetectionCallback([](DebugDetectionInfo* info) {\n        printf(\"检测到调试行为!\\n\");\n        printf(\"  - 检测类型: %s\\n\", info->detectionType);\n        printf(\"  - 可信度: %.2f\\n\", info->confidenceLevel);\n        printf(\"  - 检测时间: %llu ms\\n\", info->detectionTime);\n    });\n    \n    // 启用主动反调试保护\n    printf(\"启用主动反调试保护...\\n\");\n    protector.EnableActiveProtection();\n    \n    // 执行初始调试环境检测\n    printf(\"\\n执行初始调试环境检测...\\n\");\n    DebugEnvironmentResult initialResult = protector.CheckDebugEnvironment();\n    \n    if (initialResult.isDebuggerPresent) {\n        printf(\"警告: 检测到调试器存在!\\n\");\n        printf(\"  - 检测方法: %s\\n\", initialResult.detectionMethod);\n        printf(\"  - 调试器类型: %s\\n\", initialResult.debuggerType);\n    } else {\n        printf(\"未检测到调试器\\n\");\n    }\n    \n    // 添加关键代码区域保护\n    printf(\"\\n添加关键代码区域保护...\\n\");\n    \n    // 创建一个模拟的关键函数\n    int (*criticalFunction)(const char* input, size_t length) = \n        [](const char* input, size_t length) -> int {\n            int sum = 0;\n            for (size_t i = 0; i < length; i++) {\n                sum += input[i];\n            }\n            return sum;\n        };\n    \n    // 注册关键函数保护\n    uint32_t regionId = protector.ProtectCodeRegion(\n        (void*)criticalFunction, 256, PROTECTION_LEVEL_HIGH);\n    \n    if (regionId == 0) {\n        fprintf(stderr, \"无法保护关键代码区域\\n\");\n    } else {\n        printf(\"已添加关键代码区域保护，区域ID: %u\\n\", regionId);\n    }\n    \n    // 设置不同类型的陷阱\n    printf(\"\\n设置反调试陷阱...\\n\");\n    \n    uint32_t trapId1 = protector.SetDebugTrap(DEBUG_TRAP_TIMING, nullptr);\n    uint32_t trapId2 = protector.SetDebugTrap(DEBUG_TRAP_EXCEPTION, nullptr);\n    uint32_t trapId3 = protector.SetDebugTrap(DEBUG_TRAP_CHECKSUM, (void*)criticalFunction);\n    \n    printf(\"已设置反调试陷阱，ID: %u, %u, %u\\n\", trapId1, trapId2, trapId3);\n    \n    // 模拟运行受保护的代码\n    printf(\"\\n模拟运行受保护的代码...\\n\");\n    \n    const char testData[] = \"This is test data for our protected function\";\n    int result = criticalFunction(testData, strlen(testData));\n    \n    printf(\"计算结果: %d\\n\", result);\n    \n    // 执行持续性调试检测\n    printf(\"\\n执行持续性调试检测...\\n\");\n    \n    for (int i = 0; i < 3; i++) {\n        // 模拟一些工作负载\n        printf(\"执行工作负载 %d/3...\\n\", i+1);\n        \n        // 触发调试检测\n        DebugCheckResult checkResult = protector.PerformDebugCheck();\n        \n        if (checkResult.debugDetected) {\n            printf(\"警告: 检测到调试尝试!\\n\");\n            printf(\"  - 检测点: %s\\n\", checkResult.detectionPoint);\n            printf(\"  - 可疑程度: %.2f/10\\n\", checkResult.suspicionLevel);\n        } else {\n            printf(\"未检测到调试尝试\\n\");\n        }\n    }\n    \n    // 获取并显示反调试统计信息\n    printf(\"\\n获取反调试统计信息...\\n\");\n    AntiDebugStats stats = protector.GetStatistics();\n    \n    printf(\"反调试统计:\\n\");\n    printf(\"  - 执行的检测次数: %u\\n\", stats.checksPerformed);\n    printf(\"  - 检测到的调试尝试: %u\\n\", stats.debugAttemptsDetected);\n    printf(\"  - 陷阱触发次数: %u\\n\", stats.trapsTriggerCount);\n    printf(\"  - 平均检测延迟: %.2f ms\\n\", stats.avgDetectionLatency);\n    \n    // 移除陷阱\n    printf(\"\\n移除调试陷阱...\\n\");\n    protector.RemoveDebugTrap(trapId1);\n    protector.RemoveDebugTrap(trapId2);\n    protector.RemoveDebugTrap(trapId3);\n    \n    // 解除代码区域保护\n    printf(\"解除代码区域保护...\\n\");\n    protector.UnprotectCodeRegion(regionId);\n    \n    // 关闭主动保护\n    printf(\"关闭主动反调试保护...\\n\");\n    protector.DisableActiveProtection();\n    \n    // 清理资源\n    protector.Cleanup();\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化反调试保护模块\n      参数:\n        - 名称: config\n          类型: AntiDebugConfig\n          描述: 反调试配置参数\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: protector.Initialize(config)", "SetDebugDetectionCallback": "功能: 设置调试检测回调\n      参数:\n        - 名称: callback\n          类型: std::function<void(DebugDetectionInfo*)>\n          描述: 检测到调试行为时调用的回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.SetDebugDetectionCallback(callbackFunction)", "EnableActiveProtection": "功能: 启用主动反调试保护\n      参数:\n      返回值:\n        类型: bool\n        描述: 启用是否成功\n      示例: protector.EnableActiveProtection()", "CheckDebugEnvironment": "功能: 检测调试环境\n      参数:\n      返回值:\n        类型: DebugEnvironmentResult\n        描述: 调试环境检测结果\n      示例: result = protector.CheckDebugEnvironment()", "ProtectCodeRegion": "功能: 保护代码区域\n      参数:\n        - 名称: address\n          类型: void*\n          描述: 代码区域地址\n        - 名称: size\n          类型: size_t\n          描述: 代码区域大小(字节)\n        - 名称: protectionLevel\n          类型: uint8_t\n          描述: 保护级别\n      返回值:\n        类型: uint32_t\n        描述: 保护区域ID，0表示失败\n      示例: regionId = protector.ProtectCodeRegion(address, size, level)", "SetDebugTrap": "功能: 设置调试陷阱\n      参数:\n        - 名称: trapType\n          类型: uint8_t\n          描述: 陷阱类型\n        - 名称: context\n          类型: void*\n          描述: 陷阱上下文数据\n      返回值:\n        类型: uint32_t\n        描述: 陷阱ID，0表示失败\n      示例: trapId = protector.SetDebugTrap(trapType, context)", "PerformDebugCheck": "功能: 执行调试检测\n      参数:\n      返回值:\n        类型: DebugCheckResult\n        描述: 调试检测结果\n      示例: result = protector.PerformDebugCheck()", "GetStatistics": "功能: 获取反调试统计信息\n      参数:\n      返回值:\n        类型: AntiDebugStats\n        描述: 反调试统计信息\n      示例: stats = protector.GetStatistics()", "RemoveDebugTrap": "功能: 移除调试陷阱\n      参数:\n        - 名称: trapId\n          类型: uint32_t\n          描述: 陷阱ID\n      返回值:\n        类型: bool\n        描述: 移除是否成功\n      示例: protector.RemoveDebugTrap(trapId)", "UnprotectCodeRegion": "功能: 解除代码区域保护\n      参数:\n        - 名称: regionId\n          类型: uint32_t\n          描述: 保护区域ID\n      返回值:\n        类型: bool\n        描述: 解除是否成功\n      示例: protector.UnprotectCodeRegion(regionId)", "DisableActiveProtection": "功能: 关闭主动反调试保护\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: protector.DisableActiveProtection()"}, "数据结构": {"AntiDebugConfig": "描述: 反调试保护配置参数\n      字段:\n        - 名称: enablePtraceDetection\n          类型: bool\n          描述: 是否启用ptrace检测\n        - 名称: enableBreakpointDetection\n          类型: bool\n          描述: 是否启用断点检测\n        - 名称: enableTimingChecks\n          类型: bool\n          描述: 是否启用时间检测\n        - 名称: enableParentProcessCheck\n          类型: bool\n          描述: 是否启用父进程检测\n        - 名称: enableMemoryChecks\n          类型: bool\n          描述: 是否启用内存检测\n        - 名称: detectionResponseLevel\n          类型: uint8_t\n          描述: 检测响应级别", "DebugDetectionInfo": "描述: 调试检测信息\n      字段:\n        - 名称: detectionType\n          类型: char[32]\n          描述: 检测类型\n        - 名称: confidenceLevel\n          类型: float\n          描述: 可信度(0-1)\n        - 名称: detectionTime\n          类型: uint64_t\n          描述: 检测时间戳(毫秒)\n        - 名称: additionalInfo\n          类型: char[128]\n          描述: 额外信息", "DebugEnvironmentResult": "描述: 调试环境检测结果\n      字段:\n        - 名称: isDebuggerPresent\n          类型: bool\n          描述: 调试器是否存在\n        - 名称: detectionMethod\n          类型: char[32]\n          描述: 检测方法\n        - 名称: debuggerType\n          类型: char[32]\n          描述: 调试器类型\n        - 名称: confidenceScore\n          类型: float\n          描述: 可信度评分(0-10)", "DebugCheckResult": "描述: 调试检测结果\n      字段:\n        - 名称: debugDetected\n          类型: bool\n          描述: 是否检测到调试\n        - 名称: detectionPoint\n          类型: char[32]\n          描述: 检测点\n        - 名称: suspicionLevel\n          类型: float\n          描述: 可疑程度(0-10)\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 时间戳(毫秒)", "AntiDebugStats": "描述: 反调试统计信息\n      字段:\n        - 名称: checksPerformed\n          类型: uint32_t\n          描述: 执行的检测次数\n        - 名称: debugAttemptsDetected\n          类型: uint32_t\n          描述: 检测到的调试尝试\n        - 名称: trapsTriggerCount\n          类型: uint32_t\n          描述: 陷阱触发次数\n        - 名称: avgDetectionLatency\n          类型: float\n          描述: 平均检测延迟(毫秒)"}}