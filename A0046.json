{"示例代码": {"下发受控端更新数据示例": "int main() {\n    // 初始化更新数据发送器\n    UpdateDataSender sender;\n    \n    // 打开更新文件\n    FILE* updateFile = fopen(\"client_update_v2.1.bin\", \"rb\");\n    if (!updateFile) {\n        printf(\"无法打开更新文件\\n\");\n        return 1;\n    }\n    \n    // 获取文件大小\n    fseek(updateFile, 0, SEEK_END);\n    long fileSize = ftell(updateFile);\n    fseek(updateFile, 0, SEEK_SET);\n    \n    // 创建传输会话\n    UPDATE_SESSION session;\n    if (!sender.CreateUpdateSession(targetClient, fileSize, &session)) {\n        printf(\"创建更新会话失败\\n\");\n        fclose(updateFile);\n        return 1;\n    }\n    \n    // 分块读取并发送更新数据\n    unsigned char buffer[8192];\n    size_t bytesRead;\n    uint64_t totalSent = 0;\n    \n    while ((bytesRead = fread(buffer, 1, sizeof(buffer), updateFile)) > 0) {\n        // 发送数据块\n        if (!sender.SendUpdateData(&session, buffer, bytesRead)) {\n            printf(\"更新数据发送失败\\n\");\n            break;\n        }\n        \n        totalSent += bytesRead;\n        printf(\"已发送: %lu / %ld 字节 (%.2f%%)\\n\", \n               totalSent, fileSize, (float)totalSent * 100 / fileSize);\n    }\n    \n    // 完成更新会话\n    if (totalSent == fileSize) {\n        if (sender.FinalizeUpdateSession(&session)) {\n            printf(\"更新数据传输完成\\n\");\n        } else {\n            printf(\"更新会话结束失败\\n\");\n        }\n    }\n    \n    fclose(updateFile);\n    return 0;\n}"}, "API接口": {"CreateUpdateSession": "功能: 创建更新数据传输会话\n      参数:\n        - 名称: clientHandle\n          类型: CLIENT_HANDLE\n          描述: 目标客户端句柄\n        - 名称: totalSize\n          类型: uint64_t\n          描述: 更新数据总大小(字节)\n        - 名称: session\n          类型: UPDATE_SESSION*\n          描述: 更新会话结构体指针\n      返回值:\n        类型: bool\n        描述: 会话是否创建成功\n      示例: sender.CreateUpdateSession(targetClient, fileSize, &session)", "SendUpdateData": "功能: 发送更新数据块\n      参数:\n        - 名称: session\n          类型: UPDATE_SESSION*\n          描述: 更新会话结构体指针\n        - 名称: data\n          类型: unsigned char*\n          描述: 数据块缓冲区\n        - 名称: dataSize\n          类型: size_t\n          描述: 数据块大小\n      返回值:\n        类型: bool\n        描述: 数据是否成功发送\n      示例: sender.SendUpdateData(&session, buffer, bytesRead)", "FinalizeUpdateSession": "功能: 结束更新数据传输会话\n      参数:\n        - 名称: session\n          类型: UPDATE_SESSION*\n          描述: 更新会话结构体指针\n      返回值:\n        类型: bool\n        描述: 会话是否成功结束\n      示例: sender.FinalizeUpdateSession(&session)"}, "数据结构": {"UPDATE_SESSION": "描述: 更新数据传输会话结构体\n      字段:\n        - 名称: sessionId\n          类型: uint32_t\n          描述: 会话唯一标识符\n        - 名称: clientHandle\n          类型: CLIENT_HANDLE\n          描述: 目标客户端句柄\n        - 名称: totalSize\n          类型: uint64_t\n          描述: 更新数据总大小\n        - 名称: sentSize\n          类型: uint64_t\n          描述: 已发送数据大小\n        - 名称: nextBlockId\n          类型: uint32_t\n          描述: 下一个数据块ID\n        - 名称: startTimestamp\n          类型: uint64_t\n          描述: 会话开始时间戳\n        - 名称: status\n          类型: BYTE\n          描述: 会话状态", "UPDATE_DATA_BLOCK": "描述: 更新数据块结构体\n      字段:\n        - 名称: blockId\n          类型: uint32_t\n          描述: 数据块ID\n        - 名称: sessionId\n          类型: uint32_t\n          描述: 所属会话ID\n        - 名称: dataSize\n          类型: uint16_t\n          描述: 数据块大小\n        - 名称: data\n          类型: unsigned char[]\n          描述: 数据块内容\n        - 名称: checksum\n          类型: uint32_t\n          描述: 数据校验和"}}