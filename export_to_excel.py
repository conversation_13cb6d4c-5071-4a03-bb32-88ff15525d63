#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import re
import pandas as pd
from collections import defaultdict

def extract_file_number(filename):
    """从文件名中提取编号，例如从'A0001.json'中提取'0001'"""
    match = re.search(r'A(\d+)\.json', filename)
    if match:
        return int(match.group(1))
    return 0

def read_json_file(file_path):
    """读取JSON文件并返回其内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"读取文件 {file_path} 时发生错误: {str(e)}")
        return None

def create_excel_from_jsons(dir_path, output_file="JSON数据汇总.xlsx"):
    """将目录下的所有JSON文件按编号顺序导出到Excel"""
    # 获取所有JSON文件并排序
    json_files = [f for f in os.listdir(dir_path) if f.endswith('.json') and f.startswith('A')]
    json_files.sort(key=extract_file_number)
    
    # 用于存储不同表的数据
    sheet_data = defaultdict(list)
    
    # 跟踪每个表中的字段
    sheet_columns = defaultdict(set)
    
    print(f"处理 {len(json_files)} 个JSON文件...")
    
    # 处理每个JSON文件
    for json_file in json_files:
        file_number = extract_file_number(json_file)
        file_path = os.path.join(dir_path, json_file)
        data = read_json_file(file_path)
        
        if not data:
            continue
        
        # 处理API接口
        if "API接口" in data:
            for api_name, api_content in data["API接口"].items():
                row = {
                    "文件编号": f"A{file_number:04d}",
                    "接口名称": api_name,
                    "接口内容": api_content
                }
                sheet_data["API接口"].append(row)
                sheet_columns["API接口"].update(row.keys())
        
        # 处理数据结构
        if "数据结构" in data:
            for struct_name, struct_content in data["数据结构"].items():
                row = {
                    "文件编号": f"A{file_number:04d}",
                    "结构名称": struct_name,
                    "结构内容": struct_content
                }
                sheet_data["数据结构"].append(row)
                sheet_columns["数据结构"].update(row.keys())
        
        # 处理示例代码
        if "示例代码" in data:
            for example_name, example_content in data["示例代码"].items():
                row = {
                    "文件编号": f"A{file_number:04d}",
                    "示例名称": example_name,
                    "代码内容": example_content
                }
                sheet_data["示例代码"].append(row)
                sheet_columns["示例代码"].update(row.keys())
    
    # 创建Excel文件
    with pd.ExcelWriter(os.path.join(dir_path, output_file)) as writer:
        for sheet_name, data_list in sheet_data.items():
            if data_list:  # 只创建有数据的表
                df = pd.DataFrame(data_list)
                # 确保列顺序统一
                cols = list(sheet_columns[sheet_name])
                # 将"文件编号"放在第一列
                if "文件编号" in cols:
                    cols.remove("文件编号")
                    cols = ["文件编号"] + cols
                df = df[cols]
                df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"Excel文件已创建: {output_file}")

if __name__ == "__main__":
    # 获取脚本所在目录
    dir_path = os.path.dirname(os.path.realpath(__file__))
    create_excel_from_jsons(dir_path)
    print("处理完成!")
