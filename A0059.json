{"示例代码": {"网络数据包过滤示例": "int main() {\n    // 初始化数据包过滤管理器\n    PacketFilterManager filterManager;\n    \n    // 创建过滤器配置\n    PACKET_FILTER_CONFIG config;\n    memset(&config, 0, sizeof(PACKET_FILTER_CONFIG));\n    \n    // 设置过滤器基本配置\n    config.enableFiltering = true;\n    config.logFilteredPackets = true;\n    config.filterLogPath = \"/var/log/portforward/filter.log\";\n    \n    // 初始化过滤管理器\n    if (!filterManager.Initialize(&config)) {\n        printf(\"初始化数据包过滤管理器失败: %s\\n\", filterManager.GetLastError());\n        return 1;\n    }\n    \n    printf(\"数据包过滤管理器已初始化\\n\");\n    \n    // 创建一个过滤规则来阻止特定IP\n    IP_FILTER_RULE ipRule;\n    memset(&ipRule, 0, sizeof(IP_FILTER_RULE));\n    \n    ipRule.ruleType = FILTER_TYPE_IP;\n    ipRule.action = FILTER_ACTION_BLOCK;\n    strcpy(ipRule.ipAddress, \"*************\");\n    ipRule.ipMask = 32;  // 精确匹配此IP\n    ipRule.direction = FILTER_DIRECTION_BOTH;\n    \n    FILTER_RULE_ID ipRuleId;\n    if (filterManager.AddIPRule(&ipRule, &ipRuleId)) {\n        printf(\"已添加IP过滤规则，规则ID: %u\\n\", ipRuleId);\n    }\n    \n    // 创建一个端口过滤规则来阻止特定端口\n    PORT_FILTER_RULE portRule;\n    memset(&portRule, 0, sizeof(PORT_FILTER_RULE));\n    \n    portRule.ruleType = FILTER_TYPE_PORT;\n    portRule.action = FILTER_ACTION_BLOCK;\n    portRule.protocol = PROTOCOL_TCP;\n    portRule.portRangeStart = 22;\n    portRule.portRangeEnd = 23;\n    portRule.direction = FILTER_DIRECTION_OUTBOUND;\n    \n    FILTER_RULE_ID portRuleId;\n    if (filterManager.AddPortRule(&portRule, &portRuleId)) {\n        printf(\"已添加端口过滤规则，规则ID: %u\\n\", portRuleId);\n    }\n    \n    // 创建一个内容过滤规则阻止包含特定字符串的数据包\n    CONTENT_FILTER_RULE contentRule;\n    memset(&contentRule, 0, sizeof(CONTENT_FILTER_RULE));\n    \n    contentRule.ruleType = FILTER_TYPE_CONTENT;\n    contentRule.action = FILTER_ACTION_BLOCK;\n    strcpy(contentRule.pattern, \"malware\");\n    contentRule.patternLength = strlen(\"malware\");\n    contentRule.isCaseSensitive = false;\n    contentRule.direction = FILTER_DIRECTION_BOTH;\n    \n    FILTER_RULE_ID contentRuleId;\n    if (filterManager.AddContentRule(&contentRule, &contentRuleId)) {\n        printf(\"已添加内容过滤规则，规则ID: %u\\n\", contentRuleId);\n    }\n    \n    // 获取当前活动的转发连接列表\n    PortForwardConnector connector;\n    FORWARD_CONNECTION_ID connections[10];\n    uint8_t connectionCount = 0;\n    \n    if (connector.ListActiveConnections(connections, 10, &connectionCount)) {\n        // 为活动连接应用过滤规则\n        if (connectionCount > 0) {\n            FORWARD_CONNECTION_ID connectionId = connections[0];\n            \n            // 应用过滤规则到连接\n            if (filterManager.ApplyFilterToConnection(connectionId, ipRuleId)) {\n                printf(\"已将IP过滤规则应用到连接ID: %u\\n\", connectionId);\n            }\n            \n            if (filterManager.ApplyFilterToConnection(connectionId, portRuleId)) {\n                printf(\"已将端口过滤规则应用到连接ID: %u\\n\", connectionId);\n            }\n            \n            if (filterManager.ApplyFilterToConnection(connectionId, contentRuleId)) {\n                printf(\"已将内容过滤规则应用到连接ID: %u\\n\", connectionId);\n            }\n            \n            // 等待一段时间后检查过滤结果\n            printf(\"等待过滤结果...\\n\");\n            sleep(30);\n            \n            // 查询过滤统计信息\n            FILTER_STATISTICS stats;\n            if (filterManager.GetFilterStatistics(&stats)) {\n                printf(\"\\n过滤统计信息:\\n\");\n                printf(\"总处理数据包: %lu\\n\", stats.totalPacketsProcessed);\n                printf(\"总过滤数据包: %lu\\n\", stats.totalPacketsFiltered);\n                printf(\"IP规则匹配: %lu\\n\", stats.ipRuleMatches);\n                printf(\"端口规则匹配: %lu\\n\", stats.portRuleMatches);\n                printf(\"内容规则匹配: %lu\\n\", stats.contentRuleMatches);\n            }\n            \n            // 导出过滤日志\n            if (filterManager.ExportFilterLog(\"/tmp/filter_report.txt\")) {\n                printf(\"已导出过滤日志到 /tmp/filter_report.txt\\n\");\n            }\n        }\n    }\n    \n    // 删除过滤规则\n    filterManager.RemoveRule(ipRuleId);\n    filterManager.RemoveRule(portRuleId);\n    filterManager.RemoveRule(contentRuleId);\n    printf(\"已移除所有过滤规则\\n\");\n    \n    // 关闭过滤管理器\n    filterManager.Shutdown();\n    printf(\"数据包过滤管理器已关闭\\n\");\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化数据包过滤管理器\n      参数:\n        - 名称: config\n          类型: PACKET_FILTER_CONFIG*\n          描述: 过滤器配置结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功初始化\n      示例: filterManager.Initialize(&config)", "AddIPRule": "功能: 添加IP地址过滤规则\n      参数:\n        - 名称: rule\n          类型: IP_FILTER_RULE*\n          描述: IP过滤规则结构体指针\n        - 名称: ruleId\n          类型: FILTER_RULE_ID*\n          描述: 返回的规则ID指针\n      返回值:\n        类型: bool\n        描述: 是否成功添加规则\n      示例: filterManager.AddIPRule(&ipRule, &ipRuleId)", "AddPortRule": "功能: 添加端口过滤规则\n      参数:\n        - 名称: rule\n          类型: PORT_FILTER_RULE*\n          描述: 端口过滤规则结构体指针\n        - 名称: ruleId\n          类型: FILTER_RULE_ID*\n          描述: 返回的规则ID指针\n      返回值:\n        类型: bool\n        描述: 是否成功添加规则\n      示例: filterManager.AddPortRule(&portRule, &portRuleId)", "AddContentRule": "功能: 添加内容过滤规则\n      参数:\n        - 名称: rule\n          类型: CONTENT_FILTER_RULE*\n          描述: 内容过滤规则结构体指针\n        - 名称: ruleId\n          类型: FILTER_RULE_ID*\n          描述: 返回的规则ID指针\n      返回值:\n        类型: bool\n        描述: 是否成功添加规则\n      示例: filterManager.AddContentRule(&contentRule, &contentRuleId)", "ApplyFilterToConnection": "功能: 将过滤规则应用到转发连接\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: ruleId\n          类型: FILTER_RULE_ID\n          描述: 规则ID\n      返回值:\n        类型: bool\n        描述: 是否成功应用规则\n      示例: filterManager.ApplyFilterToConnection(connectionId, ipRuleId)", "GetFilterStatistics": "功能: 获取过滤统计信息\n      参数:\n        - 名称: stats\n          类型: FILTER_STATISTICS*\n          描述: 统计信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取统计信息\n      示例: filterManager.GetFilterStatistics(&stats)", "RemoveRule": "功能: 移除过滤规则\n      参数:\n        - 名称: ruleId\n          类型: FILTER_RULE_ID\n          描述: 规则ID\n      返回值:\n        类型: bool\n        描述: 是否成功移除规则\n      示例: filterManager.RemoveRule(ipRuleId)", "Shutdown": "功能: 关闭数据包过滤管理器\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否成功关闭\n      示例: filterManager.Shutdown()"}, "数据结构": {"PACKET_FILTER_CONFIG": "描述: 数据包过滤配置结构体\n      字段:\n        - 名称: enableFiltering\n          类型: bool\n          描述: 是否启用过滤\n        - 名称: logFilteredPackets\n          类型: bool\n          描述: 是否记录被过滤的数据包\n        - 名称: filterLogPath\n          类型: char[256]\n          描述: 过滤日志路径\n        - 名称: maxRulesCount\n          类型: uint16_t\n          描述: 最大规则数量\n        - 名称: defaultAction\n          类型: uint8_t\n          描述: 默认处理动作", "IP_FILTER_RULE": "描述: IP过滤规则结构体\n      字段:\n        - 名称: ruleType\n          类型: uint8_t\n          描述: 规则类型\n        - 名称: action\n          类型: uint8_t\n          描述: 处理动作\n        - 名称: ipAddress\n          类型: char[64]\n          描述: IP地址\n        - 名称: ipMask\n          类型: uint8_t\n          描述: 子网掩码位数\n        - 名称: direction\n          类型: uint8_t\n          描述: 过滤方向", "PORT_FILTER_RULE": "描述: 端口过滤规则结构体\n      字段:\n        - 名称: ruleType\n          类型: uint8_t\n          描述: 规则类型\n        - 名称: action\n          类型: uint8_t\n          描述: 处理动作\n        - 名称: protocol\n          类型: uint8_t\n          描述: 协议类型\n        - 名称: portRangeStart\n          类型: uint16_t\n          描述: 起始端口\n        - 名称: portRangeEnd\n          类型: uint16_t\n          描述: 结束端口\n        - 名称: direction\n          类型: uint8_t\n          描述: 过滤方向", "CONTENT_FILTER_RULE": "描述: 内容过滤规则结构体\n      字段:\n        - 名称: ruleType\n          类型: uint8_t\n          描述: 规则类型\n        - 名称: action\n          类型: uint8_t\n          描述: 处理动作\n        - 名称: pattern\n          类型: char[256]\n          描述: 匹配模式\n        - 名称: patternLength\n          类型: uint16_t\n          描述: 模式长度\n        - 名称: isCaseSensitive\n          类型: bool\n          描述: 是否区分大小写\n        - 名称: direction\n          类型: uint8_t\n          描述: 过滤方向", "FILTER_STATISTICS": "描述: 过滤统计信息结构体\n      字段:\n        - 名称: totalPacketsProcessed\n          类型: uint64_t\n          描述: 处理的总数据包数\n        - 名称: totalPacketsFiltered\n          类型: uint64_t\n          描述: 被过滤的总数据包数\n        - 名称: ipRuleMatches\n          类型: uint64_t\n          描述: IP规则匹配次数\n        - 名称: portRuleMatches\n          类型: uint64_t\n          描述: 端口规则匹配次数\n        - 名称: contentRuleMatches\n          类型: uint64_t\n          描述: 内容规则匹配次数\n        - 名称: startTimestamp\n          类型: uint64_t\n          描述: 统计开始时间戳\n        - 名称: endTimestamp\n          类型: uint64_t\n          描述: 统计结束时间戳"}}