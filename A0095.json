{"示例代码": {"说明": "暂无"}, "API接口": {"RemoteLoadAndExecuteELF": "功能: 远程加载指定的ELF可执行文件并运行。\n      参数:\n        - 名称: elf_file_path_on_target\n          类型: const char*\n          描述: 目标机器上ELF文件的路径。如果文件不存在，可能需要先传输。\n        - 名称: argv\n          类型: char* const*\n          描述: 传递给ELF程序的命令行参数列表 (类似main函数的argv)。\n        - 名称: envp\n          类型: char* const*\n          描述: 传递给ELF程序的环境变量列表 (可选)。\n      返回值:\n        类型: int\n        描述: 操作状态，0表示成功加载并开始执行，非0表示失败。ELF程序的退出状态需通过其他方式获取。\n      示例: const char* args[] = {\"program_name\", \"-arg1\", NULL}; if (RemoteLoadAndExecuteELF(\"/tmp/executable.elf\", args, NULL) == 0) { /* ELF程序已启动 */ }"}}