{"示例代码": {"进程注入示例": "int main() {\n    // 创建进程注入管理器\n    ProcessInjector injector;\n    \n    // 配置进程注入选项\n    InjectionOptions options;\n    options.targetProcess = \"target_app\";  // 目标进程名\n    options.injectMethod = INJECT_METHOD_PTRACE;  // 使用ptrace方法\n    options.payloadPath = \"/opt/payload/module.so\";  // 共享库路径\n    options.waitForCompletion = true;  // 等待注入完成\n    \n    // 设置高级注入选项\n    AdvancedInjectionOptions advOptions;\n    advOptions.hideFromProcMaps = true;  // 从/proc/maps隐藏\n    advOptions.obfuscateSymbols = true;  // 混淆符号\n    advOptions.injectEntryPoint = \"init_module\";  // 入口点函数名\n    advOptions.eraseMemoryOnExit = true;  // 退出时清除内存\n    \n    options.advancedOptions = advOptions;\n    \n    // 设置注入后回调\n    options.postInjectionCallback = [](pid_t pid, void* addr) {\n        printf(\"Injection completed in process %d at address %p\\n\", pid, addr);\n    };\n    \n    // 执行进程注入\n    InjectionResult result = injector.inject(options);\n    \n    if (result.success) {\n        printf(\"Process injection successful\\n\");\n        printf(\"Target PID: %d\\n\", result.targetPid);\n        printf(\"Injection address: %p\\n\", result.injectionAddress);\n    } else {\n        printf(\"Injection failed: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 监控注入进程\n    injector.monitorInjectedProcess(result.targetPid, [](ProcessStatus status) {\n        printf(\"Process status: %s\\n\", \n               status.isRunning ? \"Running\" : \"Terminated\");\n        return status.isRunning;  // 继续监控直到终止\n    });\n    \n    // 清理注入器资源\n    injector.cleanup();\n    \n    return 0;\n}"}, "API接口": {"inject": "功能: 向目标进程注入代码或共享库\n      参数:\n        - 名称: options\n          类型: InjectionOptions\n          描述: 注入选项\n      返回值:\n        类型: InjectionResult\n        描述: 注入结果\n      示例: InjectionResult result = injector.inject(options);", "findTargetProcess": "功能: 查找目标进程\n      参数:\n        - 名称: processName\n          类型: const std::string&\n          描述: 进程名或模式\n      返回值:\n        类型: std::vector<ProcessInfo>\n        描述: 匹配的进程列表\n      示例: auto processes = injector.findTargetProcess(\"firefox\");", "monitorInjectedProcess": "功能: 监控注入的进程\n      参数:\n        - 名称: pid\n          类型: pid_t\n          描述: 目标进程ID\n        - 名称: callback\n          类型: std::function<bool(ProcessStatus)>\n          描述: 状态回调函数，返回false停止监控\n      返回值:\n        类型: bool\n        描述: 监控是否启动成功\n      示例: bool monitoring = injector.monitorInjectedProcess(pid, statusCallback);", "detachFromProcess": "功能: 从注入的进程分离\n      参数:\n        - 名称: pid\n          类型: pid_t\n          描述: 进程ID\n      返回值:\n        类型: bool\n        描述: 分离是否成功\n      示例: bool detached = injector.detachFromProcess(result.targetPid);", "cleanup": "功能: 清理注入器资源\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: injector.cleanup();"}, "数据结构": {"InjectionOptions": "描述: 进程注入选项结构体\n      字段:\n        - 名称: targetProcess\n          类型: std::string\n          描述: 目标进程名称或PID\n        - 名称: injectMethod\n          类型: InjectionMethod\n          描述: 注入方法\n        - 名称: payloadPath\n          类型: std::string\n          描述: 要注入的共享库或代码路径\n        - 名称: waitForCompletion\n          类型: bool\n          描述: 是否等待注入完成\n        - 名称: timeoutMs\n          类型: uint32_t\n          描述: 注入超时时间（毫秒）\n        - 名称: advancedOptions\n          类型: AdvancedInjectionOptions\n          描述: 高级注入选项\n        - 名称: postInjectionCallback\n          类型: std::function<void(pid_t, void*)>\n          描述: 注入完成后的回调函数", "InjectionMethod": "描述: 注入方法枚举\n      字段:\n        - 名称: INJECT_METHOD_PTRACE\n          类型: enum\n          描述: 使用ptrace进行注入\n        - 名称: INJECT_METHOD_LD_PRELOAD\n          类型: enum\n          描述: 使用LD_PRELOAD进行注入\n        - 名称: INJECT_METHOD_PROC_MEM\n          类型: enum\n          描述: 使用/proc/pid/mem进行注入\n        - 名称: INJECT_METHOD_SHARED_MEMORY\n          类型: enum\n          描述: 使用共享内存进行注入", "AdvancedInjectionOptions": "描述: 高级注入选项结构体\n      字段:\n        - 名称: hideFromProcMaps\n          类型: bool\n          描述: 是否从/proc/maps隐藏\n        - 名称: obfuscateSymbols\n          类型: bool\n          描述: 是否混淆符号\n        - 名称: injectEntryPoint\n          类型: std::string\n          描述: 注入的入口点函数名\n        - 名称: eraseMemoryOnExit\n          类型: bool\n          描述: 退出时是否清除内存\n        - 名称: customMemoryProtection\n          类型: int\n          描述: 自定义内存保护标志（如PROT_READ|PROT_EXEC）", "InjectionResult": "描述: 注入结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 注入是否成功\n        - 名称: targetPid\n          类型: pid_t\n          描述: 目标进程ID\n        - 名称: injectionAddress\n          类型: void*\n          描述: 注入地址\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: payloadSize\n          类型: size_t\n          描述: 注入的代码或库大小", "ProcessInfo": "描述: 进程信息结构体\n      字段:\n        - 名称: pid\n          类型: pid_t\n          描述: 进程ID\n        - 名称: name\n          类型: std::string\n          描述: 进程名称\n        - 名称: user\n          类型: std::string\n          描述: 所有者用户名\n        - 名称: memoryMaps\n          类型: std::vector<MemoryRegion>\n          描述: 进程内存映射\n        - 名称: isInjectable\n          类型: bool\n          描述: 是否可注入", "ProcessStatus": "描述: 进程状态结构体\n      字段:\n        - 名称: isRunning\n          类型: bool\n          描述: 进程是否正在运行\n        - 名称: uptime\n          类型: uint64_t\n          描述: 进程运行时间（秒）\n        - 名称: memoryUsage\n          类型: size_t\n          描述: 内存使用量（字节）\n        - 名称: cpuUsage\n          类型: float\n          描述: CPU使用率（百分比）\n        - 名称: threadCount\n          类型: int\n          描述: 线程数量"}}