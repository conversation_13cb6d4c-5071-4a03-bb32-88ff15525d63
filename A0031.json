{"示例代码": {"端口转发示例": "int main() {\n    // 创建端口转发管理器\n    PortForwarder forwarder;\n    \n    // 配置转发规则\n    ForwardingRule rule;\n    rule.localHost = \"0.0.0.0\";\n    rule.localPort = 8080;\n    rule.remoteHost = \"*************\";\n    rule.remotePort = 80;\n    rule.protocol = PROTOCOL_TCP;\n    rule.ruleId = \"web-access\";\n    \n    // 设置额外选项\n    rule.options.enableLogging = true;\n    rule.options.bufferSize = 8192;\n    rule.options.connectionTimeout = 30;\n    rule.options.enableSSL = false;\n    \n    // 添加转发规则\n    if (!forwarder.addForwardingRule(rule)) {\n        printf(\"Failed to add forwarding rule: %s\\n\", forwarder.getLastError().c_str());\n        return 1;\n    }\n    \n    // 配置另一个转发规则（SSH流量）\n    ForwardingRule sshRule;\n    sshRule.localHost = \"127.0.0.1\";\n    sshRule.localPort = 2222;\n    sshRule.remoteHost = \"********\";\n    sshRule.remotePort = 22;\n    sshRule.protocol = PROTOCOL_TCP;\n    sshRule.ruleId = \"ssh-tunnel\";\n    \n    // 设置加密选项\n    sshRule.options.enableLogging = true;\n    sshRule.options.enableEncryption = true;\n    sshRule.options.encryptionKey = \"secret-key-example\";\n    sshRule.options.enableCompression = true;\n    \n    // 添加SSH转发规则\n    forwarder.addForwardingRule(sshRule);\n    \n    // 设置全局选项\n    GlobalForwardingOptions globalOpts;\n    globalOpts.maxConnections = 100;\n    globalOpts.connectionBacklog = 10;\n    globalOpts.enableTrafficShaping = true;\n    globalOpts.maxBandwidthKBps = 1024; // 限制总带宽为1MB/s\n    \n    forwarder.setGlobalOptions(globalOpts);\n    \n    // 启动端口转发服务\n    if (!forwarder.start()) {\n        printf(\"Failed to start port forwarding service: %s\\n\", \n               forwarder.getLastError().c_str());\n        return 1;\n    }\n    \n    printf(\"Port forwarding service started\\n\");\n    printf(\"Rule 1: %s:%d -> %s:%d\\n\", \n           rule.localHost.c_str(), rule.localPort, \n           rule.remoteHost.c_str(), rule.remotePort);\n    printf(\"Rule 2: %s:%d -> %s:%d\\n\", \n           sshRule.localHost.c_str(), sshRule.localPort, \n           sshRule.remoteHost.c_str(), sshRule.remotePort);\n    \n    // 注册状态回调函数\n    forwarder.registerStatusCallback([](const ForwardingStatus& status) {\n        printf(\"Status update - Rule ID: %s\\n\", status.ruleId.c_str());\n        printf(\"  Active connections: %d\\n\", status.activeConnections);\n        printf(\"  Bytes forwarded: %zu\\n\", status.bytesForwarded);\n        printf(\"  Last error: %s\\n\", status.lastError.empty() ? \"none\" : status.lastError.c_str());\n    });\n    \n    // 运行转发服务（实际应用中会使用事件循环或线程池）\n    printf(\"Press Ctrl+C to stop...\\n\");\n    \n    // 模拟运行一段时间\n    sleep(60);\n    \n    // 获取转发统计信息\n    ForwardingStatistics stats = forwarder.getStatistics();\n    printf(\"\\nForwarding statistics:\\n\");\n    printf(\"Total connections: %d\\n\", stats.totalConnections);\n    printf(\"Active connections: %d\\n\", stats.activeConnections);\n    printf(\"Total bytes forwarded: %zu\\n\", stats.totalBytesForwarded);\n    printf(\"Average latency: %.2f ms\\n\", stats.averageLatencyMs);\n    \n    // 停止特定规则的转发\n    forwarder.stopForwardingRule(\"ssh-tunnel\");\n    \n    // 停止所有转发\n    forwarder.stop();\n    printf(\"Port forwarding service stopped\\n\");\n    \n    return 0;\n}"}, "API接口": {"addForwardingRule": "功能: 添加端口转发规则\n      参数:\n        - 名称: rule\n          类型: ForwardingRule\n          描述: 转发规则配置\n      返回值:\n        类型: bool\n        描述: 添加规则是否成功\n      示例: bool success = forwarder.addForwardingRule(rule);", "start": "功能: 启动端口转发服务\n      参数:\n      返回值:\n        类型: bool\n        描述: 启动是否成功\n      示例: bool started = forwarder.start();", "stop": "功能: 停止端口转发服务\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: forwarder.stop();", "stopForwardingRule": "功能: 停止特定端口转发规则\n      参数:\n        - 名称: ruleId\n          类型: const std::string&\n          描述: 转发规则ID\n      返回值:\n        类型: bool\n        描述: 停止规则是否成功\n      示例: bool stopped = forwarder.stopForwardingRule(\"web-access\");", "setGlobalOptions": "功能: 设置全局转发选项\n      参数:\n        - 名称: options\n          类型: GlobalForwardingOptions\n          描述: 全局选项\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: forwarder.setGlobalOptions(globalOptions);", "getStatistics": "功能: 获取转发统计信息\n      参数:\n      返回值:\n        类型: ForwardingStatistics\n        描述: 转发统计数据\n      示例: ForwardingStatistics stats = forwarder.getStatistics();", "registerStatusCallback": "功能: 注册状态回调函数\n      参数:\n        - 名称: callback\n          类型: std::function<void(const ForwardingStatus&)>\n          描述: 状态回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: forwarder.registerStatusCallback([](const ForwardingStatus& status) { /* process status */ });"}, "数据结构": {"ForwardingRule": "描述: 端口转发规则结构体\n      字段:\n        - 名称: localHost\n          类型: std::string\n          描述: 本地监听主机地址\n        - 名称: localPort\n          类型: uint16_t\n          描述: 本地监听端口\n        - 名称: remoteHost\n          类型: std::string\n          描述: 远程目标主机地址\n        - 名称: remotePort\n          类型: uint16_t\n          描述: 远程目标端口\n        - 名称: protocol\n          类型: ProtocolType\n          描述: 使用的协议\n        - 名称: ruleId\n          类型: std::string\n          描述: 规则标识符\n        - 名称: options\n          类型: ForwardingOptions\n          描述: 转发选项", "ProtocolType": "描述: 协议类型枚举\n      字段:\n        - 名称: PROTOCOL_TCP\n          类型: enum\n          描述: TCP协议\n        - 名称: PROTOCOL_UDP\n          类型: enum\n          描述: UDP协议", "ForwardingOptions": "描述: 转发选项结构体\n      字段:\n        - 名称: enableLogging\n          类型: bool\n          描述: 是否启用日志\n        - 名称: bufferSize\n          类型: uint32_t\n          描述: 缓冲区大小\n        - 名称: connectionTimeout\n          类型: uint32_t\n          描述: 连接超时时间（秒）\n        - 名称: enableSSL\n          类型: bool\n          描述: 是否启用SSL\n        - 名称: enableEncryption\n          类型: bool\n          描述: 是否启用加密\n        - 名称: encryptionKey\n          类型: std::string\n          描述: 加密密钥\n        - 名称: enableCompression\n          类型: bool\n          描述: 是否启用压缩", "GlobalForwardingOptions": "描述: 全局转发选项结构体\n      字段:\n        - 名称: maxConnections\n          类型: uint32_t\n          描述: 最大连接数\n        - 名称: connectionBacklog\n          类型: uint32_t\n          描述: 连接等待队列长度\n        - 名称: enableTrafficShaping\n          类型: bool\n          描述: 是否启用流量控制\n        - 名称: maxBandwidthKBps\n          类型: uint32_t\n          描述: 最大带宽（KB/s）", "ForwardingStatus": "描述: 转发状态结构体\n      字段:\n        - 名称: ruleId\n          类型: std::string\n          描述: 规则标识符\n        - 名称: active\n          类型: bool\n          描述: 规则是否活动\n        - 名称: activeConnections\n          类型: uint32_t\n          描述: 活动连接数\n        - 名称: bytesForwarded\n          类型: size_t\n          描述: 已转发字节数\n        - 名称: lastError\n          类型: std::string\n          描述: 最后一次错误", "ForwardingStatistics": "描述: 转发统计信息结构体\n      字段:\n        - 名称: totalConnections\n          类型: uint32_t\n          描述: 总连接数\n        - 名称: activeConnections\n          类型: uint32_t\n          描述: 当前活动连接数\n        - 名称: totalBytesForwarded\n          类型: size_t\n          描述: 总转发字节数\n        - 名称: averageLatencyMs\n          类型: float\n          描述: 平均延迟（毫秒）\n        - 名称: rulesActive\n          类型: uint32_t\n          描述: 活动规则数"}}