{"示例代码": {"恢复端口转发示例": "int main() {\n    // 初始化端口转发连接管理器\n    PortForwardConnector connector;\n    \n    // 获取所有端口转发连接列表(包括暂停和失败的连接)\n    FORWARD_CONNECTION_ID connections[20];\n    uint8_t connectionCount = 0;\n    \n    if (connector.ListAllConnections(connections, 20, &connectionCount)) {\n        printf(\"当前所有端口转发连接数量: %d\\n\", connectionCount);\n        \n        // 遍历显示所有连接及其状态\n        bool foundPausedConnection = false;\n        FORWARD_CONNECTION_ID pausedConnectionId = 0;\n        \n        for (int i = 0; i < connectionCount; i++) {\n            FORWARD_CONNECTION_INFO info;\n            if (connector.GetConnectionInfo(connections[i], &info)) {\n                const char* statusStr;\n                switch (info.status) {\n                    case CONNECTION_STATUS_ACTIVE: statusStr = \"活动\"; break;\n                    case CONNECTION_STATUS_PAUSED: statusStr = \"暂停\"; break;\n                    case CONNECTION_STATUS_ERROR: statusStr = \"错误\"; break;\n                    case CONNECTION_STATUS_CLOSING: statusStr = \"关闭中\"; break;\n                    default: statusStr = \"未知\"; break;\n                }\n                \n                printf(\"%d. 连接ID %u: %s:%d -> %s:%d (状态: %s)\\n\", i + 1,\n                       info.connectionId, info.localHost, info.localPort, \n                       info.remoteHost, info.remotePort, statusStr);\n                       \n                // 记录第一个处于暂停状态的连接\n                if (info.status == CONNECTION_STATUS_PAUSED && !foundPausedConnection) {\n                    foundPausedConnection = true;\n                    pausedConnectionId = info.connectionId;\n                }\n            }\n        }\n        \n        // 如果找到了暂停的连接，尝试恢复它\n        if (foundPausedConnection) {\n            printf(\"\\n找到暂停的连接ID: %u，准备恢复...\\n\", pausedConnectionId);\n            \n            // 配置恢复选项\n            RESUME_OPTIONS options;\n            memset(&options, 0, sizeof(RESUME_OPTIONS));\n            options.resetBuffers = true;            // 重置传输缓冲区\n            options.restoreFullBandwidth = true;    // 恢复完整带宽\n            options.resyncConnection = true;        // 重新同步连接状态\n            \n            // 执行恢复操作\n            RESUME_RESULT result;\n            if (connector.ResumeForwardWithOptions(pausedConnectionId, &options, &result)) {\n                printf(\"端口转发连接已成功恢复\\n\");\n                printf(\"恢复时间: %s\", ctime((time_t*)&result.resumeTime));\n                printf(\"暂停持续时间: %u秒\\n\", result.pauseDuration);\n                \n                // 设置连接自动恢复策略\n                AUTO_RECOVERY_POLICY policy;\n                memset(&policy, 0, sizeof(AUTO_RECOVERY_POLICY));\n                policy.enabled = true;\n                policy.maxRetries = 5;\n                policy.retryIntervalMs = 1000;\n                policy.exponentialBackoff = true;\n                \n                connector.SetAutoRecoveryPolicy(pausedConnectionId, &policy);\n                printf(\"已为连接设置自动恢复策略\\n\");\n            } else {\n                printf(\"恢复端口转发连接失败: %s\\n\", connector.GetLastError());\n                printf(\"错误代码: %u\\n\", result.errorCode);\n                return 1;\n            }\n        } else {\n            printf(\"没有找到处于暂停状态的连接\\n\");\n            \n            // 如果没有暂停的连接，尝试创建一个新连接并设置自动恢复\n            FORWARD_RULE_ID ruleId = 123; // 示例规则ID\n            CONNECT_OPTIONS connectOptions;\n            memset(&connectOptions, 0, sizeof(CONNECT_OPTIONS));\n            connectOptions.timeout = 10;\n            \n            FORWARD_CONNECTION_ID newConnectionId;\n            if (connector.ConnectForward(ruleId, &connectOptions, &newConnectionId)) {\n                printf(\"已创建新连接: %u，设置自动恢复策略\\n\", newConnectionId);\n                \n                AUTO_RECOVERY_POLICY policy;\n                memset(&policy, 0, sizeof(AUTO_RECOVERY_POLICY));\n                policy.enabled = true;\n                policy.maxRetries = 5;\n                policy.retryIntervalMs = 1000;\n                policy.exponentialBackoff = true;\n                \n                connector.SetAutoRecoveryPolicy(newConnectionId, &policy);\n            }\n        }\n    } else {\n        printf(\"获取连接列表失败: %s\\n\", connector.GetLastError());\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"ListAllConnections": "功能: 获取所有端口转发连接列表(包括暂停和失败的连接)\n      参数:\n        - 名称: connections\n          类型: FORWARD_CONNECTION_ID*\n          描述: 连接ID数组\n        - 名称: maxCount\n          类型: uint8_t\n          描述: 数组最大容量\n        - 名称: count\n          类型: uint8_t*\n          描述: 返回实际连接数量\n      返回值:\n        类型: bool\n        描述: 是否成功获取连接列表\n      示例: connector.ListAllConnections(connections, 20, &connectionCount)", "GetConnectionInfo": "功能: 获取连接详细信息\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: info\n          类型: FORWARD_CONNECTION_INFO*\n          描述: 连接信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取连接信息\n      示例: connector.GetConnectionInfo(connections[i], &info)", "ResumeForwardWithOptions": "功能: 使用高级选项恢复暂停的端口转发连接\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: options\n          类型: RESUME_OPTIONS*\n          描述: 恢复选项结构体指针\n        - 名称: result\n          类型: RESUME_RESULT*\n          描述: 恢复结果结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功恢复连接\n      示例: connector.ResumeForwardWithOptions(pausedConnectionId, &options, &result)", "SetAutoRecoveryPolicy": "功能: 设置连接自动恢复策略\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: policy\n          类型: AUTO_RECOVERY_POLICY*\n          描述: 自动恢复策略结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功设置恢复策略\n      示例: connector.SetAutoRecoveryPolicy(pausedConnectionId, &policy)", "ConnectForward": "功能: 建立端口转发连接\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 转发规则ID\n        - 名称: options\n          类型: CONNECT_OPTIONS*\n          描述: 连接选项结构体指针\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID*\n          描述: 用于存储创建的连接ID\n      返回值:\n        类型: bool\n        描述: 连接是否成功建立\n      示例: connector.ConnectForward(ruleId, &connectOptions, &newConnectionId)"}, "数据结构": {"RESUME_OPTIONS": "描述: 恢复连接选项结构体\n      字段:\n        - 名称: resetBuffers\n          类型: bool\n          描述: 是否重置传输缓冲区\n        - 名称: restoreFullBandwidth\n          类型: bool\n          描述: 是否恢复完整带宽\n        - 名称: resyncConnection\n          类型: bool\n          描述: 是否重新同步连接状态\n        - 名称: flags\n          类型: uint32_t\n          描述: 恢复标志位", "RESUME_RESULT": "描述: 恢复操作结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 恢复操作是否成功\n        - 名称: resumeTime\n          类型: uint64_t\n          描述: 恢复时间戳\n        - 名称: pauseDuration\n          类型: uint32_t\n          描述: 暂停持续时间(秒)\n        - 名称: errorCode\n          类型: uint16_t\n          描述: 错误代码(如果失败)", "AUTO_RECOVERY_POLICY": "描述: 自动恢复策略结构体\n      字段:\n        - 名称: enabled\n          类型: bool\n          描述: 是否启用自动恢复\n        - 名称: maxRetries\n          类型: uint8_t\n          描述: 最大重试次数\n        - 名称: retryIntervalMs\n          类型: uint32_t\n          描述: 重试间隔(毫秒)\n        - 名称: exponentialBackoff\n          类型: bool\n          描述: 是否使用指数退避算法\n        - 名称: maxBackoffMs\n          类型: uint32_t\n          描述: 最大退避时间(毫秒)"}}