{"示例代码": {"反虚拟机检测示例": "int main() {\n    // 初始化反虚拟机检测组件\n    VMDetector vmDetector;\n    \n    // 注册多种虚拟机检测技术\n    vmDetector.registerDetectionMethod(VMDetector::CPU_VENDOR_CHECK);\n    vmDetector.registerDetectionMethod(VMDetector::DEVICE_CHECK);\n    vmDetector.registerDetectionMethod(VMDetector::MEMORY_ARTIFACTS_CHECK);\n    \n    // 执行虚拟机环境检测\n    DetectionResult result = vmDetector.detectVirtualEnvironment();\n    \n    if (result.isVirtualMachine) {\n        // 检测到虚拟环境，执行相应策略\n        switch (vmDetector.getResponseStrategy()) {\n            case RESPONSE_EXIT:\n                return -1;  // 直接退出\n            case RESPONSE_FAKE_BEHAVIOR:\n                performFakeBehavior();  // 展示假行为\n                break;\n            case RESPONSE_HIDDEN_EXECUTION:\n                enterStealthMode();  // 进入隐身模式\n                break;\n        }\n    }\n    \n    // 正常程序逻辑\n    performMainOperation();\n    \n    return 0;\n}"}, "API接口": {"registerDetectionMethod": "功能: 注册一种虚拟机检测方法\n      参数:\n        - 名称: methodType\n          类型: DetectionMethodType\n          描述: 要注册的检测方法类型\n      返回值:\n        类型: bool\n        描述: 是否成功注册检测方法\n      示例: vmDetector.registerDetectionMethod(VMDetector::CPU_VENDOR_CHECK);", "detectVirtualEnvironment": "功能: 执行虚拟环境检测\n      参数:\n      返回值:\n        类型: DetectionResult\n        描述: 检测结果，包含是否为虚拟机及详细信息\n      示例: DetectionResult result = vmDetector.detectVirtualEnvironment();", "setResponseStrategy": "功能: 设置检测到虚拟环境时的响应策略\n      参数:\n        - 名称: strategy\n          类型: ResponseStrategy\n          描述: 响应策略类型\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: vmDetector.setResponseStrategy(VMDetector::RESPONSE_FAKE_BEHAVIOR);", "getResponseStrategy": "功能: 获取当前设置的响应策略\n      参数:\n      返回值:\n        类型: ResponseStrategy\n        描述: 当前设置的响应策略\n      示例: ResponseStrategy strategy = vmDetector.getResponseStrategy();"}, "数据结构": {"DetectionMethodType": "描述: 虚拟机检测方法类型枚举\n      字段:\n        - 名称: CPU_VENDOR_CHECK\n          类型: enum\n          描述: 检查CPU厂商字符串\n        - 名称: DEVICE_CHECK\n          类型: enum\n          描述: 检查设备特征和驱动程序\n        - 名称: MEMORY_ARTIFACTS_CHECK\n          类型: enum\n          描述: 检查内存中的虚拟机特征\n        - 名称: REGISTRY_CHECK\n          类型: enum\n          描述: 检查系统配置特征\n        - 名称: MAC_ADDRESS_CHECK\n          类型: enum\n          描述: 检查网卡MAC地址特征", "ResponseStrategy": "描述: 虚拟环境检测后的响应策略枚举\n      字段:\n        - 名称: RESPONSE_EXIT\n          类型: enum\n          描述: 立即退出程序\n        - 名称: RESPONSE_FAKE_BEHAVIOR\n          类型: enum\n          描述: 展示虚假行为以迷惑分析\n        - 名称: RESPONSE_HIDDEN_EXECUTION\n          类型: enum\n          描述: 进入隐身模式，隐藏真实行为\n        - 名称: RESPONSE_DELAYED_ACTION\n          类型: enum\n          描述: 延迟执行真实行为", "DetectionResult": "描述: 虚拟环境检测结果结构体\n      字段:\n        - 名称: isVirtualMachine\n          类型: bool\n          描述: 是否为虚拟环境\n        - 名称: vmType\n          类型: VirtualMachineType\n          描述: 检测到的虚拟机类型\n        - 名称: confidenceLevel\n          类型: float\n          描述: 检测结果的置信度（0.0-1.0）\n        - 名称: detectedFeatures\n          类型: std::vector<std::string>\n          描述: 检测到的虚拟环境特征列表"}}