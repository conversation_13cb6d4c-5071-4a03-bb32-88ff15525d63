{"示例代码": {"动态链接库劫持示例": "int main() {\n    // 创建动态链接库劫持管理器\n    LibraryHijack hijacker;\n    \n    // 配置劫持选项\n    LibraryHijackConfig config;\n    config.targetLibrary = \"/lib/x86_64-linux-gnu/libc.so.6\";\n    config.replacementLibraryPath = \"/opt/malicious/fake_libc.so\";\n    config.backupOriginalLib = true;\n    config.originalBackupPath = \"/opt/.backup/libc.so.6.bak\";\n    \n    // 设置劫持函数\n    std::vector<HijackedFunction> functions = {\n        {\n            .originalName = \"system\",\n            .replacementName = \"hijacked_system\",\n            .preserveOriginal = true\n        },\n        {\n            .originalName = \"open\",\n            .replacementName = \"hijacked_open\",\n            .preserveOriginal = true\n        },\n        {\n            .originalName = \"connect\",\n            .replacementName = \"hijacked_connect\",\n            .preserveOriginal = true\n        }\n    };\n    \n    config.hijackedFunctions = functions;\n    \n    // 设置劫持方法\n    hijacker.setHijackMethod(HIJACK_METHOD_LD_PRELOAD);\n    \n    // 安装劫持\n    HijackResult result = hijacker.install(config);\n    \n    if (result.success) {\n        printf(\"Library hijack installed successfully\\n\");\n    } else {\n        printf(\"Library hijack failed: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 检查劫持状态\n    HijackStatus status = hijacker.checkStatus(config.targetLibrary);\n    if (status.isActive) {\n        printf(\"Hijack is active with %d functions\\n\", status.hijackedFunctionCount);\n    }\n    \n    // 如需卸载劫持\n    // hijacker.uninstall(config.targetLibrary);\n    \n    return 0;\n}"}, "API接口": {"setHijackMethod": "功能: 设置库劫持方法\n      参数:\n        - 名称: method\n          类型: HijackMethod\n          描述: 劫持方法枚举\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hijacker.setHijackMethod(HIJACK_METHOD_LD_PRELOAD);", "install": "功能: 安装库劫持\n      参数:\n        - 名称: config\n          类型: LibraryHijackConfig\n          描述: 劫持配置\n      返回值:\n        类型: HijackResult\n        描述: 劫持安装结果\n      示例: HijackResult result = hijacker.install(config);", "checkStatus": "功能: 检查劫持状态\n      参数:\n        - 名称: targetLibrary\n          类型: const std::string&\n          描述: 目标库路径\n      返回值:\n        类型: HijackStatus\n        描述: 劫持状态\n      示例: HijackStatus status = hijacker.checkStatus(config.targetLibrary);", "uninstall": "功能: 卸载库劫持\n      参数:\n        - 名称: targetLibrary\n          类型: const std::string&\n          描述: 目标库路径\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = hijacker.uninstall(config.targetLibrary);", "addHijackedFunction": "功能: 添加要劫持的函数\n      参数:\n        - 名称: originalName\n          类型: const std::string&\n          描述: 原始函数名\n        - 名称: replacementName\n          类型: const std::string&\n          描述: 替换函数名\n        - 名称: preserveOriginal\n          类型: bool\n          描述: 是否保留原始函数调用\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hijacker.addHijackedFunction(\"open\", \"hijacked_open\", true);"}, "数据结构": {"LibraryHijackConfig": "描述: 动态链接库劫持配置结构体\n      字段:\n        - 名称: targetLibrary\n          类型: std::string\n          描述: 目标库路径\n        - 名称: replacementLibraryPath\n          类型: std::string\n          描述: 替换库路径\n        - 名称: backupOriginalLib\n          类型: bool\n          描述: 是否备份原始库\n        - 名称: originalBackupPath\n          类型: std::string\n          描述: 原始库备份路径\n        - 名称: hijackedFunctions\n          类型: std::vector<HijackedFunction>\n          描述: 要劫持的函数列表", "HijackedFunction": "描述: 被劫持的函数描述结构体\n      字段:\n        - 名称: originalName\n          类型: std::string\n          描述: 原始函数名\n        - 名称: replacementName\n          类型: std::string\n          描述: 替换函数名\n        - 名称: preserveOriginal\n          类型: bool\n          描述: 是否保留原始函数调用\n        - 名称: hookType\n          类型: HookType\n          描述: 劫持类型（前置钩子、后置钩子、完全替换）", "HijackResult": "描述: 劫持结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 劫持是否成功\n        - 名称: installedLibraryPath\n          类型: std::string\n          描述: 已安装的库路径\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息", "HijackStatus": "描述: 劫持状态结构体\n      字段:\n        - 名称: isActive\n          类型: bool\n          描述: 劫持是否有效\n        - 名称: hijackedFunctionCount\n          类型: int\n          描述: 被劫持的函数数量\n        - 名称: installMethod\n          类型: HijackMethod\n          描述: 使用的安装方法\n        - 名称: affectedProcessCount\n          类型: int\n          描述: 受影响的进程数量"}}