#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import sys

def convert_to_string_format(obj, indent_level=0):
    """将对象转换为格式化字符串，保留缩进"""
    result = ""
    if isinstance(obj, dict):
        indent = "  " * indent_level
        first_key = True
        for key, value in obj.items():
            if not first_key:
                result += "\n"
            else:
                first_key = False
            
            # 对于顶级键值，使用冒号加空格
            result += f"{indent}{key}: {convert_to_string_format(value, indent_level + 1)}"
    elif isinstance(obj, list):
        for i, item in enumerate(obj):
            indent = "  " * (indent_level - 1) if indent_level > 0 else ""
            result += f"{indent}- {convert_to_string_format(item, indent_level + 1)}"
            if i < len(obj) - 1:
                result += "\n"
    else:
        return str(obj)
    return result

def process_json_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 处理API接口
        if "API接口" in data:
            for api_name, api_content in data["API接口"].items():
                if isinstance(api_content, dict):
                    # 构建字符串表示
                    content_str = "功能: " + api_content.get("功能", "") + "\n"
                    
                    # 处理参数
                    content_str += "      参数:\n"
                    if "参数" in api_content and isinstance(api_content["参数"], list):
                        for param in api_content["参数"]:
                            content_str += f"        - 名称: {param.get('名称', '')}\n"
                            content_str += f"          类型: {param.get('类型', '')}\n"
                            content_str += f"          描述: {param.get('描述', '')}\n"
                    
                    # 处理返回值
                    content_str += "      返回值:\n"
                    if "返回值" in api_content and isinstance(api_content["返回值"], dict):
                        content_str += f"        类型: {api_content['返回值'].get('类型', '')}\n"
                        content_str += f"        描述: {api_content['返回值'].get('描述', '')}\n"
                    
                    # 处理示例
                    if "示例" in api_content:
                        content_str += f"      示例: {api_content.get('示例', '')}"
                    
                    # 更新原JSON
                    data["API接口"][api_name] = content_str
        
        # 处理数据结构
        if "数据结构" in data:
            for struct_name, struct_content in data["数据结构"].items():
                if isinstance(struct_content, dict):
                    # 构建字符串表示
                    struct_str = "描述: " + struct_content.get("描述", "") + "\n"
                    
                    # 处理字段
                    struct_str += "      字段:\n"
                    if "字段" in struct_content and isinstance(struct_content["字段"], list):
                        for field in struct_content["字段"]:
                            struct_str += f"        - 名称: {field.get('名称', '')}\n"
                            struct_str += f"          类型: {field.get('类型', '')}\n"
                            struct_str += f"          描述: {field.get('描述', '')}\n"
                    
                    # 更新原JSON
                    data["数据结构"][struct_name] = struct_str.rstrip()
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return True
    except Exception as e:
        print(f"处理文件 {file_path} 时发生错误: {str(e)}")
        return False

def main():
    dir_path = os.path.dirname(os.path.realpath(__file__))
    success_count = 0
    fail_count = 0
    
    # 获取所有JSON文件
    json_files = [f for f in os.listdir(dir_path) if f.endswith('.json')]
    total_files = len(json_files)
    
    print(f"找到 {total_files} 个JSON文件，开始处理...")
    
    for json_file in json_files:
        file_path = os.path.join(dir_path, json_file)
        print(f"处理: {json_file}...", end="")
        
        # 跳过已经处理过的A0001.json
        if json_file == "A0001.json":
            print(" 已处理，跳过。")
            continue
        
        if process_json_file(file_path):
            success_count += 1
            print(" 成功!")
        else:
            fail_count += 1
            print(" 失败!")
    
    print(f"\n处理完成! 成功: {success_count}, 失败: {fail_count}, 总计: {total_files}")

if __name__ == "__main__":
    main()
