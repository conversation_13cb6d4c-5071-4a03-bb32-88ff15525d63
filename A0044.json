{"示例代码": {"接收系统信息示例": "int main() {\n    // 初始化系统信息接收器\n    SystemInfoReceiver receiver;\n    \n    // 启动系统信息监听服务\n    if (!receiver.StartListening(CONTROL_PORT)) {\n        printf(\"启动信息接收服务失败\\n\");\n        return 1;\n    }\n    \n    printf(\"等待接收系统信息...\\n\");\n    \n    // 接收加密的系统信息数据\n    ENCRYPTED_DATA encData;\n    if (receiver.ReceiveSystemInfo(&encData, 60000)) { // 等待60秒\n        printf(\"成功接收系统信息数据\\n\");\n        \n        // 解密系统信息\n        SYSTEM_INFO_DATA sysInfo;\n        if (receiver.DecryptAndDecompress(&encData, &sysInfo)) {\n            // 显示系统详细信息\n            printf(\"设备详细档案:\\n\");\n            printf(\"操作系统: %s\\n\", sysInfo.osVersion);\n            printf(\"内核版本: %s\\n\", sysInfo.kernelVersion);\n            printf(\"主机名: %s\\n\", sysInfo.hostname);\n            printf(\"CPU型号: %s\\n\", sysInfo.cpuModel);\n            printf(\"CPU核心数: %d\\n\", sysInfo.cpuCores);\n            printf(\"总内存: %lu MB\\n\", sysInfo.totalMemory / (1024*1024));\n            \n            // 保存系统信息到数据库\n            DeviceDatabase::SaveSystemInfo(&sysInfo);\n        } else {\n            printf(\"系统信息解密失败\\n\");\n        }\n        \n        // 释放加密数据\n        receiver.FreeEncryptedData(&encData);\n    } else {\n        printf(\"接收系统信息超时\\n\");\n    }\n    \n    // 停止监听服务\n    receiver.StopListening();\n    \n    return 0;\n}"}, "API接口": {"StartListening": "功能: 启动系统信息接收监听服务\n      参数:\n        - 名称: port\n          类型: uint16_t\n          描述: 监听端口\n      返回值:\n        类型: bool\n        描述: 服务是否成功启动\n      示例: receiver.StartListening(CONTROL_PORT)", "StopListening": "功能: 停止系统信息接收监听服务\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: receiver.StopListening()", "ReceiveSystemInfo": "功能: 接收加密的系统信息数据\n      参数:\n        - 名称: encData\n          类型: ENCRYPTED_DATA*\n          描述: 用于存储接收到的加密数据\n        - 名称: timeout\n          类型: uint32_t\n          描述: 接收超时时间(毫秒)\n      返回值:\n        类型: bool\n        描述: 是否成功接收数据\n      示例: receiver.ReceiveSystemInfo(&encData, 60000)", "DecryptAndDecompress": "功能: 解密并解压系统信息数据\n      参数:\n        - 名称: encData\n          类型: ENCRYPTED_DATA*\n          描述: 加密的数据\n        - 名称: sysInfo\n          类型: SYSTEM_INFO_DATA*\n          描述: 解密后的系统信息\n      返回值:\n        类型: bool\n        描述: 解密解压是否成功\n      示例: receiver.DecryptAndDecompress(&encData, &sysInfo)"}, "数据结构": {"ENCRYPTED_DATA": "描述: 加密数据结构\n      字段:\n        - 名称: data\n          类型: unsigned char*\n          描述: 加密数据缓冲区\n        - 名称: dataSize\n          类型: uint32_t\n          描述: 数据大小\n        - 名称: checksum\n          类型: uint32_t\n          描述: 数据校验和\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 加密时间戳", "SYSTEM_INFO_DATA": "描述: 系统信息数据结构\n      字段:\n        - 名称: osVersion\n          类型: char[256]\n          描述: 操作系统版本信息\n        - 名称: kernelVersion\n          类型: char[128]\n          描述: 内核版本信息\n        - 名称: hostname\n          类型: char[64]\n          描述: 主机名\n        - 名称: cpuModel\n          类型: char[256]\n          描述: CPU型号信息\n        - 名称: cpuCores\n          类型: int\n          描述: CPU核心数\n        - 名称: totalMemory\n          类型: uint64_t\n          描述: 系统总内存(字节)\n        - 名称: networkInterfaces\n          类型: NETWORK_INTERFACE[8]\n          描述: 网络接口信息", "DEVICE_PROFILE": "描述: 设备档案结构体\n      字段:\n        - 名称: deviceId\n          类型: char[64]\n          描述: 设备唯一标识符\n        - 名称: systemInfo\n          类型: SYSTEM_INFO_DATA\n          描述: 系统信息数据\n        - 名称: firstSeenTime\n          类型: uint64_t\n          描述: 首次发现时间\n        - 名称: lastUpdateTime\n          类型: uint64_t\n          描述: 最后更新时间"}}