{"示例代码": {"远程投递示例": "int main() {\n    // 创建远程投递客户端\n    RemoteDeliveryClient client;\n    \n    // 配置远程服务器信息\n    DeliveryServerConfig config;\n    config.serverAddress = \"remote-server.example.com\";\n    config.port = 8080;\n    config.useSecureChannel = true;\n    \n    // 连接到远程服务器\n    ConnectionResult result = client.connect(config);\n    if (result.isSuccess()) {\n        // 准备要投递的数据\n        DeliveryPackage package;\n        package.addFile(\"/path/to/local/file.dat\");\n        package.addMetadata(\"taskId\", \"12345\");\n        package.addMetadata(\"priority\", \"high\");\n        \n        // 提交远程投递任务\n        DeliveryResult deliveryResult = client.submit(package);\n        if (deliveryResult.isSuccess()) {\n            printf(\"投递成功，任务ID: %s\\n\", deliveryResult.getTaskId().c_str());\n        } else {\n            printf(\"投递失败: %s\\n\", deliveryResult.getErrorMessage().c_str());\n        }\n        \n        // 断开连接\n        client.disconnect();\n    } else {\n        printf(\"连接失败: %s\\n\", result.getErrorMessage().c_str());\n    }\n    \n    return 0;\n}"}, "API接口": {"connect": "功能: 连接到远程投递服务器\n      参数:\n        - 名称: config\n          类型: DeliveryServerConfig\n          描述: 服务器配置信息，包括地址、端口和安全设置\n      返回值:\n        类型: ConnectionResult\n        描述: 连接结果，包含连接是否成功以及可能的错误信息\n      示例: ConnectionResult result = client.connect(config);", "submit": "功能: 提交投递包到远程服务器\n      参数:\n        - 名称: package\n          类型: DeliveryPackage\n          描述: 包含要投递的文件和元数据信息的投递包\n        - 名称: options\n          类型: DeliveryOptions\n          描述: 可选的投递选项，如重试策略、超时时间等\n      返回值:\n        类型: DeliveryResult\n        描述: 投递结果，包含成功状态、任务ID和可能的错误信息\n      示例: DeliveryResult result = client.submit(package, DeliveryOptions::default());", "checkStatus": "功能: 检查远程投递任务的状态\n      参数:\n        - 名称: taskId\n          类型: string\n          描述: 要查询的投递任务ID\n      返回值:\n        类型: TaskStatus\n        描述: 任务状态信息，包括当前状态、进度和详细信息\n      示例: TaskStatus status = client.checkStatus(\"task-12345\");", "cancelTask": "功能: 取消正在进行的远程投递任务\n      参数:\n        - 名称: taskId\n          类型: string\n          描述: 要取消的投递任务ID\n      返回值:\n        类型: bool\n        描述: 取消操作是否成功\n      示例: bool cancelled = client.cancelTask(\"task-12345\");", "disconnect": "功能: 断开与远程服务器的连接\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: client.disconnect();"}, "数据结构": {"DeliveryServerConfig": "描述: 远程服务器配置信息\n      字段:\n        - 名称: serverAddress\n          类型: string\n          描述: 服务器地址，可以是域名或IP地址\n        - 名称: port\n          类型: int\n          描述: 服务器端口号\n        - 名称: useSecureChannel\n          类型: bool\n          描述: 是否使用加密通道进行通信\n        - 名称: authToken\n          类型: string\n          描述: 认证令牌，用于服务器身份验证\n        - 名称: timeout\n          类型: int\n          描述: 连接超时时间（毫秒）", "DeliveryPackage": "描述: 投递包，包含要投递的文件和元数据\n      字段:\n        - 名称: files\n          类型: vector<string>\n          描述: 要投递的文件路径列表\n        - 名称: metadata\n          类型: map<string, string>\n          描述: 与投递包关联的元数据键值对\n        - 名称: compressionLevel\n          类型: int\n          描述: 数据压缩级别（0-9，0表示不压缩）\n        - 名称: encryptionEnabled\n          类型: bool\n          描述: 是否对数据进行加密", "DeliveryResult": "描述: 投递操作的结果\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 投递是否成功\n        - 名称: taskId\n          类型: string\n          描述: 服务器分配的任务ID，用于后续查询或取消操作\n        - 名称: errorCode\n          类型: int\n          描述: 错误代码，成功时为0\n        - 名称: errorMessage\n          类型: string\n          描述: 详细的错误信息\n        - 名称: serverReceiptTime\n          类型: timestamp\n          描述: 服务器接收时间戳", "TaskStatus": "描述: 投递任务的状态信息\n      字段:\n        - 名称: taskId\n          类型: string\n          描述: 任务ID\n        - 名称: state\n          类型: enum\n          描述: 任务状态（PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED）\n        - 名称: progress\n          类型: float\n          描述: 任务进度百分比（0-100）\n        - 名称: message\n          类型: string\n          描述: 状态详细信息或错误消息\n        - 名称: completionTime\n          类型: timestamp\n          描述: 任务完成时间戳"}}