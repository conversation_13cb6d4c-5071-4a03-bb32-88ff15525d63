{"示例代码": {"进程内存控制示例": "int main() {\n    // 创建进程内存控制器\n    ProcessMemoryController controller;\n    \n    // 设置控制器配置\n    MemoryControlConfig config;\n    config.targetProcess = \"target_process_name\";\n    // 或者通过进程ID指定目标\n    // config.targetPid = 1234;\n    \n    config.accessLevel = ACCESS_LEVEL_FULL;\n    config.useKernelMode = false;\n    \n    // 初始化控制器\n    if (!controller.initialize(config)) {\n        printf(\"Failed to initialize process memory controller: %s\\n\", \n               controller.getLastError().c_str());\n        return 1;\n    }\n    \n    printf(\"Process memory controller initialized\\n\");\n    printf(\"Target process: %s (PID: %d)\\n\", \n           controller.getProcessName().c_str(), controller.getProcessId());\n    \n    // 获取进程内存信息\n    ProcessMemoryInfo memInfo = controller.getProcessMemoryInfo();\n    printf(\"\\nProcess memory info:\\n\");\n    printf(\"- Working set: %zu bytes\\n\", memInfo.workingSetSize);\n    printf(\"- Private bytes: %zu bytes\\n\", memInfo.privateBytes);\n    printf(\"- Virtual size: %zu bytes\\n\", memInfo.virtualSize);\n    \n    // 枚举内存区域\n    std::vector<MemoryRegion> regions = controller.enumerateMemoryRegions();\n    printf(\"\\nFound %zu memory regions\\n\", regions.size());\n    \n    // 显示部分内存区域信息\n    int displayCount = std::min(5, (int)regions.size());\n    printf(\"\\nShowing first %d memory regions:\\n\", displayCount);\n    for (int i = 0; i < displayCount; i++) {\n        const MemoryRegion& region = regions[i];\n        printf(\"Region %d:\\n\", i + 1);\n        printf(\"- Base address: 0x%lx\\n\", region.baseAddress);\n        printf(\"- Size: %zu bytes\\n\", region.size);\n        printf(\"- Protection: %s\\n\", \n               controller.getProtectionString(region.protection).c_str());\n        printf(\"- Type: %s\\n\", region.regionType.c_str());\n    }\n    \n    // 示例：读取内存\n    if (!regions.empty()) {\n        const MemoryRegion& region = regions[0];\n        size_t readSize = std::min(region.size, (size_t)128);\n        \n        std::vector<uint8_t> buffer(readSize);\n        if (controller.readMemory(region.baseAddress, buffer.data(), readSize)) {\n            printf(\"\\nRead %zu bytes from address 0x%lx:\\n\", readSize, region.baseAddress);\n            printf(\"Hex dump: \");\n            for (size_t i = 0; i < std::min(readSize, (size_t)16); i++) {\n                printf(\"%02X \", buffer[i]);\n            }\n            printf(\"...\\n\");\n        } else {\n            printf(\"\\nFailed to read memory: %s\\n\", controller.getLastError().c_str());\n        }\n    }\n    \n    // 示例：写入内存\n    if (!regions.empty()) {\n        const MemoryRegion& region = regions[0];\n        if ((region.protection & MEMORY_PROTECTION_WRITE) != 0) {\n            uint8_t dataToWrite[] = {0x11, 0x22, 0x33, 0x44};\n            if (controller.writeMemory(region.baseAddress, dataToWrite, sizeof(dataToWrite))) {\n                printf(\"\\nSuccessfully wrote 4 bytes to address 0x%lx\\n\", \n                       region.baseAddress);\n            } else {\n                printf(\"\\nFailed to write memory: %s\\n\", controller.getLastError().c_str());\n            }\n        } else {\n            printf(\"\\nCannot write to the selected region (no write permission)\\n\");\n        }\n    }\n    \n    // 示例：分配新内存\n    size_t allocationSize = 4096;  // 4KB\n    uintptr_t allocatedAddress = controller.allocateMemory(allocationSize, \n                                                       MEMORY_PROTECTION_READ_WRITE);\n    \n    if (allocatedAddress != 0) {\n        printf(\"\\nAllocated %zu bytes at address 0x%lx\\n\", \n               allocationSize, allocatedAddress);\n        \n        // 向分配的内存写入数据\n        std::vector<uint8_t> dataToWrite(256, 0xAA);\n        if (controller.writeMemory(allocatedAddress, dataToWrite.data(), dataToWrite.size())) {\n            printf(\"Wrote %zu bytes to allocated memory\\n\", dataToWrite.size());\n        }\n        \n        // 释放分配的内存\n        if (controller.freeMemory(allocatedAddress)) {\n            printf(\"Successfully freed allocated memory\\n\");\n        }\n    } else {\n        printf(\"\\nFailed to allocate memory: %s\\n\", controller.getLastError().c_str());\n    }\n    \n    // 清理资源\n    controller.cleanup();\n    printf(\"\\nProcess memory controller cleaned up\\n\");\n    \n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化进程内存控制器\n      参数:\n        - 名称: config\n          类型: MemoryControlConfig\n          描述: 控制器配置\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: bool success = controller.initialize(config);", "getProcessMemoryInfo": "功能: 获取进程内存信息\n      参数:\n      返回值:\n        类型: ProcessMemoryInfo\n        描述: 进程内存信息\n      示例: ProcessMemoryInfo info = controller.getProcessMemoryInfo();", "enumerateMemoryRegions": "功能: 枚举内存区域\n      参数:\n      返回值:\n        类型: std::vector<MemoryRegion>\n        描述: 内存区域列表\n      示例: auto regions = controller.enumerateMemoryRegions();", "readMemory": "功能: 读取内存\n      参数:\n        - 名称: address\n          类型: uintptr_t\n          描述: 内存地址\n        - 名称: buffer\n          类型: void*\n          描述: 目标缓冲区\n        - 名称: size\n          类型: size_t\n          描述: 要读取的字节数\n      返回值:\n        类型: bool\n        描述: 读取是否成功\n      示例: bool success = controller.readMemory(address, buffer, size);", "writeMemory": "功能: 写入内存\n      参数:\n        - 名称: address\n          类型: uintptr_t\n          描述: 内存地址\n        - 名称: data\n          类型: const void*\n          描述: 要写入的数据\n        - 名称: size\n          类型: size_t\n          描述: 要写入的字节数\n      返回值:\n        类型: bool\n        描述: 写入是否成功\n      示例: bool success = controller.writeMemory(address, data, dataSize);", "allocateMemory": "功能: 分配内存\n      参数:\n        - 名称: size\n          类型: size_t\n          描述: 内存大小\n        - 名称: protection\n          类型: MemoryProtection\n          描述: 内存保护标志\n      返回值:\n        类型: uintptr_t\n        描述: 分配的内存地址，0表示失败\n      示例: uintptr_t address = controller.allocateMemory(4096, MEMORY_PROTECTION_READ_WRITE);", "freeMemory": "功能: 释放内存\n      参数:\n        - 名称: address\n          类型: uintptr_t\n          描述: 内存地址\n      返回值:\n        类型: bool\n        描述: 释放是否成功\n      示例: bool success = controller.freeMemory(address);", "cleanup": "功能: 清理资源\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: controller.cleanup();"}, "数据结构": {"MemoryControlConfig": "描述: 内存控制配置结构体\n      字段:\n        - 名称: targetProcess\n          类型: std::string\n          描述: 目标进程名称\n        - 名称: targetPid\n          类型: pid_t\n          描述: 目标进程ID\n        - 名称: accessLevel\n          类型: AccessLevel\n          描述: 访问级别\n        - 名称: useKernelMode\n          类型: bool\n          描述: 是否使用内核模式", "AccessLevel": "描述: 访问级别枚举\n      字段:\n        - 名称: ACCESS_LEVEL_READ\n          类型: enum\n          描述: 只读访问\n        - 名称: ACCESS_LEVEL_WRITE\n          类型: enum\n          描述: 读写访问\n        - 名称: ACCESS_LEVEL_FULL\n          类型: enum\n          描述: 完全访问（读写执行）", "ProcessMemoryInfo": "描述: 进程内存信息结构体\n      字段:\n        - 名称: workingSetSize\n          类型: size_t\n          描述: 工作集大小\n        - 名称: privateBytes\n          类型: size_t\n          描述: 私有字节数\n        - 名称: virtualSize\n          类型: size_t\n          描述: 虚拟大小\n        - 名称: pagefileUsage\n          类型: size_t\n          描述: 页面文件使用量", "MemoryRegion": "描述: 内存区域结构体\n      字段:\n        - 名称: baseAddress\n          类型: uintptr_t\n          描述: 基址\n        - 名称: size\n          类型: size_t\n          描述: 大小\n        - 名称: protection\n          类型: MemoryProtection\n          描述: 保护标志\n        - 名称: state\n          类型: MemoryState\n          描述: 状态\n        - 名称: regionType\n          类型: std::string\n          描述: 区域类型", "MemoryProtection": "描述: 内存保护标志枚举\n      字段:\n        - 名称: MEMORY_PROTECTION_NONE\n          类型: enum\n          描述: 无保护\n        - 名称: MEMORY_PROTECTION_READ\n          类型: enum\n          描述: 读保护\n        - 名称: MEMORY_PROTECTION_WRITE\n          类型: enum\n          描述: 写保护\n        - 名称: MEMORY_PROTECTION_EXECUTE\n          类型: enum\n          描述: 执行保护\n        - 名称: MEMORY_PROTECTION_READ_WRITE\n          类型: enum\n          描述: 读写保护\n        - 名称: MEMORY_PROTECTION_READ_EXECUTE\n          类型: enum\n          描述: 读执行保护\n        - 名称: MEMORY_PROTECTION_READ_WRITE_EXECUTE\n          类型: enum\n          描述: 读写执行保护", "MemoryState": "描述: 内存状态枚举\n      字段:\n        - 名称: MEMORY_STATE_COMMIT\n          类型: enum\n          描述: 已提交\n        - 名称: MEMORY_STATE_RESERVE\n          类型: enum\n          描述: 已保留\n        - 名称: MEMORY_STATE_FREE\n          类型: enum\n          描述: 空闲"}}