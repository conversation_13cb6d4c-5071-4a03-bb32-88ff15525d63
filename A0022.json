{"示例代码": {"定时执行示例": "int main() {\n    // 创建定时执行管理器\n    ScheduledExecutor executor;\n    \n    // 配置定时任务\n    ScheduledTaskConfig task;\n    task.executable = \"/opt/payload/scan_network.sh\";\n    task.arguments = \"--stealth --report=/tmp/results.dat\";\n    task.schedulingPattern = \"0 3 * * *\";  // 每天凌晨3点执行\n    task.taskName = \"NetworkScan\";\n    task.taskDescription = \"Network security scan\";\n    \n    // 设置执行约束\n    task.constraints.maxCpuUsage = 30.0;  // 最大CPU使用率30%\n    task.constraints.maxMemoryUsage = 200;  // 最大内存使用200MB\n    task.constraints.executeOnlyWhenIdle = true;  // 仅系统空闲时执行\n    task.constraints.stopIfUserActive = true;  // 用户活动时暂停\n    \n    // 配置高级选项\n    SchedulingOptions options;\n    options.randomizeExecutionTime = true;  // 随机化执行时间\n    options.randomWindow = 30 * 60;  // 30分钟窗口\n    options.prioritizeStealth = true;  // 优先考虑隐蔽性\n    options.backupMissedSchedules = true;  // 备份错过的计划\n    \n    // 注册定时任务\n    ScheduleResult result = executor.scheduleTask(task, options);\n    \n    if (result.success) {\n        printf(\"Task scheduled successfully\\n\");\n        printf(\"Task ID: %s\\n\", result.taskId.c_str());\n        printf(\"Next execution time: %s\\n\", ctime(&result.nextExecutionTime));\n    } else {\n        printf(\"Failed to schedule task: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 列出所有计划任务\n    std::vector<ScheduledTask> tasks = executor.listScheduledTasks();\n    printf(\"Total scheduled tasks: %zu\\n\", tasks.size());\n    \n    // 取消特定任务\n    // executor.cancelTask(result.taskId);\n    \n    // 启动定时器服务（如果尚未运行）\n    executor.startSchedulerService();\n    \n    return 0;\n}"}, "API接口": {"scheduleTask": "功能: 计划新的定时任务\n      参数:\n        - 名称: task\n          类型: ScheduledTaskConfig\n          描述: 任务配置\n        - 名称: options\n          类型: SchedulingOptions\n          描述: 调度选项\n      返回值:\n        类型: ScheduleResult\n        描述: 调度结果\n      示例: ScheduleResult result = executor.scheduleTask(task, options);", "cancelTask": "功能: 取消已计划的任务\n      参数:\n        - 名称: taskId\n          类型: const std::string&\n          描述: 任务ID\n      返回值:\n        类型: bool\n        描述: 取消是否成功\n      示例: bool canceled = executor.cancelTask(\"NetworkScan_12345\");", "listScheduledTasks": "功能: 列出所有计划的任务\n      参数:\n      返回值:\n        类型: std::vector<ScheduledTask>\n        描述: 计划任务列表\n      示例: std::vector<ScheduledTask> tasks = executor.listScheduledTasks();", "getTaskStatus": "功能: 获取特定任务的状态\n      参数:\n        - 名称: taskId\n          类型: const std::string&\n          描述: 任务ID\n      返回值:\n        类型: TaskStatus\n        描述: 任务状态信息\n      示例: TaskStatus status = executor.getTaskStatus(\"NetworkScan_12345\");", "startSchedulerService": "功能: 启动调度器服务\n      参数:\n      返回值:\n        类型: bool\n        描述: 启动是否成功\n      示例: bool started = executor.startSchedulerService();"}, "数据结构": {"ScheduledTaskConfig": "描述: 计划任务配置结构体\n      字段:\n        - 名称: executable\n          类型: std::string\n          描述: 可执行文件路径\n        - 名称: arguments\n          类型: std::string\n          描述: 命令行参数\n        - 名称: schedulingPattern\n          类型: std::string\n          描述: 调度模式（cron格式）\n        - 名称: taskName\n          类型: std::string\n          描述: 任务名称\n        - 名称: taskDescription\n          类型: std::string\n          描述: 任务描述\n        - 名称: constraints\n          类型: ExecutionConstraints\n          描述: 执行约束", "ExecutionConstraints": "描述: 执行约束结构体\n      字段:\n        - 名称: maxCpuUsage\n          类型: float\n          描述: 最大CPU使用率百分比\n        - 名称: maxMemoryUsage\n          类型: uint32_t\n          描述: 最大内存使用量（MB）\n        - 名称: executeOnlyWhenIdle\n          类型: bool\n          描述: 是否仅在系统空闲时执行\n        - 名称: stopIfUserActive\n          类型: bool\n          描述: 用户活动时是否暂停执行\n        - 名称: networkConstraints\n          类型: NetworkConstraints\n          描述: 网络限制", "SchedulingOptions": "描述: 调度选项结构体\n      字段:\n        - 名称: randomizeExecutionTime\n          类型: bool\n          描述: 是否随机化执行时间\n        - 名称: randomWindow\n          类型: uint32_t\n          描述: 随机化窗口大小（秒）\n        - 名称: prioritizeStealth\n          类型: bool\n          描述: 是否优先考虑隐蔽性\n        - 名称: backupMissedSchedules\n          类型: bool\n          描述: 是否备份错过的计划\n        - 名称: maxRetries\n          类型: uint32_t\n          描述: 最大重试次数", "ScheduleResult": "描述: 调度结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 调度是否成功\n        - 名称: taskId\n          类型: std::string\n          描述: 任务ID\n        - 名称: nextExecutionTime\n          类型: time_t\n          描述: 下次执行时间\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息", "ScheduledTask": "描述: 已计划任务结构体\n      字段:\n        - 名称: id\n          类型: std::string\n          描述: 任务ID\n        - 名称: name\n          类型: std::string\n          描述: 任务名称\n        - 名称: pattern\n          类型: std::string\n          描述: 调度模式\n        - 名称: command\n          类型: std::string\n          描述: 完整命令\n        - 名称: nextRun\n          类型: time_t\n          描述: 下次运行时间", "TaskStatus": "描述: 任务状态结构体\n      字段:\n        - 名称: isActive\n          类型: bool\n          描述: 任务是否活动\n        - 名称: lastExecutionTime\n          类型: time_t\n          描述: 上次执行时间\n        - 名称: lastExecutionResult\n          类型: int\n          描述: 上次执行结果代码\n        - 名称: executionCount\n          类型: uint32_t\n          描述: 执行次数\n        - 名称: nextScheduledTime\n          类型: time_t\n          描述: 下次计划执行时间"}}