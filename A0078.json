{"示例代码": {"时间差反调试示例": "int main() {\n    // 初始化时间差反调试模块\n    TimeBasedAntiDebug antiDebug;\n    \n    // 配置时间检测参数\n    TimingConfig config;\n    config.enableRdtscCheck = true;\n    config.enableGetTickCountCheck = true;\n    config.enableTimeOfDayCheck = true;\n    config.thresholdMicroseconds = 1000; // 检测阈值\n    config.samplingFrequencyHz = 100;\n    \n    // 初始化时间检测机制\n    if (!antiDebug.Initialize(config)) {\n        fprintf(stderr, \"无法初始化时间差检测机制\\n\");\n        return 1;\n    }\n    \n    printf(\"时间差反调试机制已启动\\n\");\n    \n    // 注册检测回调函数\n    antiDebug.RegisterDetectionCallback([](TimingAnomalyInfo* info) {\n        printf(\"检测到时间异常！\\n\");\n        printf(\"  检测方法: %s\\n\", info->detectionMethod);\n        printf(\"  预期时间: %llu\\n\", info->expectedTime);\n        printf(\"  实际时间: %llu\\n\", info->actualTime);\n        printf(\"  时间差异: %lld\\n\", info->timeDifference);\n        \n        // 可以在此处实现自定义反应，如混淆代码或自我终止\n    });\n    \n    // 启动持续检测线程\n    antiDebug.StartContinuousMonitoring();\n    \n    // 在关键代码区域前后插入检测点\n    printf(\"即将执行关键代码区域...\\n\");\n    \n    // 创建时间检测点\n    TimeCheckpoint checkpoint = antiDebug.CreateCheckpoint();\n    \n    // 模拟关键代码区域（实际应用中为要保护的核心逻辑）\n    for (int i = 0; i < 1000; i++) {\n        volatile int sum = 0;\n        for (int j = 0; j < 1000; j++) {\n            sum += j;\n        }\n    }\n    \n    // 验证检测点，如果被调试会导致执行时间异常\n    if (!antiDebug.VerifyCheckpoint(checkpoint)) {\n        printf(\"检测到可能的调试器干预！\\n\");\n        // 可以选择终止程序或返回虚假数据\n        antiDebug.ObfuscateExecution();\n        return 1;\n    }\n    \n    printf(\"关键代码区域执行完毕\\n\");\n    \n    // 执行另一个关键操作，使用RDTSC指令直接检测\n    if (antiDebug.DetectDebuggerWithRdtsc()) {\n        printf(\"RDTSC指令检测到调试器！\\n\");\n        return 1;\n    }\n    \n    // 使用GetTickCount检测方法\n    if (antiDebug.DetectDebuggerWithTickCount()) {\n        printf(\"GetTickCount检测到时间异常！\\n\");\n        return 1;\n    }\n    \n    // 停止监控并清理资源\n    antiDebug.StopContinuousMonitoring();\n    antiDebug.Cleanup();\n    \n    printf(\"程序正常完成\\n\");\n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化时间差反调试模块\n      参数:\n        - 名称: config\n          类型: TimingConfig\n          描述: 时间检测配置参数\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: antiDebug.Initialize(config)", "RegisterDetectionCallback": "功能: 注册时间异常检测回调函数\n      参数:\n        - 名称: callback\n          类型: std::function<void(TimingAnomalyInfo*)>\n          描述: 检测到时间异常时调用的回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: antiDebug.RegisterDetectionCallback(callbackFunction)", "StartContinuousMonitoring": "功能: 启动持续时间监控线程\n      参数:\n      返回值:\n        类型: bool\n        描述: 监控线程是否成功启动\n      示例: antiDebug.StartContinuousMonitoring()", "CreateCheckpoint": "功能: 创建时间检测点\n      参数:\n      返回值:\n        类型: TimeCheckpoint\n        描述: 时间检测点标识符\n      示例: TimeCheckpoint checkpoint = antiDebug.CreateCheckpoint()", "VerifyCheckpoint": "功能: 验证时间检测点\n      参数:\n        - 名称: checkpoint\n          类型: TimeCheckpoint\n          描述: 要验证的时间检测点\n      返回值:\n        类型: bool\n        描述: 验证是否通过（true为正常，false表示检测到异常）\n      示例: antiDebug.VerifyCheckpoint(checkpoint)", "DetectDebuggerWithRdtsc": "功能: 使用RDTSC指令检测调试器\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否检测到调试器（true表示检测到）\n      示例: antiDebug.DetectDebuggerWithRdtsc()", "DetectDebuggerWithTickCount": "功能: 使用GetTickCount检测调试器\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否检测到调试器（true表示检测到）\n      示例: antiDebug.DetectDebuggerWithTickCount()", "ObfuscateExecution": "功能: 在检测到调试器时混淆执行流程\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: antiDebug.ObfuscateExecution()", "StopContinuousMonitoring": "功能: 停止持续时间监控\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: antiDebug.StopContinuousMonitoring()"}, "数据结构": {"TimingConfig": "描述: 时间差检测配置\n      字段:\n        - 名称: enableRdtscCheck\n          类型: bool\n          描述: 是否启用RDTSC指令检测\n        - 名称: enableGetTickCountCheck\n          类型: bool\n          描述: 是否启用GetTickCount检测\n        - 名称: enableTimeOfDayCheck\n          类型: bool\n          描述: 是否启用gettimeofday检测\n        - 名称: thresholdMicroseconds\n          类型: uint64_t\n          描述: 时间差异阈值（微秒）\n        - 名称: samplingFrequencyHz\n          类型: uint32_t\n          描述: 采样频率（赫兹）", "TimingAnomalyInfo": "描述: 时间异常信息\n      字段:\n        - 名称: detectionMethod\n          类型: char[32]\n          描述: 检测方法（RDTSC/GetTickCount/TimeOfDay）\n        - 名称: expectedTime\n          类型: uint64_t\n          描述: 预期执行时间\n        - 名称: actualTime\n          类型: uint64_t\n          描述: 实际测量时间\n        - 名称: timeDifference\n          类型: int64_t\n          描述: 时间差异值\n        - 名称: anomalyType\n          类型: uint8_t\n          描述: 异常类型标识", "TimeCheckpoint": "描述: 时间检测点结构\n      字段:\n        - 名称: id\n          类型: uint32_t\n          描述: 检测点ID\n        - 名称: startTime\n          类型: uint64_t\n          描述: 起始时间戳\n        - 名称: expectedDuration\n          类型: uint64_t\n          描述: 预期执行时长\n        - 名称: contextData\n          类型: void*\n          描述: 上下文数据指针"}}