{"示例代码": {"流量加密示例": "// Linux系统中使用C++实现流量加密功能\n#include <openssl/ssl.h>\n#include <openssl/err.h>\n#include <iostream>\n#include <string>\n#include <vector>\n\nclass SecureConnection {\nprivate:\n    SSL_CTX *ctx;\n    SSL *ssl;\n    int socket;\n    \npublic:\n    SecureConnection() {\n        // 初始化OpenSSL\n        SSL_library_init();\n        OpenSSL_add_all_algorithms();\n        SSL_load_error_strings();\n        \n        // 创建SSL上下文\n        ctx = SSL_CTX_new(TLS_client_method());\n        if (!ctx) {\n            ERR_print_errors_fp(stderr);\n            throw std::runtime_error(\"无法创建SSL上下文\");\n        }\n    }\n    \n    bool connect(const std::string& host, int port) {\n        // 创建套接字并连接\n        // ...(省略标准套接字连接代码)\n        \n        // 关联SSL和套接字\n        ssl = SSL_new(ctx);\n        SSL_set_fd(ssl, socket);\n        \n        // 执行SSL握手\n        if (SSL_connect(ssl) != 1) {\n            ERR_print_errors_fp(stderr);\n            return false;\n        }\n        \n        return true;\n    }\n    \n    bool send(const std::vector<uint8_t>& data) {\n        // 发送加密数据\n        int written = SSL_write(ssl, data.data(), data.size());\n        return (written > 0);\n    }\n    \n    std::vector<uint8_t> receive(int max_size) {\n        // 接收并解密数据\n        std::vector<uint8_t> buffer(max_size);\n        int bytes = SSL_read(ssl, buffer.data(), max_size);\n        \n        if (bytes <= 0) {\n            buffer.clear();\n            return buffer;\n        }\n        \n        buffer.resize(bytes);\n        return buffer;\n    }\n    \n    ~SecureConnection() {\n        if (ssl) {\n            SSL_shutdown(ssl);\n            SSL_free(ssl);\n        }\n        if (ctx) {\n            SSL_CTX_free(ctx);\n        }\n    }\n};"}, "API接口": {"InitializeSecureContext": "功能: 初始化安全通信上下文\n      参数:\n        - 名称: cert_path\n          类型: const char*\n          描述: 证书文件路径，可选\n        - 名称: key_path\n          类型: const char*\n          描述: 密钥文件路径，可选\n      返回值:\n        类型: SSL_CTX*\n        描述: SSL上下文指针，失败返回NULL\n      示例: SSL_CTX* ctx = InitializeSecureContext(\"/path/to/cert.pem\", \"/path/to/key.pem\")", "CreateSecureChannel": "功能: 创建加密通信通道\n      参数:\n        - 名称: ctx\n          类型: SSL_CTX*\n          描述: SSL上下文\n        - 名称: socket\n          类型: int\n          描述: 已连接的套接字描述符\n      返回值:\n        类型: SSL*\n        描述: SSL连接指针，失败返回NULL\n      示例: SSL* ssl = CreateSecureChannel(ctx, client_socket)", "SendEncryptedData": "功能: 发送加密数据\n      参数:\n        - 名称: ssl\n          类型: SSL*\n          描述: SSL连接\n        - 名称: data\n          类型: const void*\n          描述: 要发送的数据\n        - 名称: length\n          类型: int\n          描述: 数据长度\n      返回值:\n        类型: int\n        描述: 发送的字节数，失败返回-1\n      示例: SendEncryptedData(ssl, buffer, buffer_size)"}, "数据结构": {"EncryptionConfig": "描述: 加密配置结构体\n      字段:\n        - 名称: cipher\n          类型: char[32]\n          描述: 加密算法名称\n        - 名称: key_bits\n          类型: int\n          描述: 密钥长度(位)\n        - 名称: cert_path\n          类型: char[256]\n          描述: 证书文件路径\n        - 名称: key_path\n          类型: char[256]\n          描述: 私钥文件路径\n        - 名称: verify_peer\n          类型: bool\n          描述: 是否验证对端证书", "SecureSession": "描述: 安全会话结构体\n      字段:\n        - 名称: session_id\n          类型: unsigned char[32]\n          描述: 会话唯一标识符\n        - 名称: cipher_suite\n          类型: char[64]\n          描述: 协商的密码套件\n        - 名称: established_time\n          类型: time_t\n          描述: 会话建立时间\n        - 名称: last_activity\n          类型: time_t\n          描述: 最后活动时间\n        - 名称: bytes_sent\n          类型: uint64_t\n          描述: 已发送字节数\n        - 名称: bytes_received\n          类型: uint64_t\n          描述: 已接收字节数"}}