{"示例代码": {"网络嗅探示例": "int main() {\n    // 创建网络嗅探器\n    NetworkSniffer sniffer;\n    \n    // 配置嗅探选项\n    SnifferConfig config;\n    \n    // 设置网络接口\n    config.interface = \"eth0\";\n    config.promiscuousMode = true;\n    \n    // 设置过滤规则\n    config.captureFilter = \"tcp port 80 or tcp port 443\";\n    \n    // 设置捕获选项\n    config.maxPacketSize = 65535;\n    config.capturePayload = true;\n    config.timeout = 500;  // 毫秒\n    \n    // 设置监听协议\n    ProtocolSettings protocols;\n    protocols.enableHTTP = true;\n    protocols.enableHTTPS = true;\n    protocols.enableFTP = true;\n    protocols.enableTelnet = true;\n    protocols.enableSMTP = true;\n    \n    config.protocols = protocols;\n    \n    // 设置数据提取器\n    DataExtractors extractors;\n    extractors.extractPasswords = true;\n    extractors.extractCookies = true;\n    extractors.extractEmails = true;\n    extractors.extractCreditCards = true;\n    \n    config.extractors = extractors;\n    \n    // 初始化嗅探器\n    if (!sniffer.initialize(config)) {\n        printf(\"Failed to initialize sniffer: %s\\n\", sniffer.getLastError().c_str());\n        return 1;\n    }\n    \n    printf(\"Sniffer initialized on interface %s\\n\", config.interface.c_str());\n    printf(\"Filter: %s\\n\", config.captureFilter.c_str());\n    \n    // 定义回调函数处理捕获的数据包\n    sniffer.setPacketCallback([](const PacketData& packet) {\n        printf(\"Captured packet - Time: %s, Size: %d bytes\\n\", \n               packet.timestamp.c_str(), packet.size);\n               \n        if (packet.protocol == PROTOCOL_HTTP) {\n            printf(\"HTTP: %s %s\\n\", packet.httpMethod.c_str(), \n                   packet.httpUrl.c_str());\n        }\n        \n        if (packet.credentials.size() > 0) {\n            for (const auto& cred : packet.credentials) {\n                printf(\"Credential found - Service: %s, Username: %s, Password: %s\\n\",\n                       cred.service.c_str(), cred.username.c_str(), \n                       cred.password.c_str());\n            }\n        }\n    });\n    \n    // 开始嗅探\n    if (!sniffer.startCapture()) {\n        printf(\"Failed to start capture: %s\\n\", sniffer.getLastError().c_str());\n        return 1;\n    }\n    \n    printf(\"Capture started. Press Ctrl+C to stop...\\n\");\n    \n    // 等待嗅探结果（实际应用中可能使用不同的机制）\n    sleep(60);\n    \n    // 停止嗅探\n    sniffer.stopCapture();\n    \n    // 获取统计数据\n    SnifferStatistics stats = sniffer.getStatistics();\n    printf(\"\\nCapture Statistics:\\n\");\n    printf(\"Packets captured: %d\\n\", stats.packetsTotal);\n    printf(\"Data captured: %zu bytes\\n\", stats.bytesTotal);\n    printf(\"HTTP requests: %d\\n\", stats.httpRequests);\n    printf(\"Credentials found: %d\\n\", stats.credentialsFound);\n    \n    // 保存捕获的数据\n    sniffer.saveCapture(\"/tmp/capture.pcap\");\n    \n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化网络嗅探器\n      参数:\n        - 名称: config\n          类型: SnifferConfig\n          描述: 嗅探器配置\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: bool success = sniffer.initialize(config);", "startCapture": "功能: 开始数据包捕获\n      参数:\n      返回值:\n        类型: bool\n        描述: 捕获是否成功启动\n      示例: bool started = sniffer.startCapture();", "stopCapture": "功能: 停止数据包捕获\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: sniffer.stopCapture();", "setPacketCallback": "功能: 设置数据包处理回调函数\n      参数:\n        - 名称: callback\n          类型: std::function<void(const PacketData&)>\n          描述: 回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: sniffer.setPacketCallback([](const PacketData& packet) { /* process packet */ });", "getStatistics": "功能: 获取嗅探统计信息\n      参数:\n      返回值:\n        类型: SnifferStatistics\n        描述: 嗅探统计数据\n      示例: SnifferStatistics stats = sniffer.getStatistics();", "saveCapture": "功能: 保存捕获的数据\n      参数:\n        - 名称: filename\n          类型: const std::string&\n          描述: 保存文件名\n      返回值:\n        类型: bool\n        描述: 保存是否成功\n      示例: bool saved = sniffer.saveCapture(\"/tmp/capture.pcap\");"}, "数据结构": {"SnifferConfig": "描述: 嗅探器配置结构体\n      字段:\n        - 名称: interface\n          类型: std::string\n          描述: 网络接口名称\n        - 名称: promiscuousMode\n          类型: bool\n          描述: 是否启用混杂模式\n        - 名称: captureFilter\n          类型: std::string\n          描述: 数据包过滤表达式\n        - 名称: maxPacketSize\n          类型: uint32_t\n          描述: 最大数据包大小\n        - 名称: capturePayload\n          类型: bool\n          描述: 是否捕获数据负载\n        - 名称: timeout\n          类型: int\n          描述: 捕获超时时间（毫秒）\n        - 名称: protocols\n          类型: ProtocolSettings\n          描述: 协议设置\n        - 名称: extractors\n          类型: DataExtractors\n          描述: 数据提取器设置", "ProtocolSettings": "描述: 协议设置结构体\n      字段:\n        - 名称: enableHTTP\n          类型: bool\n          描述: 是否启用HTTP协议解析\n        - 名称: enableHTTPS\n          类型: bool\n          描述: 是否启用HTTPS协议解析\n        - 名称: enableFTP\n          类型: bool\n          描述: 是否启用FTP协议解析\n        - 名称: enableTelnet\n          类型: bool\n          描述: 是否启用Telnet协议解析\n        - 名称: enableSMTP\n          类型: bool\n          描述: 是否启用SMTP协议解析", "DataExtractors": "描述: 数据提取器设置结构体\n      字段:\n        - 名称: extractPasswords\n          类型: bool\n          描述: 是否提取密码\n        - 名称: extractCookies\n          类型: bool\n          描述: 是否提取Cookie\n        - 名称: extractEmails\n          类型: bool\n          描述: 是否提取邮箱地址\n        - 名称: extractCreditCards\n          类型: bool\n          描述: 是否提取信用卡号", "PacketData": "描述: 捕获的数据包结构体\n      字段:\n        - 名称: timestamp\n          类型: std::string\n          描述: 捕获时间戳\n        - 名称: size\n          类型: int\n          描述: 数据包大小\n        - 名称: protocol\n          类型: ProtocolType\n          描述: 协议类型\n        - 名称: sourceIP\n          类型: std::string\n          描述: 源IP地址\n        - 名称: destIP\n          类型: std::string\n          描述: 目标IP地址\n        - 名称: sourcePort\n          类型: uint16_t\n          描述: 源端口\n        - 名称: destPort\n          类型: uint16_t\n          描述: 目标端口\n        - 名称: httpMethod\n          类型: std::string\n          描述: HTTP方法\n        - 名称: httpUrl\n          类型: std::string\n          描述: HTTP URL\n        - 名称: credentials\n          类型: std::vector<Credential>\n          描述: 提取的凭证列表\n        - 名称: payload\n          类型: std::vector<uint8_t>\n          描述: 数据包负载", "ProtocolType": "描述: 协议类型枚举\n      字段:\n        - 名称: PROTOCOL_TCP\n          类型: enum\n          描述: TCP协议\n        - 名称: PROTOCOL_UDP\n          类型: enum\n          描述: UDP协议\n        - 名称: PROTOCOL_HTTP\n          类型: enum\n          描述: HTTP协议\n        - 名称: PROTOCOL_HTTPS\n          类型: enum\n          描述: HTTPS协议\n        - 名称: PROTOCOL_FTP\n          类型: enum\n          描述: FTP协议", "Credential": "描述: 凭证结构体\n      字段:\n        - 名称: service\n          类型: std::string\n          描述: 服务名称\n        - 名称: username\n          类型: std::string\n          描述: 用户名\n        - 名称: password\n          类型: std::string\n          描述: 密码", "SnifferStatistics": "描述: 嗅探统计信息结构体\n      字段:\n        - 名称: packetsTotal\n          类型: uint32_t\n          描述: 捕获的数据包总数\n        - 名称: bytesTotal\n          类型: size_t\n          描述: 捕获的总字节数\n        - 名称: httpRequests\n          类型: uint32_t\n          描述: 捕获的HTTP请求数\n        - 名称: credentialsFound\n          类型: uint32_t\n          描述: 发现的凭证数量"}}