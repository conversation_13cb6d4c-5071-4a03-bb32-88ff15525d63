{"示例代码": {"获取系统信息示例": "int main() {\n    // 初始化系统信息收集器\n    SystemInfoCollector collector;\n    \n    // 创建系统信息结构体\n    SYSTEM_INFO_DATA sysInfo;\n    memset(&sysInfo, 0, sizeof(SYSTEM_INFO_DATA));\n    \n    // 收集系统信息\n    if (collector.GatherSystemInfo(&sysInfo)) {\n        printf(\"系统信息收集成功\\n\");\n        \n        // 打印收集到的系统信息\n        printf(\"操作系统: %s\\n\", sysInfo.osVersion);\n        printf(\"内核版本: %s\\n\", sysInfo.kernelVersion);\n        printf(\"CPU型号: %s\\n\", sysInfo.cpuModel);\n        printf(\"CPU核心数: %d\\n\", sysInfo.cpuCores);\n        printf(\"总内存: %lu MB\\n\", sysInfo.totalMemory / (1024*1024));\n        printf(\"主机名: %s\\n\", sysInfo.hostname);\n        \n        // 将系统信息编码为JSON格式\n        char* jsonData = collector.EncodeSystemInfoToJson(&sysInfo);\n        if (jsonData) {\n            printf(\"JSON数据: %s\\n\", jsonData);\n            free(jsonData);\n        }\n    } else {\n        printf(\"系统信息收集失败\\n\");\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"GatherSystemInfo": "功能: 收集系统硬件和操作系统信息\n      参数:\n        - 名称: sysInfo\n          类型: SYSTEM_INFO_DATA*\n          描述: 用于存储收集到的系统信息的结构体指针\n      返回值:\n        类型: bool\n        描述: 信息收集是否成功\n      示例: collector.GatherSystemInfo(&sysInfo)", "GetCpuInfo": "功能: 获取CPU相关信息\n      参数:\n        - 名称: cpuInfo\n          类型: CPU_INFO*\n          描述: CPU信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取CPU信息\n      示例: collector.GetCpuInfo(&sysInfo.cpuInfo)", "GetMemoryInfo": "功能: 获取内存相关信息\n      参数:\n        - 名称: memInfo\n          类型: MEMORY_INFO*\n          描述: 内存信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取内存信息\n      示例: collector.GetMemoryInfo(&sysInfo.memInfo)", "EncodeSystemInfoToJson": "功能: 将系统信息编码为JSON格式\n      参数:\n        - 名称: sysInfo\n          类型: SYSTEM_INFO_DATA*\n          描述: 系统信息结构体指针\n      返回值:\n        类型: char*\n        描述: JSON格式的系统信息，使用完需要调用free释放\n      示例: char* jsonData = collector.EncodeSystemInfoToJson(&sysInfo);"}, "数据结构": {"SYSTEM_INFO_DATA": "描述: 系统设备信息结构体\n      字段:\n        - 名称: osVersion\n          类型: char[256]\n          描述: 操作系统版本信息\n        - 名称: kernelVersion\n          类型: char[128]\n          描述: 内核版本信息\n        - 名称: hostname\n          类型: char[64]\n          描述: 主机名\n        - 名称: cpuModel\n          类型: char[256]\n          描述: CPU型号信息\n        - 名称: cpuCores\n          类型: int\n          描述: CPU核心数\n        - 名称: cpuFrequency\n          类型: uint32_t\n          描述: CPU频率(MHz)\n        - 名称: totalMemory\n          类型: uint64_t\n          描述: 系统总内存(字节)\n        - 名称: availableMemory\n          类型: uint64_t\n          描述: 可用内存(字节)\n        - 名称: networkInterfaces\n          类型: NETWORK_INTERFACE[8]\n          描述: 网络接口信息\n        - 名称: networkInterfaceCount\n          类型: int\n          描述: 网络接口数量", "NETWORK_INTERFACE": "描述: 网络接口信息结构\n      字段:\n        - 名称: name\n          类型: char[32]\n          描述: 网络接口名称\n        - 名称: ipAddress\n          类型: char[16]\n          描述: IP地址\n        - 名称: macAddress\n          类型: char[18]\n          描述: MAC地址"}}