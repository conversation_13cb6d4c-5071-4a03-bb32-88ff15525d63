{"示例代码": {"生成载荷示例": "// 初始化载荷生成器\nPayloadGenerator generator;\n\n// 配置载荷参数\nPayloadConfig config;\nconfig.SetTargetIP(\"*************\");\nconfig.SetPort(4444);\nconfig.SetEncryption(true);\n\n// 生成载荷\nBinary* payload = generator.Generate(config);\n\n// 保存载荷到文件\nif (payload->SaveToFile(\"/tmp/payload.bin\")) {\n    printf(\"载荷生成成功，已保存到/tmp/payload.bin\\n\");\n} else {\n    printf(\"载荷生成失败\\n\");\n}"}, "API接口": {"Generate": "功能: 根据配置参数生成载荷\n      参数:\n        - 名称: config\n          类型: PayloadConfig&\n          描述: 载荷配置参数\n      返回值:\n        类型: Binary*\n        描述: 生成的载荷二进制数据\n      示例: Binary* payload = generator.Generate(config)", "SaveToFile": "功能: 将生成的载荷保存到指定文件\n      参数:\n        - 名称: filePath\n          类型: const char*\n          描述: 目标文件路径\n      返回值:\n        类型: bool\n        描述: 是否成功保存文件\n      示例: payload->SaveToFile(\"/tmp/payload.bin\")"}, "数据结构": {"Binary": "描述: 载荷二进制数据结构体\n      字段:\n        - 名称: data\n          类型: unsigned char*\n          描述: 二进制数据指针\n        - 名称: size\n          类型: size_t\n          描述: 数据大小\n        - 名称: type\n          类型: int\n          描述: 载荷类型", "PayloadGenerator": "描述: 载荷生成器类\n      字段:\n        - 名称: engineVersion\n          类型: char[32]\n          描述: 生成引擎版本\n        - 名称: supportedTypes\n          类型: int[]\n          描述: 支持的载荷类型列表"}}