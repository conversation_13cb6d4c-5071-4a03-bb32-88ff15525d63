{"示例代码": {"使用UDP扫描示例": "// Linux系统中使用C语言实现UDP端口扫描\n#include <stdio.h>\n#include <stdlib.h>\n#include <string.h>\n#include <unistd.h>\n#include <errno.h>\n#include <sys/socket.h>\n#include <netinet/in.h>\n#include <arpa/inet.h>\n#include <fcntl.h>\n#include <netdb.h>\n#include <sys/time.h>\n#include <poll.h>\n\n#define MAX_PORT 65535\n#define MIN_PORT 1\n#define DEFAULT_TIMEOUT_MS 1000\n#define BUFFER_SIZE 512\n\n// UDP端口扫描函数\nint scan_udp_port(const char *target_ip, int port, int timeout_ms) {\n    int sock;\n    struct sockaddr_in target_addr, local_addr;\n    socklen_t addr_len = sizeof(struct sockaddr_in);\n    char buffer[BUFFER_SIZE];\n    struct pollfd fds;\n    int res;\n    \n    // 创建UDP套接字\n    sock = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);\n    if (sock < 0) {\n        perror(\"socket创建失败\");\n        return -1;\n    }\n    \n    // 绑定本地地址(可选)\n    memset(&local_addr, 0, sizeof(local_addr));\n    local_addr.sin_family = AF_INET;\n    local_addr.sin_port = 0;  // 自动分配端口\n    local_addr.sin_addr.s_addr = htonl(INADDR_ANY);\n    \n    if (bind(sock, (struct sockaddr *)&local_addr, sizeof(local_addr)) < 0) {\n        perror(\"bind失败\");\n        close(sock);\n        return -1;\n    }\n    \n    // 设置接收超时\n    struct timeval tv;\n    tv.tv_sec = timeout_ms / 1000;\n    tv.tv_usec = (timeout_ms % 1000) * 1000;\n    setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));\n    \n    // 准备目标地址\n    memset(&target_addr, 0, sizeof(target_addr));\n    target_addr.sin_family = AF_INET;\n    target_addr.sin_port = htons(port);\n    target_addr.sin_addr.s_addr = inet_addr(target_ip);\n    \n    // 为不同的常见服务准备适当的探测数据包\n    char *probe_data = NULL;\n    int probe_len = 0;\n    \n    // 根据端口选择探测数据包\n    switch (port) {\n        case 53:  // DNS\n            probe_data = \"\\x00\\x00\\x01\\x00\\x00\\x01\\x00\\x00\\x00\\x00\\x00\\x00\\x03www\\x06google\\x03com\\x00\\x00\\x01\\x00\\x01\";\n            probe_len = 29;\n            break;\n        case 161:  // SNMP\n            probe_data = \"\\x30\\x26\\x02\\x01\\x01\\x04\\x06public\\xa1\\x19\\x02\\x04\\x71\\xb4\\xb5\\x68\\x02\\x01\\x00\\x02\\x01\\x00\\x30\\x0b\\x30\\x09\\x06\\x05\\x2b\\x06\\x01\\x02\\x01\\x05\\x00\";\n            probe_len = 33;\n            break;\n        default:  // 通用探测数据包\n            probe_data = \"\\x00\\x01\\x02\\x03\\x04\\x05\\x06\\x07\\x08\\x09\";\n            probe_len = 10;\n            break;\n    }\n    \n    // 发送探测数据包\n    if (sendto(sock, probe_data, probe_len, 0, \n               (struct sockaddr *)&target_addr, sizeof(target_addr)) < 0) {\n        perror(\"sendto失败\");\n        close(sock);\n        return -1;\n    }\n    \n    // 使用poll等待可能的响应\n    fds.fd = sock;\n    fds.events = POLLIN;\n    res = poll(&fds, 1, timeout_ms);\n    \n    if (res > 0) {\n        // 有数据可读，端口可能开放\n        if (recvfrom(sock, buffer, BUFFER_SIZE, 0, \n                    (struct sockaddr *)&target_addr, &addr_len) > 0) {\n            close(sock);\n            return 0;  // 端口开放\n        }\n    } else if (res == 0) {\n        // 超时，尝试判断端口状态\n        // 对于UDP，没有响应可能表示端口开放或被过滤\n        // 这里我们保守地认为它是关闭的\n        close(sock);\n        return -1;\n    } else {\n        // 错误\n        perror(\"poll错误\");\n        close(sock);\n        return -2;\n    }\n    \n    close(sock);\n    return -1;\n}\n\n// 扫描UDP端口范围\nvoid scan_udp_port_range(const char *target_ip, int start_port, int end_port) {\n    printf(\"开始扫描目标 %s 的UDP端口范围 %d-%d...\\n\", target_ip, start_port, end_port);\n    \n    for (int port = start_port; port <= end_port; port++) {\n        int result = scan_udp_port(target_ip, port, DEFAULT_TIMEOUT_MS);\n        \n        if (result == 0) {\n            printf(\"[+] UDP端口 %d 可能开放\\n\", port);\n            \n            // 尝试识别服务\n            struct servent *service = getservbyport(htons(port), \"udp\");\n            if (service) {\n                printf(\"    服务: %s\\n\", service->s_name);\n            }\n        }\n    }\n    \n    printf(\"UDP扫描完成\\n\");\n}\n\nint main(int argc, char *argv[]) {\n    if (argc < 2) {\n        printf(\"用法: %s <目标IP> [起始端口] [结束端口]\\n\", argv[0]);\n        return 1;\n    }\n    \n    const char *target_ip = argv[1];\n    int start_port = (argc > 2) ? atoi(argv[2]) : 1;\n    int end_port = (argc > 3) ? atoi(argv[3]) : 1024;\n    \n    // 校验端口范围\n    if (start_port < MIN_PORT || start_port > MAX_PORT ||\n        end_port < MIN_PORT || end_port > MAX_PORT ||\n        start_port > end_port) {\n        printf(\"无效的端口范围. 使用范围: %d-%d\\n\", MIN_PORT, MAX_PORT);\n        return 1;\n    }\n    \n    scan_udp_port_range(target_ip, start_port, end_port);\n    return 0;\n}"}, "API接口": {"ScanUDPPort": "功能: 扫描单个UDP端口状态\n      参数:\n        - 名称: target_ip\n          类型: const char*\n          描述: 目标IP地址\n        - 名称: port\n          类型: int\n          描述: 要扫描的端口号\n        - 名称: timeout_ms\n          类型: int\n          描述: 等待响应超时时间(毫秒)\n      返回值:\n        类型: int\n        描述: 0=端口可能开放, -1=端口可能关闭, -2=扫描出错\n      示例: int result = ScanUDPPort(\"***********\", 53, 1000)", "ScanUDPPortRange": "功能: 扫描连续的UDP端口范围\n      参数:\n        - 名称: target_ip\n          类型: const char*\n          描述: 目标IP地址\n        - 名称: start_port\n          类型: int\n          描述: 起始端口\n        - 名称: end_port\n          类型: int\n          描述: 结束端口\n        - 名称: timeout_ms\n          类型: int\n          描述: 每个端口等待响应超时时间(毫秒)\n      返回值:\n        类型: UDPPortScanResult*\n        描述: UDP扫描结果结构体\n      示例: UDPPortScanResult* results = ScanUDPPortRange(\"***********\", 1, 1024, 1000)", "GenerateUDPProbe": "功能: 生成特定服务的UDP探测包\n      参数:\n        - 名称: service_type\n          类型: int\n          描述: 服务类型：0=通用, 1=DNS, 2=SNMP, 3=NTP, 4=TFTP\n        - 名称: buffer\n          类型: unsigned char*\n          描述: 输出缓冲区\n        - 名称: buffer_size\n          类型: int\n          描述: 缓冲区大小\n      返回值:\n        类型: int\n        描述: 生成的探测包大小，失败返回-1\n      示例: unsigned char probe[512]; int size = GenerateUDPProbe(1, probe, 512)"}, "数据结构": {"UDPPortStatus": "描述: UDP端口状态结构体\n      字段:\n        - 名称: port\n          类型: int\n          描述: 端口号\n        - 名称: state\n          类型: int\n          描述: 端口状态：0=开放, 1=可能开放, 2=关闭, 3=被过滤\n        - 名称: service_name\n          类型: char[32]\n          描述: 识别到的服务名称，如未识别则为空\n        - 名称: response_data\n          类型: unsigned char[512]\n          描述: 接收到的响应数据\n        - 名称: response_len\n          类型: int\n          描述: 响应数据长度", "UDPPortScanResult": "描述: UDP端口扫描结果结构体\n      字段:\n        - 名称: target_ip\n          类型: char[64]\n          描述: 目标IP地址\n        - 名称: scan_time\n          类型: time_t\n          描述: 扫描时间\n        - 名称: total_ports\n          类型: int\n          描述: 扫描的总端口数\n        - 名称: open_ports\n          类型: int\n          描述: 可能开放的端口数量\n        - 名称: filtered_ports\n          类型: int\n          描述: 被过滤的端口数量\n        - 名称: port_status\n          类型: UDPPortStatus[]\n          描述: 端口状态数组", "UDPScanOptions": "描述: UDP扫描选项结构体\n      字段:\n        - 名称: timeout_ms\n          类型: int\n          描述: 等待响应超时(毫秒)\n        - 名称: retry_count\n          类型: int\n          描述: 重试次数\n        - 名称: wait_between_probes_ms\n          类型: int\n          描述: 两次探测之间的等待时间(毫秒)\n        - 名称: custom_probes\n          类型: bool\n          描述: 是否使用自定义探测包\n        - 名称: service_detection\n          类型: bool\n          描述: 是否进行服务检测"}}