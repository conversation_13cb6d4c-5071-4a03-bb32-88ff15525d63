{"示例代码": {"硬件特征检测示例": "int main() {\n    // 初始化硬件特征检测模块\n    HardwareFeatureDetector detector;\n    \n    // 配置硬件特征检测参数\n    HardwareDetectionConfig config;\n    config.enableCpuIdCheck = true;\n    config.enableTscCheck = true;\n    config.enableMemoryCheck = true;\n    config.enablePeripheralCheck = true;\n    config.enableVirtualizationDetection = true;\n    \n    // 初始化检测器\n    if (!detector.Initialize(config)) {\n        fprintf(stderr, \"无法初始化硬件特征检测模块\\n\");\n        return 1;\n    }\n    \n    printf(\"硬件特征检测模块已启动\\n\");\n    \n    // 执行CPU ID特征检测\n    printf(\"\\n正在检测CPU特征...\\n\");\n    CpuFeatures cpuFeatures = detector.DetectCpuFeatures();\n    \n    printf(\"CPU信息:\\n\");\n    printf(\"  - 制造商: %s\\n\", cpuFeatures.vendor);\n    printf(\"  - 型号名称: %s\\n\", cpuFeatures.modelName);\n    printf(\"  - 核心数: %d\\n\", cpuFeatures.cores);\n    printf(\"  - 支持指令集: \");\n    if (cpuFeatures.hasMMX) printf(\"MMX \");\n    if (cpuFeatures.hasSSE) printf(\"SSE \");\n    if (cpuFeatures.hasSSE2) printf(\"SSE2 \");\n    if (cpuFeatures.hasSSE3) printf(\"SSE3 \");\n    if (cpuFeatures.hasAVX) printf(\"AVX \");\n    if (cpuFeatures.hasAVX2) printf(\"AVX2 \");\n    printf(\"\\n\");\n    \n    // 执行时间戳计数器(TSC)检测\n    printf(\"\\n正在进行TSC一致性检测...\\n\");\n    TscCheckResult tscResult = detector.PerformTscConsistencyCheck();\n    \n    if (tscResult.isConsistent) {\n        printf(\"TSC一致性检测正常\\n\");\n    } else {\n        printf(\"检测到TSC异常，可能存在虚拟化环境\\n\");\n        printf(\"  - 差异系数: %.6f\\n\", tscResult.variationCoefficient);\n    }\n    \n    // 检测内存特征\n    printf(\"\\n正在检测内存特征...\\n\");\n    MemoryCharacteristics memInfo = detector.DetectMemoryCharacteristics();\n    \n    printf(\"内存特征:\\n\");\n    printf(\"  - 总物理内存: %u MB\\n\", memInfo.totalPhysicalMemoryMB);\n    printf(\"  - 可用物理内存: %u MB\\n\", memInfo.availablePhysicalMemoryMB);\n    printf(\"  - 内存访问延迟: %.2f ns\\n\", memInfo.accessLatencyNs);\n    printf(\"  - 内存分配模式: %s\\n\", \n           memInfo.isNormalAllocationPattern ? \"正常\" : \"异常(可能是虚拟环境)\");\n    \n    // 检测外围设备特征\n    printf(\"\\n正在检测外围设备特征...\\n\");\n    PeripheralDeviceInfo deviceInfo = detector.DetectPeripheralDevices();\n    \n    printf(\"设备信息:\\n\");\n    printf(\"  - 检测到的设备数量: %d\\n\", deviceInfo.deviceCount);\n    printf(\"  - 硬盘模型: %s\\n\", deviceInfo.diskModel);\n    printf(\"  - 网卡MAC地址: %s\\n\", deviceInfo.macAddress);\n    printf(\"  - 设备特征分数: %.2f/10\\n\", deviceInfo.deviceScore);\n    \n    // 检测虚拟化环境\n    printf(\"\\n检测虚拟化环境...\\n\");\n    VirtualizationInfo virtInfo = detector.CheckVirtualization();\n    \n    if (virtInfo.isVirtualized) {\n        printf(\"检测到虚拟化环境!\\n\");\n        printf(\"  - 虚拟化类型: %s\\n\", virtInfo.virtualizationType);\n        printf(\"  - 检测方法: %s\\n\", virtInfo.detectionMethod);\n        printf(\"  - 可信度: %.2f%%\\n\", virtInfo.confidenceLevel * 100);\n    } else {\n        printf(\"未检测到虚拟化环境，可能是物理机\\n\");\n    }\n    \n    // 执行综合硬件环境检测\n    printf(\"\\n执行综合硬件环境检测...\\n\");\n    HardwareEnvironmentResult envResult = detector.AnalyzeHardwareEnvironment();\n    \n    printf(\"硬件环境分析结果:\\n\");\n    printf(\"  - 环境类型: %s\\n\", envResult.environmentType);\n    printf(\"  - 安全性评分: %.2f/10\\n\", envResult.securityScore);\n    printf(\"  - 硬件特征评价: %s\\n\", envResult.evaluationSummary);\n    \n    // 清理资源\n    detector.Cleanup();\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化硬件特征检测模块\n      参数:\n        - 名称: config\n          类型: HardwareDetectionConfig\n          描述: 硬件检测配置参数\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: detector.Initialize(config)", "DetectCpuFeatures": "功能: 检测CPU特征\n      参数:\n      返回值:\n        类型: CpuFeatures\n        描述: CPU特征信息\n      示例: cpuFeatures = detector.DetectCpuFeatures()", "PerformTscConsistencyCheck": "功能: 执行时间戳计数器(TSC)一致性检测\n      参数:\n      返回值:\n        类型: TscCheckResult\n        描述: TSC检查结果\n      示例: tscResult = detector.PerformTscConsistencyCheck()", "DetectMemoryCharacteristics": "功能: 检测内存特征\n      参数:\n      返回值:\n        类型: MemoryCharacteristics\n        描述: 内存特征信息\n      示例: memInfo = detector.DetectMemoryCharacteristics()", "DetectPeripheralDevices": "功能: 检测外围设备特征\n      参数:\n      返回值:\n        类型: PeripheralDeviceInfo\n        描述: 外围设备信息\n      示例: deviceInfo = detector.DetectPeripheralDevices()", "CheckVirtualization": "功能: 检测虚拟化环境\n      参数:\n      返回值:\n        类型: VirtualizationInfo\n        描述: 虚拟化环境信息\n      示例: virtInfo = detector.CheckVirtualization()", "AnalyzeHardwareEnvironment": "功能: 分析综合硬件环境\n      参数:\n      返回值:\n        类型: HardwareEnvironmentResult\n        描述: 硬件环境分析结果\n      示例: envResult = detector.AnalyzeHardwareEnvironment()", "Cleanup": "功能: 清理资源\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: detector.Cleanup()"}, "数据结构": {"HardwareDetectionConfig": "描述: 硬件特征检测配置\n      字段:\n        - 名称: enableCpuIdCheck\n          类型: bool\n          描述: 是否启用CPU ID检测\n        - 名称: enableTscCheck\n          类型: bool\n          描述: 是否启用时间戳计数器检测\n        - 名称: enableMemoryCheck\n          类型: bool\n          描述: 是否启用内存特征检测\n        - 名称: enablePeripheralCheck\n          类型: bool\n          描述: 是否启用外围设备检测\n        - 名称: enableVirtualizationDetection\n          类型: bool\n          描述: 是否启用虚拟化环境检测", "CpuFeatures": "描述: CPU特征信息\n      字段:\n        - 名称: vendor\n          类型: char[32]\n          描述: CPU制造商\n        - 名称: modelName\n          类型: char[64]\n          描述: CPU型号名称\n        - 名称: cores\n          类型: int\n          描述: CPU核心数\n        - 名称: hasMMX\n          类型: bool\n          描述: 是否支持MMX指令集\n        - 名称: hasSSE\n          类型: bool\n          描述: 是否支持SSE指令集\n        - 名称: hasSSE2\n          类型: bool\n          描述: 是否支持SSE2指令集\n        - 名称: hasSSE3\n          类型: bool\n          描述: 是否支持SSE3指令集\n        - 名称: hasAVX\n          类型: bool\n          描述: 是否支持AVX指令集\n        - 名称: hasAVX2\n          类型: bool\n          描述: 是否支持AVX2指令集", "TscCheckResult": "描述: 时间戳计数器检查结果\n      字段:\n        - 名称: isConsistent\n          类型: bool\n          描述: TSC是否一致\n        - 名称: variationCoefficient\n          类型: double\n          描述: 变异系数\n        - 名称: suspicionScore\n          类型: float\n          描述: 可疑程度评分(0-1)", "MemoryCharacteristics": "描述: 内存特征信息\n      字段:\n        - 名称: totalPhysicalMemoryMB\n          类型: uint32_t\n          描述: 总物理内存(MB)\n        - 名称: availablePhysicalMemoryMB\n          类型: uint32_t\n          描述: 可用物理内存(MB)\n        - 名称: accessLatencyNs\n          类型: double\n          描述: 内存访问延迟(纳秒)\n        - 名称: isNormalAllocationPattern\n          类型: bool\n          描述: 是否为正常的内存分配模式", "PeripheralDeviceInfo": "描述: 外围设备信息\n      字段:\n        - 名称: deviceCount\n          类型: int\n          描述: 检测到的设备数量\n        - 名称: diskModel\n          类型: char[64]\n          描述: 硬盘模型\n        - 名称: macAddress\n          类型: char[18]\n          描述: 网卡MAC地址\n        - 名称: deviceScore\n          类型: float\n          描述: 设备特征分数(0-10)", "VirtualizationInfo": "描述: 虚拟化环境信息\n      字段:\n        - 名称: isVirtualized\n          类型: bool\n          描述: 是否为虚拟化环境\n        - 名称: virtualizationType\n          类型: char[32]\n          描述: 虚拟化类型\n        - 名称: detectionMethod\n          类型: char[64]\n          描述: 检测方法\n        - 名称: confidenceLevel\n          类型: float\n          描述: 可信度(0-1)", "HardwareEnvironmentResult": "描述: 硬件环境分析结果\n      字段:\n        - 名称: environmentType\n          类型: char[32]\n          描述: 环境类型\n        - 名称: securityScore\n          类型: float\n          描述: 安全性评分(0-10)\n        - 名称: evaluationSummary\n          类型: char[128]\n          描述: 评价摘要"}}