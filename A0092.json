{"示例代码": {"说明": "暂无"}, "API接口": {"GetSystemSecurityRules": "功能: 提取指定类型的系统安全规则配置。\n      参数:\n        - 名称: rule_type\n          类型: SecurityRuleType\n          描述: 要获取的安全规则类型 (例如防火墙规则、ACL规则)。\n        - 名称: rules_out\n          类型: SecurityRuleInfo**\n          描述: 用于接收安全规则列表的输出参数，需调用方释放内存。\n        - 名称: count_out\n          类型: int*\n          描述: 用于接收安全规则数量的输出参数。\n      返回值:\n        类型: int\n        描述: 操作状态，0表示成功，非0表示失败或错误码。\n      示例: SecurityRuleInfo* rules = NULL; int rule_count = 0; if (GetSystemSecurityRules(FIREWALL_RULES, &rules, &rule_count) == 0) { /* 处理规则列表 */ }"}, "数据结构": {"SecurityRuleType": "描述: 枚举类型，定义安全规则的种类。\n      字段:", "SecurityRuleInfo": "描述: 存储单条安全规则的详细信息。\n      字段:\n        - 名称: id\n          类型: char[64]\n          描述: 规则的唯一标识符。\n        - 名称: description\n          类型: char[256]\n          描述: 规则的文本描述。\n        - 名称: is_enabled\n          类型: bool\n          描述: 规则是否启用。\n        - 名称: details\n          类型: char[1024]\n          描述: 规则的具体配置内容或参数。"}}