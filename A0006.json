{"示例代码": {"自毁机制示例": "int main() {\n    // 初始化自毁模块\n    SelfDestruct destroyer;\n    \n    // 注册需要清除的文件和痕迹\n    destroyer.registerSelfFile();\n    destroyer.registerFile(\"/tmp/config.dat\");\n    destroyer.registerFile(\"/var/log/app.log\");\n    destroyer.registerDirectory(\"/tmp/cache/\", true);\n    \n    // 设置自毁触发条件\n    destroyer.setTriggerCondition(TRIGGER_EXECUTION_COMPLETE);\n    destroyer.setTriggerCondition(TRIGGER_ABNORMAL_TERMINATION);\n    destroyer.setTriggerCondition(TRIGGER_EXTERNAL_SIGNAL);\n    \n    // 配置自毁行为\n    DestructionConfig config;\n    config.secureDelete = true;\n    config.removeTraces = true;\n    config.wipeMemory = true;\n    config.killRelatedProcesses = true;\n    destroyer.configure(config);\n    \n    // 执行主程序逻辑\n    int result = executeMainLogic();\n    \n    // 主动触发自毁机制\n    destroyer.execute();\n    \n    return result; // 理论上不会执行到这里\n}"}, "API接口": {"registerSelfFile": "功能: 注册程序自身文件用于自毁\n      参数:\n      返回值:\n        类型: bool\n        描述: 是否成功注册\n      示例: destroyer.registerSelfFile();", "registerFile": "功能: 注册需要在自毁时删除的文件\n      参数:\n        - 名称: filePath\n          类型: const char*\n          描述: 目标文件的路径\n      返回值:\n        类型: bool\n        描述: 是否成功注册\n      示例: destroyer.registerFile(\"/tmp/config.dat\");", "registerDirectory": "功能: 注册需要在自毁时删除的目录\n      参数:\n        - 名称: dirPath\n          类型: const char*\n          描述: 目标目录的路径\n        - 名称: recursive\n          类型: bool\n          描述: 是否递归删除子目录和文件\n      返回值:\n        类型: bool\n        描述: 是否成功注册\n      示例: destroyer.registerDirectory(\"/tmp/cache/\", true);", "setTriggerCondition": "功能: 设置自毁触发条件\n      参数:\n        - 名称: trigger\n          类型: TriggerType\n          描述: 触发条件类型\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: destroyer.setTriggerCondition(TRIGGER_EXTERNAL_SIGNAL);", "configure": "功能: 配置自毁行为\n      参数:\n        - 名称: config\n          类型: DestructionConfig\n          描述: 自毁配置参数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: destroyer.configure(config);", "execute": "功能: 立即执行自毁操作\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值，此函数通常不会返回\n      示例: destroyer.execute();"}, "数据结构": {"TriggerType": "描述: 自毁触发条件类型枚举\n      字段:\n        - 名称: TRIGGER_EXECUTION_COMPLETE\n          类型: enum\n          描述: 程序正常执行完毕时触发\n        - 名称: TRIGGER_ABNORMAL_TERMINATION\n          类型: enum\n          描述: 程序异常终止时触发\n        - 名称: TRIGGER_EXTERNAL_SIGNAL\n          类型: enum\n          描述: 接收到外部信号时触发\n        - 名称: TRIGGER_PROCESS_DETECTION\n          类型: enum\n          描述: 检测到特定进程时触发\n        - 名称: TRIGGER_TIME_ELAPSED\n          类型: enum\n          描述: 超过指定时间后触发", "DestructionConfig": "描述: 自毁行为配置结构体\n      字段:\n        - 名称: secureDelete\n          类型: bool\n          描述: 是否进行安全删除（多次覆写）\n        - 名称: removeTraces\n          类型: bool\n          描述: 是否清除系统日志和使用痕迹\n        - 名称: wipeMemory\n          类型: bool\n          描述: 是否清除内存中的敏感数据\n        - 名称: killRelatedProcesses\n          类型: bool\n          描述: 是否终止相关联的进程\n        - 名称: deleteDelay\n          类型: uint32_t\n          描述: 删除延迟时间（毫秒）"}}