{"示例代码": {"程序流程混淆示例": "int main() {\n    // 初始化程序流程混淆模块\n    ControlFlowObfuscator obfuscator;\n    \n    // 配置控制流混淆参数\n    ObfuscationConfig config;\n    config.enableFlatteningTransform = true;\n    config.enableBogusPredicates = true;\n    config.enableOpaqueBranches = true;\n    config.enableSubroutineReordering = true;\n    config.enableIndirectJumps = true;\n    config.obfuscationLevel = 3; // 混淆级别(1-5)\n    \n    // 初始化混淆器\n    if (!obfuscator.Initialize(config)) {\n        fprintf(stderr, \"无法初始化程序流程混淆模块\\n\");\n        return 1;\n    }\n    \n    printf(\"程序流程混淆模块已启动\\n\");\n    \n    // 注册需要保护的函数\n    int (*calculateResult)(int, int, int) = [](int a, int b, int c) -> int {\n        int result = 0;\n        \n        // 一个简单但逻辑清晰的计算函数，将被混淆\n        if (a > b) {\n            result = a * b + c;\n        } else if (a == b) {\n            result = a * a - c;\n        } else {\n            result = b / (a > 0 ? a : 1) + c * 2;\n        }\n        \n        return result;\n    };\n    \n    // 注册函数获取唯一ID\n    uint32_t functionId = obfuscator.RegisterFunction((void*)calculateResult, 256);\n    if (functionId == 0) {\n        fprintf(stderr, \"函数注册失败\\n\");\n        return 1;\n    }\n    \n    printf(\"函数已注册，ID: %u\\n\", functionId);\n    \n    // 创建混淆版本\n    void* obfuscatedFunction = obfuscator.CreateObfuscatedVersion(functionId);\n    if (!obfuscatedFunction) {\n        fprintf(stderr, \"创建混淆版本失败\\n\");\n        return 1;\n    }\n    \n    printf(\"已创建混淆版本\\n\");\n    \n    // 设置混淆分析检测回调\n    obfuscator.SetAnalysisDetectionCallback([](AnalysisAttemptInfo* info) {\n        printf(\"检测到可能的分析尝试!\\n\");\n        printf(\"  - 类型: %s\\n\", info->attemptType);\n        printf(\"  - 可能源地址: 0x%p\\n\", info->sourceAddress);\n        printf(\"  - 可信度: %.2f\\n\", info->confidenceLevel);\n    });\n    \n    // 转换为函数指针\n    int (*obfuscatedCalculate)(int, int, int) = \n        (int (*)(int, int, int))obfuscatedFunction;\n    \n    // 测试原始函数与混淆函数的行为\n    printf(\"\\n测试原始函数与混淆函数的行为:\\n\");\n    \n    int testCases[3][3] = {\n        {10, 5, 2},  // a > b\n        {7, 7, 3},   // a == b\n        {3, 9, 4}    // a < b\n    };\n    \n    for (int i = 0; i < 3; i++) {\n        int a = testCases[i][0];\n        int b = testCases[i][1];\n        int c = testCases[i][2];\n        \n        // 调用原始函数\n        int originalResult = calculateResult(a, b, c);\n        \n        // 调用混淆版本\n        int obfuscatedResult = obfuscatedCalculate(a, b, c);\n        \n        printf(\"测试用例 #%d: a=%d, b=%d, c=%d\\n\", i+1, a, b, c);\n        printf(\"  - 原始结果: %d\\n\", originalResult);\n        printf(\"  - 混淆结果: %d\\n\", obfuscatedResult);\n        printf(\"  - 结果一致: %s\\n\", \n               originalResult == obfuscatedResult ? \"是\" : \"否\");\n    }\n    \n    // 分析混淆效果\n    printf(\"\\n分析混淆效果:\\n\");\n    ObfuscationMetrics metrics = obfuscator.AnalyzeObfuscationEffectiveness(functionId);\n    \n    printf(\"混淆指标:\\n\");\n    printf(\"  - 循环度量复杂度: %.2f\\n\", metrics.cyclomaticComplexity);\n    printf(\"  - 控制流平整化程度: %.2f%%\\n\", metrics.flatteningPercentage * 100);\n    printf(\"  - 假分支比例: %.2f%%\\n\", metrics.bogusPredicatesPercentage * 100);\n    printf(\"  - 逆向工程耐受性评分: %.2f/10\\n\", metrics.reverseEngineeringResistance);\n    \n    // 清理资源\n    printf(\"\\n清理资源...\\n\");\n    obfuscator.ReleaseObfuscatedVersion(functionId);\n    obfuscator.Cleanup();\n    \n    return 0;\n}"}, "API接口": {"Initialize": "功能: 初始化程序流程混淆模块\n      参数:\n        - 名称: config\n          类型: ObfuscationConfig\n          描述: 混淆配置参数\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: obfuscator.Initialize(config)", "RegisterFunction": "功能: 注册需要混淆的函数\n      参数:\n        - 名称: functionPtr\n          类型: void*\n          描述: 函数指针\n        - 名称: size\n          类型: size_t\n          描述: 函数代码大小(字节)\n      返回值:\n        类型: uint32_t\n        描述: 函数注册ID，0表示失败\n      示例: functionId = obfuscator.RegisterFunction(functionPtr, size)", "CreateObfuscatedVersion": "功能: 创建函数的混淆版本\n      参数:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n      返回值:\n        类型: void*\n        描述: 混淆后的函数指针，NULL表示失败\n      示例: obfuscatedFunction = obfuscator.CreateObfuscatedVersion(functionId)", "SetAnalysisDetectionCallback": "功能: 设置分析尝试检测回调\n      参数:\n        - 名称: callback\n          类型: std::function<void(AnalysisAttemptInfo*)>\n          描述: 检测到分析尝试时调用的回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: obfuscator.SetAnalysisDetectionCallback(callbackFunction)", "AnalyzeObfuscationEffectiveness": "功能: 分析混淆有效性\n      参数:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n      返回值:\n        类型: ObfuscationMetrics\n        描述: 混淆指标\n      示例: metrics = obfuscator.AnalyzeObfuscationEffectiveness(functionId)", "ReleaseObfuscatedVersion": "功能: 释放混淆版本资源\n      参数:\n        - 名称: functionId\n          类型: uint32_t\n          描述: 函数ID\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: obfuscator.ReleaseObfuscatedVersion(functionId)", "Cleanup": "功能: 清理所有资源\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: obfuscator.Cleanup()"}, "数据结构": {"ObfuscationConfig": "描述: 流程混淆配置\n      字段:\n        - 名称: enableFlatteningTransform\n          类型: bool\n          描述: 是否启用平坦化变换\n        - 名称: enableBogusPredicates\n          类型: bool\n          描述: 是否启用虚假条件判断\n        - 名称: enableOpaqueBranches\n          类型: bool\n          描述: 是否启用不透明分支\n        - 名称: enableSubroutineReordering\n          类型: bool\n          描述: 是否启用子程序重排序\n        - 名称: enableIndirectJumps\n          类型: bool\n          描述: 是否启用间接跳转\n        - 名称: obfuscationLevel\n          类型: uint32_t\n          描述: 混淆级别(1-5)", "AnalysisAttemptInfo": "描述: 分析尝试信息\n      字段:\n        - 名称: attemptType\n          类型: char[32]\n          描述: 分析尝试类型\n        - 名称: sourceAddress\n          类型: void*\n          描述: 可能的源地址\n        - 名称: confidenceLevel\n          类型: float\n          描述: 可信度(0-1)\n        - 名称: timeDetected\n          类型: uint64_t\n          描述: 检测时间戳", "ObfuscationMetrics": "描述: 混淆效果指标\n      字段:\n        - 名称: cyclomaticComplexity\n          类型: float\n          描述: 循环复杂度\n        - 名称: flatteningPercentage\n          类型: float\n          描述: 控制流平坦化程度(0-1)\n        - 名称: bogusPredicatesPercentage\n          类型: float\n          描述: 虚假条件判断比例(0-1)\n        - 名称: reverseEngineeringResistance\n          类型: float\n          描述: 逆向工程耐受性评分(0-10)"}}