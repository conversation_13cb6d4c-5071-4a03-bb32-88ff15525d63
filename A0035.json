{"示例代码": {"钩子技术示例": "int main() {\n    // 创建钩子管理器\n    HookManager hookManager;\n    \n    // 初始化钩子管理器\n    if (!hookManager.initialize()) {\n        printf(\"Failed to initialize hook manager\\n\");\n        return 1;\n    }\n    \n    // 配置函数钩子\n    FunctionHookConfig readHook;\n    readHook.targetFunction = \"read\";\n    readHook.targetLibrary = \"libc.so.6\";\n    readHook.hookType = HOOK_TYPE_INLINE;\n    \n    // 设置钩子回调函数\n    readHook.setCallback([](HookContext* context) {\n        // 原始read函数原型: ssize_t read(int fd, void *buf, size_t count);\n        int fd = context->getArgument<int>(0);\n        void* buffer = context->getArgument<void*>(1);\n        size_t count = context->getArgument<size_t>(2);\n        \n        printf(\"[Hook] read() called with fd=%d, count=%zu\\n\", fd, count);\n        \n        // 调用原始函数\n        ssize_t result = context->callOriginal<ssize_t>();\n        \n        // 在这里可以检查和修改读取的数据\n        if (result > 0) {\n            printf(\"[Hook] read() returned %zd bytes\\n\", result);\n            \n            // 例如，在特定情况下修改读取的数据\n            if (fd == STDIN_FILENO) {\n                char* dataBuffer = static_cast<char*>(buffer);\n                for (ssize_t i = 0; i < result; i++) {\n                    // 转换为大写（简单示例）\n                    if (dataBuffer[i] >= 'a' && dataBuffer[i] <= 'z') {\n                        dataBuffer[i] = dataBuffer[i] - 'a' + 'A';\n                    }\n                }\n                printf(\"[Hook] Modified read data\\n\");\n            }\n        }\n        \n        // 返回修改后的结果\n        return result;\n    });\n    \n    // 安装读取函数钩子\n    HookResult readResult = hookManager.installHook(readHook);\n    if (!readResult.success) {\n        printf(\"Failed to install read hook: %s\\n\", readResult.errorMessage.c_str());\n    } else {\n        printf(\"Successfully installed read hook at address %p\\n\", readResult.hookAddress);\n    }\n    \n    // 配置系统调用钩子\n    SyscallHookConfig openHook;\n    openHook.syscallNumber = SYS_open;\n    openHook.hookType = HOOK_TYPE_SYSCALL_TABLE;\n    \n    // 设置系统调用钩子回调\n    openHook.setCallback([](HookContext* context) {\n        // 原始open系统调用原型: long open(const char *pathname, int flags, mode_t mode);\n        const char* pathname = context->getArgument<const char*>(0);\n        int flags = context->getArgument<int>(1);\n        \n        printf(\"[Hook] open syscall: path=%s, flags=%d\\n\", pathname, flags);\n        \n        // 检查是否访问敏感文件\n        if (pathname && strstr(pathname, \"/etc/passwd\")) {\n            printf(\"[Hook] Detected attempt to open /etc/passwd, redirecting...\\n\");\n            // 修改参数，重定向到其他文件\n            context->setArgument<const char*>(0, \"/tmp/fake_passwd\");\n        }\n        \n        // 调用原始系统调用\n        long result = context->callOriginal<long>();\n        printf(\"[Hook] open returned: %ld\\n\", result);\n        \n        return result;\n    });\n    \n    // 安装open系统调用钩子\n    HookResult openResult = hookManager.installHook(openHook);\n    if (!openResult.success) {\n        printf(\"Failed to install open syscall hook: %s\\n\", openResult.errorMessage.c_str());\n    } else {\n        printf(\"Successfully installed open syscall hook\\n\");\n    }\n    \n    // 安装库加载钩子（拦截dlopen调用）\n    LibraryHookConfig dlHook;\n    dlHook.hookType = HOOK_TYPE_LIBRARY_LOAD;\n    dlHook.setCallback([](const char* libPath) {\n        printf(\"[Hook] Loading library: %s\\n\", libPath);\n        \n        // 可以选择阻止某些库加载\n        if (strstr(libPath, \"libsecurity\")) {\n            printf(\"[Hook] Blocking load of security library\\n\");\n            return false;\n        }\n        return true;\n    });\n    \n    hookManager.installLibraryHook(dlHook);\n    \n    printf(\"Hooks installed. Running with hooks...\\n\\n\");\n    \n    // 测试钩子工作\n    char buffer[128];\n    printf(\"Enter some text: \");\n    fflush(stdout);\n    read(STDIN_FILENO, buffer, sizeof(buffer)-1);\n    buffer[sizeof(buffer)-1] = '\\0';\n    printf(\"You entered: %s\\n\", buffer);\n    \n    // 测试文件打开钩子\n    int fd = open(\"/etc/passwd\", O_RDONLY);\n    if (fd != -1) {\n        printf(\"Opened file descriptor: %d\\n\", fd);\n        close(fd);\n    }\n    \n    // 卸载所有钩子（可选）\n    printf(\"\\nUninstalling hooks...\\n\");\n    hookManager.removeAllHooks();\n    printf(\"Hooks removed\\n\");\n    \n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化钩子管理器\n      参数:\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: bool success = hookManager.initialize();", "installHook": "功能: 安装钩子\n      参数:\n        - 名称: hookConfig\n          类型: HookConfig\n          描述: 钩子配置\n      返回值:\n        类型: HookResult\n        描述: 钩子安装结果\n      示例: HookResult result = hookManager.installHook(hookConfig);", "installLibraryHook": "功能: 安装库加载钩子\n      参数:\n        - 名称: hookConfig\n          类型: LibraryHookConfig\n          描述: 库钩子配置\n      返回值:\n        类型: HookResult\n        描述: 钩子安装结果\n      示例: HookResult result = hookManager.installLibraryHook(hookConfig);", "removeHook": "功能: 移除特定钩子\n      参数:\n        - 名称: hookId\n          类型: uint64_t\n          描述: 钩子ID\n      返回值:\n        类型: bool\n        描述: 移除是否成功\n      示例: bool success = hookManager.removeHook(hookId);", "removeAllHooks": "功能: 移除所有钩子\n      参数:\n      返回值:\n        类型: bool\n        描述: 操作是否成功\n      示例: bool success = hookManager.removeAllHooks();", "setCallback": "功能: 设置钩子回调函数\n      参数:\n        - 名称: callback\n          类型: std::function<T(HookContext*)>\n          描述: 回调函数\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hookConfig.setCallback([](HookContext* context) { /* callback code */ });", "getActiveHooks": "功能: 获取已激活的钩子列表\n      参数:\n      返回值:\n        类型: std::vector<HookInfo>\n        描述: 活动钩子信息列表\n      示例: auto hooks = hookManager.getActiveHooks();"}, "数据结构": {"HookConfig": "描述: 通用钩子配置基类\n      字段:\n        - 名称: hookType\n          类型: HookType\n          描述: 钩子类型\n        - 名称: priority\n          类型: int\n          描述: 钩子优先级\n        - 名称: persistent\n          类型: bool\n          描述: 是否持久化\n        - 名称: description\n          类型: std::string\n          描述: 钩子描述", "FunctionHookConfig": "描述: 函数钩子配置结构体，继承自HookConfig\n      字段:\n        - 名称: targetFunction\n          类型: std::string\n          描述: 目标函数名称\n        - 名称: targetLibrary\n          类型: std::string\n          描述: 目标库名称\n        - 名称: targetAddress\n          类型: void*\n          描述: 目标地址（可选）\n        - 名称: hookAllInstances\n          类型: bool\n          描述: 是否钩住所有实例\n        - 名称: stubByteCount\n          类型: size_t\n          描述: 替换字节数（仅用于HOOK_TYPE_INLINE）", "SyscallHookConfig": "描述: 系统调用钩子配置结构体，继承自HookConfig\n      字段:\n        - 名称: syscallNumber\n          类型: int\n          描述: 系统调用号\n        - 名称: hookPreCall\n          类型: bool\n          描述: 是否钩住调用前\n        - 名称: hookPostCall\n          类型: bool\n          描述: 是否钩住调用后", "LibraryHookConfig": "描述: 库钩子配置结构体，继承自HookConfig\n      字段:\n        - 名称: targetLibraries\n          类型: std::vector<std::string>\n          描述: 目标库名称列表\n        - 名称: interceptLoadOnly\n          类型: bool\n          描述: 是否仅拦截加载操作", "HookType": "描述: 钩子类型枚举\n      字段:\n        - 名称: HOOK_TYPE_INLINE\n          类型: enum\n          描述: 内联钩子（修改目标代码）\n        - 名称: HOOK_TYPE_IAT\n          类型: enum\n          描述: 导入地址表钩子\n        - 名称: HOOK_TYPE_GOT\n          类型: enum\n          描述: 全局偏移表钩子\n        - 名称: HOOK_TYPE_SYSCALL_TABLE\n          类型: enum\n          描述: 系统调用表钩子\n        - 名称: HOOK_TYPE_LIBRARY_LOAD\n          类型: enum\n          描述: 库加载钩子", "HookContext": "描述: 钩子上下文结构体\n      字段:\n        - 名称: hookId\n          类型: uint64_t\n          描述: 钩子ID\n        - 名称: hookType\n          类型: HookType\n          描述: 钩子类型\n        - 名称: originalFunction\n          类型: void*\n          描述: 原始函数指针\n        - 名称: registersState\n          类型: void*\n          描述: 寄存器状态\n        - 名称: threadId\n          类型: pid_t\n          描述: 线程ID", "HookResult": "描述: 钩子操作结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 操作是否成功\n        - 名称: hookId\n          类型: uint64_t\n          描述: 钩子ID\n        - 名称: hookAddress\n          类型: void*\n          描述: 钩子地址\n        - 名称: originalBytes\n          类型: std::vector<uint8_t>\n          描述: 原始字节\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误消息", "HookInfo": "描述: 钩子信息结构体\n      字段:\n        - 名称: hookId\n          类型: uint64_t\n          描述: 钩子ID\n        - 名称: hookType\n          类型: HookType\n          描述: 钩子类型\n        - 名称: targetName\n          类型: std::string\n          描述: 目标名称\n        - 名称: targetAddress\n          类型: void*\n          描述: 目标地址\n        - 名称: installTime\n          类型: time_t\n          描述: 安装时间\n        - 名称: active\n          类型: bool\n          描述: 是否激活\n        - 名称: callCount\n          类型: uint64_t\n          描述: 调用计数\n        - 名称: description\n          类型: std::string\n          描述: 描述信息"}}