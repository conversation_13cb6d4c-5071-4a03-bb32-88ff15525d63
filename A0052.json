{"示例代码": {"连接端口转发示例": "int main() {\n    // 初始化端口转发连接管理器\n    PortForwardConnector connector;\n    \n    // 获取已配置的转发规则ID\n    FORWARD_RULE_ID ruleId = 123;  // 示例中假设规则ID为123\n    \n    // 连接参数配置\n    CONNECT_OPTIONS options;\n    memset(&options, 0, sizeof(CONNECT_OPTIONS));\n    options.timeout = 15;       // 连接超时15秒\n    options.retryCount = 3;     // 失败重试3次\n    options.keepAlive = true;   // 启用连接保持\n    \n    // 建立端口转发连接\n    FORWARD_CONNECTION_ID connectionId;\n    if (connector.ConnectForward(ruleId, &options, &connectionId)) {\n        printf(\"端口转发通道已成功建立，连接ID: %u\\n\", connectionId);\n        \n        // 获取连接信息\n        FORWARD_CONNECTION_INFO connInfo;\n        if (connector.GetConnectionInfo(connectionId, &connInfo)) {\n            printf(\"连接详情:\\n\");\n            printf(\"本地端口: %d\\n\", connInfo.localPort);\n            printf(\"远程主机: %s:%d\\n\", connInfo.remoteHost, connInfo.remotePort);\n            printf(\"建立时间: %s\\n\", ctime((time_t*)&connInfo.establishTime));\n            printf(\"传输字节: %lu\\n\", connInfo.bytesSent + connInfo.bytesReceived);\n        }\n        \n        // 设置数据监控回调\n        connector.SetDataMonitorCallback(connectionId, dataMonitorCallback, NULL);\n        \n        printf(\"端口转发通道运行中，按Ctrl+C退出...\\n\");\n        \n        // 主循环，保持程序运行\n        while (connector.IsForwardActive(connectionId)) {\n            sleep(1);\n        }\n        \n        printf(\"端口转发连接已断开\\n\");\n    } else {\n        printf(\"无法建立端口转发通道: %s\\n\", connector.GetLastError());\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"ConnectForward": "功能: 建立端口转发连接\n      参数:\n        - 名称: ruleId\n          类型: FORWARD_RULE_ID\n          描述: 转发规则ID\n        - 名称: options\n          类型: CONNECT_OPTIONS*\n          描述: 连接选项结构体指针\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID*\n          描述: 用于存储创建的连接ID\n      返回值:\n        类型: bool\n        描述: 连接是否成功建立\n      示例: connector.ConnectForward(ruleId, &options, &connectionId)", "GetConnectionInfo": "功能: 获取转发连接信息\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: info\n          类型: FORWARD_CONNECTION_INFO*\n          描述: 连接信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取连接信息\n      示例: connector.GetConnectionInfo(connectionId, &connInfo)", "IsForwardActive": "功能: 检查转发连接是否处于活动状态\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n      返回值:\n        类型: bool\n        描述: 连接是否处于活动状态\n      示例: connector.IsForwardActive(connectionId)", "SetDataMonitorCallback": "功能: 设置数据监控回调函数\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: callback\n          类型: ForwardDataCallback\n          描述: 回调函数指针\n        - 名称: userData\n          类型: void*\n          描述: 用户自定义数据指针\n      返回值:\n        类型: bool\n        描述: 回调是否设置成功\n      示例: connector.SetDataMonitorCallback(connectionId, dataMonitorCallback, NULL)", "GetLastError": "功能: 获取上一操作的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: connector.GetLastError()"}, "数据结构": {"CONNECT_OPTIONS": "描述: 连接选项结构体\n      字段:\n        - 名称: timeout\n          类型: uint32_t\n          描述: 连接超时时间(秒)\n        - 名称: retryCount\n          类型: uint8_t\n          描述: 连接失败重试次数\n        - 名称: keepAlive\n          类型: bool\n          描述: 是否启用连接保持\n        - 名称: bufferSize\n          类型: uint32_t\n          描述: 传输缓冲区大小(字节)\n        - 名称: flags\n          类型: uint32_t\n          描述: 连接标志位", "FORWARD_CONNECTION_INFO": "描述: 转发连接信息结构体\n      字段:\n        - 名称: connectionId\n          类型: uint32_t\n          描述: 连接ID\n        - 名称: ruleId\n          类型: uint32_t\n          描述: 所使用的规则ID\n        - 名称: localHost\n          类型: char[64]\n          描述: 本地主机地址\n        - 名称: localPort\n          类型: uint16_t\n          描述: 本地端口\n        - 名称: remoteHost\n          类型: char[64]\n          描述: 远程主机地址\n        - 名称: remotePort\n          类型: uint16_t\n          描述: 远程端口\n        - 名称: status\n          类型: uint8_t\n          描述: 连接状态\n        - 名称: establishTime\n          类型: uint64_t\n          描述: 连接建立时间戳\n        - 名称: bytesSent\n          类型: uint64_t\n          描述: 已发送字节数\n        - 名称: bytesReceived\n          类型: uint64_t\n          描述: 已接收字节数", "FORWARD_DATA_EVENT": "描述: 转发数据事件结构体\n      字段:\n        - 名称: connectionId\n          类型: uint32_t\n          描述: 连接ID\n        - 名称: direction\n          类型: uint8_t\n          描述: 数据方向(上行/下行)\n        - 名称: dataSize\n          类型: uint32_t\n          描述: 数据大小\n        - 名称: timestamp\n          类型: uint64_t\n          描述: 事件时间戳"}}