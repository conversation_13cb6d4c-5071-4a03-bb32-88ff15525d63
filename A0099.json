{"示例代码": {"说明": "暂无"}, "API接口": {"EvadeKasperskyRuntimeChecks": "功能: 执行一系列高级操作以尝试规避卡巴斯基的运行时检测。\n      参数:\n        - 名称: evasion_profile_id\n          类型: const char*\n          描述: 指定预定义的绕过配置方案ID。\n      返回值:\n        类型: int\n        描述: 表示绕过尝试的结果状态码。0为成功，非0为失败或特定错误。\n      示例: if (EvadeKasperskyRuntimeChecks(\"profile_aggressive_stealth\") == 0) { /* 绕过机制已启动 */ }"}, "数据结构": {"KasperskyEvasionProfile": "描述: 定义一个针对卡巴斯基的绕过配置方案。\n      字段:\n        - 名称: profile_name\n          类型: char[64]\n          描述: 配置方案名称。\n        - 名称: disable_hips_hooks\n          类型: bool\n          描述: 是否尝试禁用或绕过HIPS（主机入侵防御系统）的钩子。\n        - 名称: process_hollowing_target\n          类型: char[128]\n          描述: 如果使用进程镂空技术，指定目标进程名 (例如 \"explorer.exe\")。"}}