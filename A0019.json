{"示例代码": {"内存注入示例": "int main() {\n    // 创建内存注入管理器\n    MemoryInjector injector;\n    \n    // 配置注入选项\n    InjectionConfig config;\n    config.targetProcess = \"target_process\";\n    config.injectionMethod = INJECT_METHOD_PTRACE;\n    config.payloadPath = \"/opt/payload/shellcode.bin\";\n    config.executeAfterInjection = true;\n    \n    // 配置内存防护选项\n    MemoryProtectionOptions protection;\n    protection.hideFromScan = true;\n    protection.obfuscateCode = true;\n    protection.encryptPayload = true;\n    protection.useSelfModifyingCode = true;\n    \n    // 应用内存保护选项\n    injector.setProtectionOptions(protection);\n    \n    // 执行内存注入\n    InjectionResult result = injector.inject(config);\n    \n    if (result.success) {\n        printf(\"Memory injection successful\\n\");\n        printf(\"Injected at address: %p\\n\", result.injectedAddress);\n        printf(\"Process ID: %d\\n\", result.processId);\n    } else {\n        printf(\"Injection failed: %s\\n\", result.errorMessage.c_str());\n        return 1;\n    }\n    \n    // 监控注入结果\n    injector.monitorExecution(result.processId, [](InjectionStatus status) {\n        printf(\"Execution status: %s\\n\", \n               status.isRunning ? \"Running\" : \"Terminated\");\n        return status.isRunning;\n    });\n    \n    return 0;\n}"}, "API接口": {"setProtectionOptions": "功能: 设置内存保护选项\n      参数:\n        - 名称: options\n          类型: MemoryProtectionOptions\n          描述: 内存保护选项\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: injector.setProtectionOptions(protection);", "inject": "功能: 执行内存注入操作\n      参数:\n        - 名称: config\n          类型: InjectionConfig\n          描述: 注入配置\n      返回值:\n        类型: InjectionResult\n        描述: 注入结果\n      示例: InjectionResult result = injector.inject(config);", "findTargetProcess": "功能: 查找目标进程\n      参数:\n        - 名称: processPattern\n          类型: const std::string&\n          描述: 进程名称或模式\n      返回值:\n        类型: std::vector<ProcessInfo>\n        描述: 符合条件的进程列表\n      示例: auto processes = injector.findTargetProcess(\"firefox\");", "monitorExecution": "功能: 监控注入代码的执行\n      参数:\n        - 名称: processId\n          类型: pid_t\n          描述: 目标进程ID\n        - 名称: callback\n          类型: std::function<bool(InjectionStatus)>\n          描述: 状态回调函数，返回false停止监控\n      返回值:\n        类型: bool\n        描述: 监控是否成功启动\n      示例: bool monitoring = injector.monitorExecution(pid, statusCallback);", "cleanup": "功能: 清理注入痕迹\n      参数:\n        - 名称: processId\n          类型: pid_t\n          描述: 目标进程ID\n        - 名称: address\n          类型: void*\n          描述: 注入地址\n      返回值:\n        类型: bool\n        描述: 清理是否成功\n      示例: bool cleaned = injector.cleanup(result.processId, result.injectedAddress);"}, "数据结构": {"InjectionConfig": "描述: 内存注入配置结构体\n      字段:\n        - 名称: targetProcess\n          类型: std::string\n          描述: 目标进程名称或ID\n        - 名称: injectionMethod\n          类型: InjectionMethod\n          描述: 注入方法\n        - 名称: payloadPath\n          类型: std::string\n          描述: 有效负载文件路径\n        - 名称: executeAfterInjection\n          类型: bool\n          描述: 是否在注入后执行代码\n        - 名称: executeDelay\n          类型: uint32_t\n          描述: 注入后执行延迟（毫秒）\n        - 名称: preferredAddress\n          类型: void*\n          描述: 首选注入地址，nullptr表示自动选择", "MemoryProtectionOptions": "描述: 内存保护选项结构体\n      字段:\n        - 名称: hideFromScan\n          类型: bool\n          描述: 是否从内存扫描中隐藏\n        - 名称: obfuscateCode\n          类型: bool\n          描述: 是否混淆代码\n        - 名称: encryptPayload\n          类型: bool\n          描述: 是否加密有效负载\n        - 名称: useSelfModifyingCode\n          类型: bool\n          描述: 是否使用自修改代码\n        - 名称: customEncryptionKey\n          类型: std::string\n          描述: 自定义加密密钥", "InjectionMethod": "描述: 注入方法枚举\n      字段:\n        - 名称: INJECT_METHOD_PTRACE\n          类型: enum\n          描述: 使用ptrace进行注入\n        - 名称: INJECT_METHOD_SHARED_MEMORY\n          类型: enum\n          描述: 使用共享内存进行注入\n        - 名称: INJECT_METHOD_LINKER\n          类型: enum\n          描述: 使用动态链接器进行注入\n        - 名称: INJECT_METHOD_PRELOAD\n          类型: enum\n          描述: 使用预加载方式进行注入", "InjectionResult": "描述: 注入结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 注入是否成功\n        - 名称: processId\n          类型: pid_t\n          描述: 目标进程ID\n        - 名称: injectedAddress\n          类型: void*\n          描述: 注入地址\n        - 名称: errorMessage\n          类型: std::string\n          描述: 如果失败，包含错误信息\n        - 名称: payloadSize\n          类型: size_t\n          描述: 注入的有效负载大小", "InjectionStatus": "描述: 注入状态结构体\n      字段:\n        - 名称: isRunning\n          类型: bool\n          描述: 注入代码是否正在运行\n        - 名称: executionTime\n          类型: uint64_t\n          描述: 已执行时间（毫秒）\n        - 名称: currentAddress\n          类型: void*\n          描述: 当前执行地址\n        - 名称: returnValue\n          类型: int\n          描述: 如果已完成，包含返回值", "ProcessInfo": "描述: 进程信息结构体\n      字段:\n        - 名称: pid\n          类型: pid_t\n          描述: 进程ID\n        - 名称: name\n          类型: std::string\n          描述: 进程名称\n        - 名称: user\n          类型: std::string\n          描述: 所有者用户名\n        - 名称: memoryMaps\n          类型: std::vector<MemoryRegion>\n          描述: 进程内存映射"}}