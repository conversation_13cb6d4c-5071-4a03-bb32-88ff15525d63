#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import re
import pandas as pd

def extract_file_number(filename):
    """从文件名中提取编号，例如从'A0001.json'中提取'0001'"""
    match = re.search(r'A(\d+)\.json', filename)
    if match:
        return int(match.group(1))
    return 0

def read_json_content(file_path):
    """读取JSON文件的原始内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"读取文件 {file_path} 时发生错误: {str(e)}")
        return None

def export_jsons_to_excel(dir_path, output_file="JSON文件汇总.xlsx"):
    """将目录下的所有JSON文件按编号顺序导出到Excel"""
    # 获取所有JSON文件并排序
    json_files = [f for f in os.listdir(dir_path) if f.endswith('.json') and f.startswith('A')]
    json_files.sort(key=extract_file_number)
    
    # 准备数据
    data = []
    
    print(f"处理 {len(json_files)} 个JSON文件...")
    
    # 处理每个JSON文件
    for json_file in json_files:
        file_number = extract_file_number(json_file)
        file_path = os.path.join(dir_path, json_file)
        content = read_json_content(file_path)
        
        if content:
            data.append({
                "文件编号": f"A{file_number:04d}",
                "JSON内容": content
            })
    
    # 创建DataFrame并导出到Excel
    df = pd.DataFrame(data)
    excel_path = os.path.join(dir_path, output_file)
    df.to_excel(excel_path, index=False)
    
    print(f"Excel文件已创建: {output_file}")
    print(f"共导出 {len(data)} 个JSON文件")

if __name__ == "__main__":
    # 获取脚本所在目录
    dir_path = os.path.dirname(os.path.realpath(__file__))
    export_jsons_to_excel(dir_path)
    print("处理完成!")
