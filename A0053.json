{"示例代码": {"断开端口转发示例": "int main() {\n    // 初始化端口转发连接管理器\n    PortForwardConnector connector;\n    \n    // 获取当前活动的端口转发连接列表\n    FORWARD_CONNECTION_ID connections[10];\n    uint8_t connectionCount = 0;\n    \n    if (connector.ListActiveConnections(connections, 10, &connectionCount)) {\n        printf(\"当前活动的端口转发连接数量: %d\\n\", connectionCount);\n        \n        // 遍历显示所有活动连接\n        for (int i = 0; i < connectionCount; i++) {\n            FORWARD_CONNECTION_INFO info;\n            if (connector.GetConnectionInfo(connections[i], &info)) {\n                printf(\"%d. 连接ID %u: %s:%d -> %s:%d\\n\", i + 1,\n                       info.connectionId, info.localHost, info.localPort, \n                       info.remoteHost, info.remotePort);\n            }\n        }\n        \n        // 选择要断开的连接ID\n        if (connectionCount > 0) {\n            // 以第一个连接为例\n            FORWARD_CONNECTION_ID connectionToClose = connections[0];\n            \n            printf(\"准备断开连接ID: %u\\n\", connectionToClose);\n            \n            // 配置断开选项\n            DISCONNECT_OPTIONS options;\n            memset(&options, 0, sizeof(DISCONNECT_OPTIONS));\n            options.gracefulTimeout = 5;  // 等待5秒完成当前传输后断开\n            options.notifyPeers = true;   // 通知对端连接将断开\n            \n            // 执行断开操作\n            if (connector.DisconnectForward(connectionToClose, &options)) {\n                printf(\"端口转发连接已成功断开\\n\");\n                \n                // 记录断开事件\n                char logMessage[256];\n                snprintf(logMessage, sizeof(logMessage), \"端口转发连接 %u 已手动断开\", connectionToClose);\n                connector.LogEvent(logMessage, EVENT_LEVEL_INFO);\n            } else {\n                printf(\"断开端口转发连接失败: %s\\n\", connector.GetLastError());\n                return 1;\n            }\n        } else {\n            printf(\"没有活动的端口转发连接\\n\");\n        }\n    } else {\n        printf(\"获取活动连接列表失败: %s\\n\", connector.GetLastError());\n        return 1;\n    }\n    \n    return 0;\n}"}, "API接口": {"ListActiveConnections": "功能: 获取当前活动的端口转发连接列表\n      参数:\n        - 名称: connections\n          类型: FORWARD_CONNECTION_ID*\n          描述: 连接ID数组\n        - 名称: maxCount\n          类型: uint8_t\n          描述: 数组最大容量\n        - 名称: count\n          类型: uint8_t*\n          描述: 返回实际连接数量\n      返回值:\n        类型: bool\n        描述: 是否成功获取连接列表\n      示例: connector.ListActiveConnections(connections, 10, &connectionCount)", "GetConnectionInfo": "功能: 获取连接详细信息\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: info\n          类型: FORWARD_CONNECTION_INFO*\n          描述: 连接信息结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功获取连接信息\n      示例: connector.GetConnectionInfo(connections[i], &info)", "DisconnectForward": "功能: 断开指定的端口转发连接\n      参数:\n        - 名称: connectionId\n          类型: FORWARD_CONNECTION_ID\n          描述: 连接ID\n        - 名称: options\n          类型: DISCONNECT_OPTIONS*\n          描述: 断开选项结构体指针\n      返回值:\n        类型: bool\n        描述: 是否成功断开连接\n      示例: connector.DisconnectForward(connectionToClose, &options)", "LogEvent": "功能: 记录事件日志\n      参数:\n        - 名称: message\n          类型: const char*\n          描述: 日志消息\n        - 名称: level\n          类型: uint8_t\n          描述: 日志级别\n      返回值:\n        类型: bool\n        描述: 是否成功记录日志\n      示例: connector.LogEvent(logMessage, EVENT_LEVEL_INFO)", "GetLastError": "功能: 获取上一操作的错误信息\n      参数:\n      返回值:\n        类型: const char*\n        描述: 错误信息字符串\n      示例: connector.GetLastError()"}, "数据结构": {"DISCONNECT_OPTIONS": "描述: 断开连接选项结构体\n      字段:\n        - 名称: gracefulTimeout\n          类型: uint16_t\n          描述: 优雅断开超时时间(秒)\n        - 名称: notifyPeers\n          类型: bool\n          描述: 是否通知对端连接将断开\n        - 名称: forceClose\n          类型: bool\n          描述: 是否强制立即断开连接\n        - 名称: saveStats\n          类型: bool\n          描述: 是否保存连接统计信息", "EVENT_LEVELS": "描述: 事件日志级别枚举\n      字段:\n        - 名称: EVENT_LEVEL_DEBUG\n          类型: uint8_t\n          描述: 调试级别，值为0x01\n        - 名称: EVENT_LEVEL_INFO\n          类型: uint8_t\n          描述: 信息级别，值为0x02\n        - 名称: EVENT_LEVEL_WARNING\n          类型: uint8_t\n          描述: 警告级别，值为0x03\n        - 名称: EVENT_LEVEL_ERROR\n          类型: uint8_t\n          描述: 错误级别，值为0x04\n        - 名称: EVENT_LEVEL_CRITICAL\n          类型: uint8_t\n          描述: 严重错误级别，值为0x05", "DISCONNECT_RESULT": "描述: 断开连接结果结构体\n      字段:\n        - 名称: success\n          类型: bool\n          描述: 断开操作是否成功\n        - 名称: bytesTransferred\n          类型: uint64_t\n          描述: 连接期间传输的总字节数\n        - 名称: connectionDuration\n          类型: uint32_t\n          描述: 连接持续时间(秒)\n        - 名称: disconnectReason\n          类型: uint8_t\n          描述: 断开连接原因代码"}}