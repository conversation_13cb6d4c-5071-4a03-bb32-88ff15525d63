{"示例代码": {"内存扫描示例": "int main() {\n    // 创建内存扫描器实例\n    MemoryScanner scanner;\n\n    // 初始化内存扫描器\n    ScannerConfig config;\n    config.scanSystemProcesses = true;\n    config.scanUserProcesses = true;\n    config.scanKernelMemory = false;\n    config.scanMode = SCAN_MODE_DEEP;\n\n    if (!scanner.initialize(config)) {\n        printf(\"Failed to initialize memory scanner: %s\\n\", scanner.getLastError());\n        return 1;\n    }\n\n    // 设置内存扫描规则\n    ScanPattern pattern1;\n    pattern1.type = PATTERN_BYTE_SEQUENCE;\n    pattern1.byteSequence = {0x41, 0x42, 0x43, 0x44, 0x45}; // ABCDE\n    pattern1.name = \"TestPattern1\";\n\n    ScanPattern pattern2;\n    pattern2.type = PATTERN_STRING;\n    pattern2.stringPattern = \"password\";\n    pattern2.caseSensitive = false;\n    pattern2.name = \"PasswordSearch\";\n\n    scanner.addPattern(pattern1);\n    scanner.addPattern(pattern2);\n\n    // 执行扫描\n    printf(\"Starting memory scan...\\n\");\n    scanner.startScan();\n\n    // 等待扫描完成\n    ScanStatus status;\n    do {\n        status = scanner.getScanStatus();\n        printf(\"Scan progress: %.2f%%, Processes: %d/%d\\n\", \n               status.progressPercent, status.processesScanned, status.totalProcesses);\n        sleep(1);\n    } while (status.scanState == SCAN_STATE_RUNNING);\n\n    // 获取扫描结果\n    ScanResults results = scanner.getResults();\n    printf(\"\\nScan completed. Found %d matches across %d processes.\\n\", \n           results.totalMatches, results.affectedProcesses.size());\n\n    // 处理结果\n    for (const auto& match : results.matches) {\n        printf(\"\\nMatch found:\\n\");\n        printf(\"- Pattern: %s\\n\", match.patternName.c_str());\n        printf(\"- Process: %s (PID: %d)\\n\", match.processName.c_str(), match.pid);\n        printf(\"- Address: 0x%lx\\n\", match.memoryAddress);\n        printf(\"- Region: %s\\n\", match.memoryRegionType.c_str());\n        \n        if (match.patternType == PATTERN_STRING) {\n            printf(\"- Content: %.*s\\n\", (int)match.matchLength, (char*)match.matchData);\n        } else {\n            printf(\"- Raw bytes: \");\n            for (size_t i = 0; i < match.matchLength; i++) {\n                printf(\"%02X \", ((unsigned char*)match.matchData)[i]);\n            }\n            printf(\"\\n\");\n        }\n    }\n\n    // 对结果进行高级分析\n    MemoryAnalysisReport report = scanner.analyzeResults(results);\n    printf(\"\\nAnalysis Report:\\n\");\n    printf(\"- Potential credentials found: %d\\n\", report.potentialCredentials);\n    printf(\"- Encrypted content detected: %d\\n\", report.encryptedContent);\n    printf(\"- Suspicious memory regions: %d\\n\", report.suspiciousRegions);\n    \n    // 清理资源\n    scanner.cleanup();\n    return 0;\n}"}, "API接口": {"initialize": "功能: 初始化内存扫描器\n      参数:\n        - 名称: config\n          类型: ScannerConfig\n          描述: 扫描器配置\n      返回值:\n        类型: bool\n        描述: 初始化是否成功\n      示例: bool success = scanner.initialize(config);", "addPattern": "功能: 添加扫描模式\n      参数:\n        - 名称: pattern\n          类型: ScanPattern\n          描述: 扫描模式\n      返回值:\n        类型: bool\n        描述: 添加是否成功\n      示例: scanner.addPattern(pattern);", "startScan": "功能: 开始内存扫描\n      参数:\n      返回值:\n        类型: bool\n        描述: 扫描是否成功启动\n      示例: scanner.startScan();", "getScanStatus": "功能: 获取扫描状态\n      参数:\n      返回值:\n        类型: ScanStatus\n        描述: 当前扫描状态\n      示例: ScanStatus status = scanner.getScanStatus();", "getResults": "功能: 获取扫描结果\n      参数:\n      返回值:\n        类型: ScanResults\n        描述: 扫描结果\n      示例: ScanResults results = scanner.getResults();", "analyzeResults": "功能: 分析扫描结果\n      参数:\n        - 名称: results\n          类型: ScanResults\n          描述: 扫描结果\n      返回值:\n        类型: MemoryAnalysisReport\n        描述: 内存分析报告\n      示例: MemoryAnalysisReport report = scanner.analyzeResults(results);", "cleanup": "功能: 清理扫描器资源\n      参数:\n      返回值:\n        类型: void\n        描述: 无返回值\n      示例: scanner.cleanup();"}, "数据结构": {"ScannerConfig": "描述: 扫描器配置结构体\n      字段:\n        - 名称: scanSystemProcesses\n          类型: bool\n          描述: 是否扫描系统进程\n        - 名称: scanUserProcesses\n          类型: bool\n          描述: 是否扫描用户进程\n        - 名称: scanKernelMemory\n          类型: bool\n          描述: 是否扫描内核内存\n        - 名称: scanMode\n          类型: ScanMode\n          描述: 扫描模式", "ScanPattern": "描述: 扫描模式结构体\n      字段:\n        - 名称: type\n          类型: PatternType\n          描述: 模式类型\n        - 名称: byteSequence\n          类型: std::vector<uint8_t>\n          描述: 字节序列\n        - 名称: stringPattern\n          类型: std::string\n          描述: 字符串模式\n        - 名称: caseSensitive\n          类型: bool\n          描述: 是否区分大小写\n        - 名称: name\n          类型: std::string\n          描述: 模式名称", "ScanStatus": "描述: 扫描状态结构体\n      字段:\n        - 名称: scanState\n          类型: ScanState\n          描述: 扫描状态\n        - 名称: progressPercent\n          类型: float\n          描述: 扫描进度百分比\n        - 名称: processesScanned\n          类型: int\n          描述: 已扫描进程数\n        - 名称: totalProcesses\n          类型: int\n          描述: 总进程数\n        - 名称: matchesFound\n          类型: int\n          描述: 已找到匹配项数", "ScanResults": "描述: 扫描结果结构体\n      字段:\n        - 名称: matches\n          类型: std::vector<MemoryMatch>\n          描述: 内存匹配列表\n        - 名称: totalMatches\n          类型: int\n          描述: 总匹配项数\n        - 名称: scanDuration\n          类型: uint64_t\n          描述: 扫描持续时间（毫秒）\n        - 名称: affectedProcesses\n          类型: std::set<pid_t>\n          描述: 受影响进程集合", "MemoryMatch": "描述: 内存匹配结构体\n      字段:\n        - 名称: pid\n          类型: pid_t\n          描述: 进程ID\n        - 名称: processName\n          类型: std::string\n          描述: 进程名称\n        - 名称: memoryAddress\n          类型: uintptr_t\n          描述: 内存地址\n        - 名称: memoryRegionType\n          类型: std::string\n          描述: 内存区域类型\n        - 名称: patternName\n          类型: std::string\n          描述: 匹配的模式名称\n        - 名称: patternType\n          类型: PatternType\n          描述: 模式类型\n        - 名称: matchData\n          类型: void*\n          描述: 匹配数据指针\n        - 名称: matchLength\n          类型: size_t\n          描述: 匹配长度", "MemoryAnalysisReport": "描述: 内存分析报告结构体\n      字段:\n        - 名称: potentialCredentials\n          类型: int\n          描述: 潜在凭据数量\n        - 名称: encryptedContent\n          类型: int\n          描述: 加密内容数量\n        - 名称: suspiciousRegions\n          类型: int\n          描述: 可疑区域数量\n        - 名称: analysisDetails\n          类型: std::vector<std::string>\n          描述: 分析详情"}}